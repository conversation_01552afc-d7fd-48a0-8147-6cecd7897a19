import Bid from '../models/bidModel.js';
import Project from '../models/projectModel.js';
import asyncHandler from '../utils/asyncHandler.js';
import { 
  calculateCompetitiveAnalysis, 
  calculateOptimizationScore 
} from '../utils/priceOptimization.js';

// @desc    Create a new bid
// @route   POST /api/bids
// @access  Private (Vendor)
export const createBid = asyncHandler(async (req, res) => {
  const { 
    project: projectId, 
    amount, 
    proposal, 
    deliveryTime, 
    attachments,
    milestones
  } = req.body;

  // Validate required fields
  if (!projectId || !amount || !proposal || !deliveryTime) {
    res.status(400);
    throw new Error('Please fill in all required fields');
  }

  // Ensure user is a vendor
  if (req.user.role !== 'vendor') {
    res.status(403);
    throw new Error('Only vendors can create bids');
  }

  // Check if project exists and is open
  const project = await Project.findById(projectId);
  if (!project) {
    res.status(404);
    throw new Error('Project not found');
  }

  if (project.status !== 'open') {
    res.status(400);
    throw new Error('Cannot bid on a project that is not open');
  }

  // Check if vendor already has a bid for this project
  const existingBid = await Bid.findOne({
    project: projectId,
    vendor: req.user._id,
    status: { $ne: 'withdrawn' }
  });

  if (existingBid) {
    res.status(400);
    throw new Error('You have already placed a bid on this project');
  }

  // Get all bids for this project for competitive analysis
  const allProjectBids = await Bid.find({ 
    project: projectId,
    status: { $ne: 'withdrawn' }
  });
  
  // Create bid
  const bid = await Bid.create({
    project: projectId,
    vendor: req.user._id,
    amount,
    proposal,
    deliveryTime,
    attachments: attachments || [],
    milestones: milestones || []
  });

  // Populate vendor information
  await bid.populate('vendor', 'name email company companyLogo rating');

  // Calculate competitive analysis
  try {
    const competitiveAnalysis = await calculateCompetitiveAnalysis(
      bid, 
      [...allProjectBids, bid]
    );
    
    bid.competitiveAnalysis = competitiveAnalysis;

    // Calculate optimization score
    const optimizationScore = await calculateOptimizationScore(
      bid,
      project,
      competitiveAnalysis
    );
    
    bid.optimizationScore = optimizationScore;
    await bid.save();
  } catch (error) {
    console.error('Bid analysis error:', error);
    // Don't fail bid creation if analysis fails
  }

  res.status(201).json(bid);
});

// @desc    Get all bids for a project
// @route   GET /api/bids/project/:projectId
// @access  Private (Project owner, Admin, or Bidder)
export const getProjectBids = asyncHandler(async (req, res) => {
  const { projectId } = req.params;
  
  // Check if project exists
  const project = await Project.findById(projectId);
  if (!project) {
    res.status(404);
    throw new Error('Project not found');
  }
  
  // Check authorization
  const isProjectOwner = project.client.toString() === req.user._id.toString();
  const isAdmin = req.user.role === 'admin';
  const isVendor = req.user.role === 'vendor';
  
  let bids;
  
  if (isProjectOwner || isAdmin) {
    // Project owner or admin can see all bids
    bids = await Bid.find({ project: projectId })
      .populate('vendor', 'name email company companyLogo rating')
      .sort({ createdAt: -1 });
  } else if (isVendor) {
    // Vendor can only see their own bids
    bids = await Bid.find({ 
      project: projectId,
      vendor: req.user._id 
    })
      .populate('vendor', 'name email company companyLogo rating')
      .sort({ createdAt: -1 });
      
    if (bids.length === 0) {
      res.status(403);
      throw new Error('Not authorized to view bids for this project');
    }
  } else {
    res.status(403);
    throw new Error('Not authorized to view bids for this project');
  }
  
  res.json(bids);
});

// @desc    Get bid by ID
// @route   GET /api/bids/:id
// @access  Private (Bid owner, Project owner, or Admin)
export const getBidById = asyncHandler(async (req, res) => {
  const bid = await Bid.findById(req.params.id)
    .populate('vendor', 'name email company companyLogo rating')
    .populate('project');
  
  if (!bid) {
    res.status(404);
    throw new Error('Bid not found');
  }
  
  // Check authorization
  const isVendor = bid.vendor._id.toString() === req.user._id.toString();
  const isProjectOwner = bid.project.client.toString() === req.user._id.toString();
  const isAdmin = req.user.role === 'admin';
  
  if (!isVendor && !isProjectOwner && !isAdmin) {
    res.status(403);
    throw new Error('Not authorized to view this bid');
  }
  
  res.json(bid);
});

// @desc    Update bid status (accept or reject)
// @route   PUT /api/bids/:id/status
// @access  Private (Project owner or Admin)
export const updateBidStatus = asyncHandler(async (req, res) => {
  const { status } = req.body;
  
  if (!status || !['pending', 'accepted', 'rejected'].includes(status)) {
    res.status(400);
    throw new Error('Please provide a valid status (pending, accepted, rejected)');
  }
  
  const bid = await Bid.findById(req.params.id);
  
  if (!bid) {
    res.status(404);
    throw new Error('Bid not found');
  }
  
  const project = await Project.findById(bid.project);
  
  if (!project) {
    res.status(404);
    throw new Error('Project not found');
  }
  
  // Check authorization
  const isProjectOwner = project.client.toString() === req.user._id.toString();
  const isAdmin = req.user.role === 'admin';
  
  if (!isProjectOwner && !isAdmin) {
    res.status(403);
    throw new Error('Not authorized to update bid status');
  }
  
  // If accepting the bid, reject all other bids
  if (status === 'accepted') {
    if (project.status !== 'open') {
      res.status(400);
      throw new Error('Cannot accept bid for a project that is not open');
    }
    
    // Reject all other bids
    await Bid.updateMany(
      { 
        project: project._id, 
        _id: { $ne: bid._id },
        status: 'pending'
      },
      { status: 'rejected' }
    );
    
    // Update project status and assigned vendor
    project.status = 'in-progress';
    project.assignedVendor = bid.vendor;
    project.winningBid = bid._id;
    await project.save();
  }
  
  // Update bid status
  bid.status = status;
  await bid.save();
  
  res.json(bid);
});

// @desc    Update a bid (vendor can update their own bid)
// @route   PUT /api/bids/:id
// @access  Private (Bid owner)
export const updateBid = asyncHandler(async (req, res) => {
  const bid = await Bid.findById(req.params.id);
  
  if (!bid) {
    res.status(404);
    throw new Error('Bid not found');
  }
  
  // Check ownership
  if (bid.vendor.toString() !== req.user._id.toString() && req.user.role !== 'admin') {
    res.status(403);
    throw new Error('Not authorized to update this bid');
  }
  
  // Only allow updates if the bid is still pending
  if (bid.status !== 'pending') {
    res.status(400);
    throw new Error('Cannot update a bid that has been accepted or rejected');
  }
  
  const {
    amount,
    proposal,
    deliveryTime,
    attachments,
    milestones
  } = req.body;
  
  // Update fields
  bid.amount = amount || bid.amount;
  bid.proposal = proposal || bid.proposal;
  bid.deliveryTime = deliveryTime || bid.deliveryTime;
  bid.attachments = attachments || bid.attachments;
  bid.milestones = milestones || bid.milestones;
  bid.revisions += 1;
  
  const updatedBid = await bid.save();
  
  // Recalculate competitive analysis and optimization score
  try {
    const allProjectBids = await Bid.find({ 
      project: bid.project,
      status: { $ne: 'withdrawn' }
    });
    
    const project = await Project.findById(bid.project);
    
    const competitiveAnalysis = await calculateCompetitiveAnalysis(
      updatedBid, 
      allProjectBids
    );
    
    updatedBid.competitiveAnalysis = competitiveAnalysis;
    
    const optimizationScore = await calculateOptimizationScore(
      updatedBid,
      project,
      competitiveAnalysis
    );
    
    updatedBid.optimizationScore = optimizationScore;
    await updatedBid.save();
  } catch (error) {
    console.error('Bid analysis error:', error);
  }
  
  res.json(updatedBid);
});

// @desc    Withdraw a bid
// @route   PUT /api/bids/:id/withdraw
// @access  Private (Bid owner)
export const withdrawBid = asyncHandler(async (req, res) => {
  const bid = await Bid.findById(req.params.id);
  
  if (!bid) {
    res.status(404);
    throw new Error('Bid not found');
  }
  
  // Check ownership
  if (bid.vendor.toString() !== req.user._id.toString() && req.user.role !== 'admin') {
    res.status(403);
    throw new Error('Not authorized to withdraw this bid');
  }
  
  // Only allow withdrawal if the bid is still pending
  if (bid.status !== 'pending') {
    res.status(400);
    throw new Error('Cannot withdraw a bid that has been accepted or rejected');
  }
  
  bid.status = 'withdrawn';
  await bid.save();
  
  res.json({ message: 'Bid withdrawn successfully', bid });
});

// @desc    Create a counter offer
// @route   POST /api/bids/:id/counter
// @access  Private (Project owner)
export const createCounterOffer = asyncHandler(async (req, res) => {
  const originalBid = await Bid.findById(req.params.id);
  
  if (!originalBid) {
    res.status(404);
    throw new Error('Bid not found');
  }
  
  const project = await Project.findById(originalBid.project);
  
  if (!project) {
    res.status(404);
    throw new Error('Project not found');
  }
  
  // Check if user is project owner
  if (project.client.toString() !== req.user._id.toString() && req.user.role !== 'admin') {
    res.status(403);
    throw new Error('Not authorized to create counter offer');
  }
  
  const { amount, deliveryTime, proposal } = req.body;
  
  if (!amount && !deliveryTime && !proposal) {
    res.status(400);
    throw new Error('Please provide at least one change for the counter offer');
  }
  
  // Create counter offer as a new bid
  const counterOffer = await Bid.create({
    project: originalBid.project,
    vendor: originalBid.vendor,
    amount: amount || originalBid.amount,
    proposal: proposal || originalBid.proposal,
    deliveryTime: deliveryTime || originalBid.deliveryTime,
    isCounterOffer: true,
    originalBid: originalBid._id,
    attachments: originalBid.attachments,
    milestones: originalBid.milestones
  });
  
  // Update original bid status to indicate there's a counter offer
  originalBid.status = 'rejected';
  await originalBid.save();
  
  res.status(201).json(counterOffer);
}); 