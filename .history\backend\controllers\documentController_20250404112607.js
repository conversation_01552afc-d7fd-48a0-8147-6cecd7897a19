import Document from '../models/documentModel.js';
import Project from '../models/projectModel.js';
import asyncHandler from '../utils/asyncHandler.js';
import { uploadFileToS3, deleteFileFromS3, getSignedUrl } from '../utils/s3Upload.js';
import * as rdhUtils from '../utils/rdhUtils.js';
import multer from 'multer';
import fs from 'fs';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';
import { fileURLToPath } from 'url';

// Configure multer for memory storage
const storage = multer.memoryStorage();
export const upload = multer({ 
  storage,
  limits: { fileSize: 10 * 1024 * 1024 } // 10MB limit
});

// @desc    Upload a new document
// @route   POST /api/documents
// @access  Private
export const uploadDocument = asyncHandler(async (req, res) => {
  const { 
    name, 
    description, 
    projectId, 
    isSecure 
  } = req.body;

  if (!req.file) {
    res.status(400);
    throw new Error('Please upload a file');
  }

  if (!name || !projectId) {
    res.status(400);
    throw new Error('Please provide a name and project ID');
  }

  // Check if project exists
  const project = await Project.findById(projectId);
  if (!project) {
    res.status(404);
    throw new Error('Project not found');
  }

  // Check authorization
  const isProjectOwner = project.client.toString() === req.user._id.toString();
  const isAssignedVendor = project.assignedVendor && 
                           project.assignedVendor.toString() === req.user._id.toString();
  const isAdmin = req.user.role === 'admin';

  if (!isProjectOwner && !isAssignedVendor && !isAdmin) {
    res.status(403);
    throw new Error('Not authorized to upload documents to this project');
  }

  // Process document for RDH if it's an image and secure flag is set
  let processedFile = req.file;
  let rdhInfo = {
    isEmbedded: false,
    embeddingMethod: null,
    psnrValue: 0,
    capacity: 0,
    watermarkData: '',
    extractedData: ''
  };

  // Apply RDH for supported file types if security is requested
  if (isSecure === 'true' && 
      ['image/jpeg', 'image/png', 'image/bmp'].includes(req.file.mimetype)) {
    try {
      // Generate a unique watermark containing document metadata
      const watermark = JSON.stringify({
        docId: uuidv4(),
        projectId,
        uploaderId: req.user._id.toString(),
        timestamp: new Date().toISOString(),
        name
      });

      // Convert watermark to binary
      const binaryWatermark = rdhUtils.textToBinary(watermark);

      // Process the image (simplified for example - in production this would process pixels)
      // This is a placeholder for the actual implementation of RDH
      // Simulate RDH processing with a 40dB PSNR (good quality)
      rdhInfo = {
        isEmbedded: true,
        embeddingMethod: 'histogram-shifting',
        psnrValue: 40.5,
        capacity: binaryWatermark.length,
        watermarkData: watermark,
        extractedData: ''
      };

      // In a real implementation, we would modify the image here
      // For now, we'll just use the original file
    } catch (error) {
      console.error('RDH processing error:', error);
      // Continue with the original file if RDH fails
    }
  }

  // Upload to S3
  const uploadResult = await uploadFileToS3(
    processedFile,
    `documents/${projectId}`
  );

  // Create document record
  const document = await Document.create({
    name,
    description,
    project: projectId,
    uploadedBy: req.user._id,
    fileUrl: uploadResult.url,
    fileType: req.file.mimetype,
    fileSize: req.file.size,
    isSecure: isSecure === 'true',
    rdhInfo,
    accessPermissions: [
      {
        user: req.user._id,
        permission: 'admin'
      },
      {
        user: project.client,
        permission: 'admin'
      }
    ]
  });

  // Add assigned vendor permission if exists
  if (project.assignedVendor) {
    document.accessPermissions.push({
      user: project.assignedVendor,
      permission: 'view'
    });
  }

  await document.save();

  res.status(201).json(document);
});

// @desc    Get all documents for a project
// @route   GET /api/documents/project/:projectId
// @access  Private
export const getProjectDocuments = asyncHandler(async (req, res) => {
  const { projectId } = req.params;
  
  // Check if project exists
  const project = await Project.findById(projectId);
  if (!project) {
    res.status(404);
    throw new Error('Project not found');
  }
  
  // Check authorization
  const isProjectOwner = project.client.toString() === req.user._id.toString();
  const isAssignedVendor = project.assignedVendor && 
                           project.assignedVendor.toString() === req.user._id.toString();
  const isAdmin = req.user.role === 'admin';
  
  if (!isProjectOwner && !isAssignedVendor && !isAdmin) {
    res.status(403);
    throw new Error('Not authorized to view documents for this project');
  }
  
  // Get documents
  const documents = await Document.find({ 
    project: projectId,
    isDeleted: false
  })
    .populate('uploadedBy', 'name email role')
    .sort({ createdAt: -1 });
  
  res.json(documents);
});

// @desc    Get document by ID
// @route   GET /api/documents/:id
// @access  Private
export const getDocumentById = asyncHandler(async (req, res) => {
  const document = await Document.findById(req.params.id)
    .populate('uploadedBy', 'name email role')
    .populate('project');
  
  if (!document || document.isDeleted) {
    res.status(404);
    throw new Error('Document not found');
  }
  
  // Check authorization
  const hasAccess = document.accessPermissions.some(permission => 
    permission.user.toString() === req.user._id.toString()
  );
  const isAdmin = req.user.role === 'admin';
  
  if (!hasAccess && !isAdmin) {
    res.status(403);
    throw new Error('Not authorized to view this document');
  }
  
  // Generate signed URL if needed
  if (!document.fileUrl.startsWith('http')) {
    document.fileUrl = getSignedUrl(document.fileUrl);
  }
  
  res.json(document);
});

// @desc    Extract hidden data from secure document
// @route   POST /api/documents/:id/extract
// @access  Private
export const extractDocumentData = asyncHandler(async (req, res) => {
  const document = await Document.findById(req.params.id);
  
  if (!document || document.isDeleted) {
    res.status(404);
    throw new Error('Document not found');
  }
  
  // Check if document has RDH
  if (!document.rdhInfo.isEmbedded) {
    res.status(400);
    throw new Error('This document does not contain embedded data');
  }
  
  // Check authorization - only admins and document owners can extract data
  const isOwner = document.uploadedBy.toString() === req.user._id.toString();
  const isAdmin = req.user.role === 'admin';
  
  if (!isOwner && !isAdmin) {
    res.status(403);
    throw new Error('Not authorized to extract data from this document');
  }
  
  // In a real implementation, we would download the file, process it, and extract the data
  // For this example, we'll just return the watermark data that was embedded
  
  res.json({
    extractedData: document.rdhInfo.watermarkData,
    document
  });
});

// @desc    Update document access permissions
// @route   PUT /api/documents/:id/permissions
// @access  Private
export const updateDocumentPermissions = asyncHandler(async (req, res) => {
  const { accessPermissions } = req.body;
  
  if (!accessPermissions || !Array.isArray(accessPermissions)) {
    res.status(400);
    throw new Error('Please provide valid access permissions');
  }
  
  const document = await Document.findById(req.params.id);
  
  if (!document || document.isDeleted) {
    res.status(404);
    throw new Error('Document not found');
  }
  
  // Check authorization - only admins and document owners can update permissions
  const isOwner = document.uploadedBy.toString() === req.user._id.toString();
  const isAdmin = req.user.role === 'admin';
  const isProjectOwner = document.project.client && 
                         document.project.client.toString() === req.user._id.toString();
  
  if (!isOwner && !isAdmin && !isProjectOwner) {
    res.status(403);
    throw new Error('Not authorized to update permissions for this document');
  }
  
  document.accessPermissions = accessPermissions;
  await document.save();
  
  res.json(document);
});

// @desc    Delete document (soft delete)
// @route   DELETE /api/documents/:id
// @access  Private
export const deleteDocument = asyncHandler(async (req, res) => {
  const document = await Document.findById(req.params.id);
  
  if (!document) {
    res.status(404);
    throw new Error('Document not found');
  }
  
  // Check authorization - only admins and document owners can delete
  const isOwner = document.uploadedBy.toString() === req.user._id.toString();
  const isAdmin = req.user.role === 'admin';
  const isProjectOwner = document.project.client && 
                         document.project.client.toString() === req.user._id.toString();
  
  if (!isOwner && !isAdmin && !isProjectOwner) {
    res.status(403);
    throw new Error('Not authorized to delete this document');
  }
  
  // Soft delete
  document.isDeleted = true;
  await document.save();
  
  res.json({ message: 'Document deleted successfully' });
});

// @desc    Update document version
// @route   PUT /api/documents/:id/version
// @access  Private
export const updateDocumentVersion = asyncHandler(async (req, res) => {
  if (!req.file) {
    res.status(400);
    throw new Error('Please upload a file');
  }
  
  const document = await Document.findById(req.params.id);
  
  if (!document || document.isDeleted) {
    res.status(404);
    throw new Error('Document not found');
  }
  
  // Check authorization
  const hasEditAccess = document.accessPermissions.some(permission => 
    permission.user.toString() === req.user._id.toString() && 
    ['edit', 'admin'].includes(permission.permission)
  );
  const isAdmin = req.user.role === 'admin';
  
  if (!hasEditAccess && !isAdmin) {
    res.status(403);
    throw new Error('Not authorized to update this document');
  }
  
  // Store previous version
  document.previousVersions.push({
    version: document.version,
    fileUrl: document.fileUrl,
    updatedAt: new Date(),
    updatedBy: req.user._id
  });
  
  // Upload new version to S3
  const uploadResult = await uploadFileToS3(
    req.file,
    `documents/${document.project}`
  );
  
  // Update document record
  document.fileUrl = uploadResult.url;
  document.fileType = req.file.mimetype;
  document.fileSize = req.file.size;
  document.version += 1;
  
  await document.save();
  
  res.json(document);
}); 