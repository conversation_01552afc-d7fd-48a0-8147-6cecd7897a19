import Message from '../models/messageModel.js';
import User from '../models/userModel.js';
import Project from '../models/projectModel.js';
import asyncHandler from '../utils/asyncHandler.js';

// @desc    Get messages for a project
// @route   GET /api/messages/project/:projectId
// @access  Private
export const getProjectMessages = asyncHandler(async (req, res) => {
  const { projectId } = req.params;
  const page = parseInt(req.query.page) || 1;
  const limit = parseInt(req.query.limit) || 50;
  
  // Check if project exists
  const project = await Project.findById(projectId);
  if (!project) {
    res.status(404);
    throw new Error('Project not found');
  }
  
  // Check authorization
  const isProjectOwner = project.client.toString() === req.user._id.toString();
  const isAssignedVendor = project.assignedVendor && 
                          project.assignedVendor.toString() === req.user._id.toString();
  const isAdmin = req.user.role === 'admin';
  
  if (!isProjectOwner && !isAssignedVendor && !isAdmin) {
    res.status(403);
    throw new Error('Not authorized to view messages for this project');
  }
  
  // Get messages
  const messages = await Message.find({ 
    project: projectId,
    isDeleted: false
  })
    .populate('sender', 'name email role company companyLogo')
    .sort({ createdAt: -1 })
    .skip((page - 1) * limit)
    .limit(limit);
  
  // Get total count
  const total = await Message.countDocuments({ 
    project: projectId,
    isDeleted: false
  });
  
  // Mark messages as read
  await Message.updateMany(
    { 
      project: projectId,
      sender: { $ne: req.user._id },
      isRead: false
    },
    { 
      isRead: true,
      readAt: new Date()
    }
  );
  
  res.json({
    messages: messages.reverse(),
    page,
    pages: Math.ceil(total / limit),
    total
  });
});

// @desc    Get direct messages between users
// @route   GET /api/messages/user/:userId
// @access  Private
export const getDirectMessages = asyncHandler(async (req, res) => {
  const { userId } = req.params;
  const page = parseInt(req.query.page) || 1;
  const limit = parseInt(req.query.limit) || 50;
  
  // Check if user exists
  const otherUser = await User.findById(userId);
  if (!otherUser) {
    res.status(404);
    throw new Error('User not found');
  }
  
  // Get messages between current user and the specified user
  const messages = await Message.find({
    $or: [
      { sender: req.user._id, recipient: userId, project: null },
      { sender: userId, recipient: req.user._id, project: null }
    ],
    isDeleted: false
  })
    .populate('sender', 'name email role company companyLogo')
    .sort({ createdAt: -1 })
    .skip((page - 1) * limit)
    .limit(limit);
  
  // Get total count
  const total = await Message.countDocuments({
    $or: [
      { sender: req.user._id, recipient: userId, project: null },
      { sender: userId, recipient: req.user._id, project: null }
    ],
    isDeleted: false
  });
  
  // Mark messages as read
  await Message.updateMany(
    { 
      sender: userId, 
      recipient: req.user._id,
      project: null,
      isRead: false
    },
    { 
      isRead: true,
      readAt: new Date()
    }
  );
  
  // Generate chat room ID (for consistency with socket.io)
  const chatRoom = `dm:${[req.user._id, userId].sort().join('_')}`;
  
  res.json({
    messages: messages.reverse(),
    page,
    pages: Math.ceil(total / limit),
    total,
    chatRoom
  });
});

// @desc    Send a message
// @route   POST /api/messages
// @access  Private
export const sendMessage = asyncHandler(async (req, res) => {
  const { content, recipient, project, attachments, replyTo, messageType } = req.body;
  
  // Validate required fields
  if (!content) {
    res.status(400);
    throw new Error('Message content is required');
  }
  
  if (!recipient && !project) {
    res.status(400);
    throw new Error('Recipient or project is required');
  }
  
  // Validate recipient if provided
  if (recipient) {
    const recipientUser = await User.findById(recipient);
    if (!recipientUser) {
      res.status(404);
      throw new Error('Recipient not found');
    }
  }
  
  // Validate project if provided
  if (project) {
    const projectObj = await Project.findById(project);
    if (!projectObj) {
      res.status(404);
      throw new Error('Project not found');
    }
    
    // Check authorization
    const isProjectOwner = projectObj.client.toString() === req.user._id.toString();
    const isAssignedVendor = projectObj.assignedVendor && 
                            projectObj.assignedVendor.toString() === req.user._id.toString();
    const isAdmin = req.user.role === 'admin';
    
    if (!isProjectOwner && !isAssignedVendor && !isAdmin) {
      res.status(403);
      throw new Error('Not authorized to send messages to this project');
    }
  }
  
  // Determine chat room
  const chatRoom = project 
    ? `project:${project}` 
    : `dm:${[req.user._id, recipient].sort().join('_')}`;
  
  // Create message
  const message = await Message.create({
    sender: req.user._id,
    recipient,
    project,
    content,
    attachments: attachments || [],
    replyTo,
    messageType: messageType || 'text',
    chatRoom
  });
  
  // Populate sender info
  await message.populate('sender', 'name email role company companyLogo');
  
  // In a real app, we would emit a Socket.IO event here,
  // but that's handled by the socket.io handlers
  
  res.status(201).json(message);
});

// @desc    Mark messages as read
// @route   PUT /api/messages/read
// @access  Private
export const markMessagesAsRead = asyncHandler(async (req, res) => {
  const { messageIds } = req.body;
  
  if (!messageIds || !Array.isArray(messageIds) || messageIds.length === 0) {
    res.status(400);
    throw new Error('Please provide message IDs to mark as read');
  }
  
  // Update messages
  const result = await Message.updateMany(
    { 
      _id: { $in: messageIds },
      sender: { $ne: req.user._id },
      isRead: false
    },
    { 
      isRead: true,
      readAt: new Date()
    }
  );
  
  res.json({ 
    success: true, 
    markedCount: result.nModified || 0
  });
});

// @desc    Get unread message count
// @route   GET /api/messages/unread
// @access  Private
export const getUnreadCount = asyncHandler(async (req, res) => {
  // Get total unread direct messages
  const dmCount = await Message.countDocuments({
    recipient: req.user._id,
    project: null,
    isRead: false,
    isDeleted: false
  });
  
  // Get projects the user is involved in
  const userProjects = await Project.find({
    $or: [
      { client: req.user._id },
      { assignedVendor: req.user._id }
    ]
  }).select('_id');
  
  const projectIds = userProjects.map(p => p._id);
  
  // Get total unread project messages
  const projectCount = await Message.countDocuments({
    project: { $in: projectIds },
    sender: { $ne: req.user._id },
    isRead: false,
    isDeleted: false
  });
  
  // Get unread counts by project
  const projectCounts = await Message.aggregate([
    { 
      $match: { 
        project: { $in: projectIds },
        sender: { $ne: req.user._id },
        isRead: false,
        isDeleted: false
      } 
    },
    { 
      $group: { 
        _id: '$project', 
        count: { $sum: 1 } 
      } 
    }
  ]);
  
  // Get unread counts by user
  const userCounts = await Message.aggregate([
    { 
      $match: { 
        recipient: req.user._id,
        project: null,
        isRead: false,
        isDeleted: false
      } 
    },
    { 
      $group: { 
        _id: '$sender', 
        count: { $sum: 1 } 
      } 
    }
  ]);
  
  res.json({
    total: dmCount + projectCount,
    directMessages: {
      total: dmCount,
      bySender: userCounts
    },
    projectMessages: {
      total: projectCount,
      byProject: projectCounts
    }
  });
});

// @desc    Delete a message
// @route   DELETE /api/messages/:id
// @access  Private
export const deleteMessage = asyncHandler(async (req, res) => {
  const message = await Message.findById(req.params.id);
  
  if (!message) {
    res.status(404);
    throw new Error('Message not found');
  }
  
  // Check ownership
  if (message.sender.toString() !== req.user._id.toString() && req.user.role !== 'admin') {
    res.status(403);
    throw new Error('Not authorized to delete this message');
  }
  
  // Soft delete
  message.isDeleted = true;
  await message.save();
  
  res.json({ message: 'Message deleted successfully' });
}); 