import User from '../models/userModel.js';
import Project from '../models/projectModel.js';
import Bid from '../models/bidModel.js';
import asyncHandler from '../utils/asyncHandler.js';
import { uploadFileToS3 } from '../utils/s3Upload.js';

// @desc    Get all users (with pagination and filtering)
// @route   GET /api/users
// @access  Private (Admin)
export const getUsers = asyncHandler(async (req, res) => {
  // Check if user is admin
  if (req.user.role !== 'admin') {
    res.status(403);
    throw new Error('Not authorized to access user list');
  }
  
  const pageSize = parseInt(req.query.limit) || 10;
  const page = parseInt(req.query.page) || 1;
  
  // Build filter object
  const filter = {};
  
  // Filter by role
  if (req.query.role) {
    filter.role = req.query.role;
  }
  
  // Search by name or email
  if (req.query.search) {
    filter.$or = [
      { name: { $regex: req.query.search, $options: 'i' } },
      { email: { $regex: req.query.search, $options: 'i' } }
    ];
  }
  
  const count = await User.countDocuments(filter);
  
  const users = await User.find(filter)
    .select('-password')
    .sort({ createdAt: -1 })
    .skip(pageSize * (page - 1))
    .limit(pageSize);
  
  res.json({
    users,
    page,
    pages: Math.ceil(count / pageSize),
    total: count
  });
});

// @desc    Get user by ID
// @route   GET /api/users/:id
// @access  Private
export const getUserById = asyncHandler(async (req, res) => {
  const user = await User.findById(req.params.id).select('-password');
  
  if (!user) {
    res.status(404);
    throw new Error('User not found');
  }
  
  // Get additional data for the user based on their role
  if (user.role === 'client') {
    // Get projects created by the client
    const projects = await Project.find({ client: user._id })
      .sort({ createdAt: -1 })
      .limit(5);
    
    res.json({
      ...user._doc,
      projects
    });
  } else if (user.role === 'vendor') {
    // Get bids submitted by the vendor
    const bids = await Bid.find({ vendor: user._id })
      .populate('project', 'title budget status')
      .sort({ createdAt: -1 })
      .limit(5);
    
    // Get projects assigned to the vendor
    const assignedProjects = await Project.find({ assignedVendor: user._id })
      .sort({ createdAt: -1 });
    
    // Calculate success rate
    const bidCount = await Bid.countDocuments({ vendor: user._id });
    const acceptedBidCount = await Bid.countDocuments({ 
      vendor: user._id,
      status: 'accepted'
    });
    
    const successRate = bidCount > 0 
      ? (acceptedBidCount / bidCount) * 100 
      : 0;
    
    res.json({
      ...user._doc,
      bids,
      assignedProjects,
      stats: {
        totalProjects: assignedProjects.length,
        totalBids: bidCount,
        acceptedBids: acceptedBidCount,
        successRate
      }
    });
  } else {
    res.json(user);
  }
});

// @desc    Update user
// @route   PUT /api/users/:id
// @access  Private (Admin or Self)
export const updateUser = asyncHandler(async (req, res) => {
  const user = await User.findById(req.params.id);
  
  if (!user) {
    res.status(404);
    throw new Error('User not found');
  }
  
  // Check authorization
  if (user._id.toString() !== req.user._id.toString() && req.user.role !== 'admin') {
    res.status(403);
    throw new Error('Not authorized to update this user');
  }
  
  // Update fields
  user.name = req.body.name || user.name;
  user.email = req.body.email || user.email;
  user.role = req.body.role || user.role;
  user.company = req.body.company || user.company;
  user.bio = req.body.bio || user.bio;
  user.location = req.body.location || user.location;
  user.website = req.body.website || user.website;
  user.skills = req.body.skills || user.skills;
  
  // Only admin can modify ratings directly
  if (req.body.rating && req.user.role === 'admin') {
    user.rating = req.body.rating;
  }
  
  // Check if password should be updated
  if (req.body.password) {
    user.password = req.body.password;
  }
  
  const updatedUser = await user.save();
  
  res.json({
    _id: updatedUser._id,
    name: updatedUser.name,
    email: updatedUser.email,
    role: updatedUser.role,
    company: updatedUser.company,
    companyLogo: updatedUser.companyLogo,
    bio: updatedUser.bio,
    location: updatedUser.location,
    website: updatedUser.website,
    skills: updatedUser.skills,
    rating: updatedUser.rating
  });
});

// @desc    Upload company logo
// @route   POST /api/users/:id/logo
// @access  Private (Self or Admin)
export const uploadCompanyLogo = asyncHandler(async (req, res) => {
  if (!req.file) {
    res.status(400);
    throw new Error('Please upload an image file');
  }
  
  const user = await User.findById(req.params.id);
  
  if (!user) {
    res.status(404);
    throw new Error('User not found');
  }
  
  // Check authorization
  if (user._id.toString() !== req.user._id.toString() && req.user.role !== 'admin') {
    res.status(403);
    throw new Error('Not authorized to update this user');
  }
  
  // Upload logo to S3
  const uploadResult = await uploadFileToS3(
    req.file,
    'logos'
  );
  
  // Update user with logo URL
  user.companyLogo = uploadResult.url;
  await user.save();
  
  res.json({
    _id: user._id,
    companyLogo: user.companyLogo,
    message: 'Company logo uploaded successfully'
  });
});

// @desc    Search for vendors by skills
// @route   GET /api/users/vendors/search
// @access  Private
export const searchVendors = asyncHandler(async (req, res) => {
  const { skills, category } = req.query;
  
  const filter = { role: 'vendor' };
  
  // Filter by skills
  if (skills) {
    const skillsArray = skills.split(',');
    filter.skills = { $in: skillsArray };
  }
  
  // Get vendors
  const vendors = await User.find(filter)
    .select('name email company companyLogo skills rating location')
    .sort({ rating: -1 });
  
  // If category is provided, also get vendors who have worked in that category
  if (category) {
    // Find projects in the category
    const projectsInCategory = await Project.find({ 
      category,
      status: 'completed',
      assignedVendor: { $exists: true }
    }).select('assignedVendor');
    
    // Get vendor IDs from those projects
    const vendorIds = projectsInCategory.map(p => p.assignedVendor);
    
    // Find vendors with experience in the category
    const vendorsWithExperience = await User.find({
      role: 'vendor',
      _id: { $in: vendorIds }
    })
      .select('name email company companyLogo skills rating location');
    
    // Combine results, removing duplicates
    const allVendorIds = new Set(vendors.map(v => v._id.toString()));
    
    vendorsWithExperience.forEach(vendor => {
      if (!allVendorIds.has(vendor._id.toString())) {
        vendors.push(vendor);
        allVendorIds.add(vendor._id.toString());
      }
    });
  }
  
  res.json(vendors);
});

// @desc    Delete user
// @route   DELETE /api/users/:id
// @access  Private (Admin only)
export const deleteUser = asyncHandler(async (req, res) => {
  // Check if user is admin
  if (req.user.role !== 'admin') {
    res.status(403);
    throw new Error('Not authorized to delete users');
  }
  
  const user = await User.findById(req.params.id);
  
  if (!user) {
    res.status(404);
    throw new Error('User not found');
  }
  
  await user.remove();
  
  res.json({ message: 'User removed' });
}); 