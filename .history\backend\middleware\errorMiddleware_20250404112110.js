// Handle 404 not found errors
export const notFound = (req, res, next) => {
  const error = new Error(`Not Found - ${req.originalUrl}`);
  res.status(404);
  next(error);
};

// Custom error handler
export const errorHandler = (err, req, res, next) => {
  // Sometimes the status code is still 200, so we set it to 500 if that's the case
  const statusCode = res.statusCode === 200 ? 500 : res.statusCode;
  
  // Set status code
  res.status(statusCode);
  
  // Handle MongoDB validation errors
  if (err.name === 'ValidationError') {
    const messages = Object.values(err.errors).map(val => val.message);
    return res.json({
      success: false,
      message: 'Validation Error',
      errors: messages
    });
  }
  
  // Handle MongoDB duplicate key error
  if (err.code === 11000) {
    return res.json({
      success: false,
      message: 'Duplicate field value entered',
      errors: [
        `Duplicate field: ${Object.keys(err.keyValue)} already exists with value: ${Object.values(err.keyValue)}`
      ]
    });
  }
  
  // Handle JWT errors
  if (err.name === 'JsonWebTokenError') {
    return res.json({
      success: false,
      message: 'Invalid token',
      errors: ['Your session has expired. Please login again.']
    });
  }
  
  // Send JSON response with error message and stack trace in development
  res.json({
    success: false,
    message: err.message || 'Server Error',
    errors: [err.message],
    stack: process.env.NODE_ENV === 'production' ? null : err.stack
  });
}; 