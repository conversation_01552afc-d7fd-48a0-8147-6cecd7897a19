import mongoose from 'mongoose';

const documentSchema = mongoose.Schema(
  {
    name: {
      type: String,
      required: [true, 'Please add a document name'],
      trim: true
    },
    description: {
      type: String
    },
    project: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Project',
      required: true
    },
    uploadedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    fileUrl: {
      type: String,
      required: [true, 'Please add a file URL']
    },
    fileType: {
      type: String,
      required: [true, 'Please add a file type']
    },
    fileSize: {
      type: Number,
      required: [true, 'Please add a file size']
    },
    isSecure: {
      type: Boolean,
      default: true
    },
    version: {
      type: Number,
      default: 1
    },
    previousVersions: [
      {
        version: Number,
        fileUrl: String,
        updatedAt: Date,
        updatedBy: {
          type: mongoose.Schema.Types.ObjectId,
          ref: 'User'
        }
      }
    ],
    rdhInfo: {
      isEmbedded: {
        type: Boolean,
        default: false
      },
      embeddingMethod: {
        type: String,
        enum: ['histogram-shifting', 'difference-expansion', 'pixel-value-ordering', null],
        default: null
      },
      psnrValue: {
        type: Number,
        default: 0
      },
      capacity: {
        type: Number,
        default: 0
      },
      watermarkData: {
        type: String,
        default: ''
      },
      extractedData: {
        type: String,
        default: ''
      }
    },
    accessPermissions: [
      {
        user: {
          type: mongoose.Schema.Types.ObjectId,
          ref: 'User'
        },
        permission: {
          type: String,
          enum: ['view', 'edit', 'admin'],
          default: 'view'
        }
      }
    ],
    isDeleted: {
      type: Boolean,
      default: false
    }
  },
  {
    timestamps: true
  }
);

// Create index for efficient document retrieval by project
documentSchema.index({ project: 1 });

// Create index for searching by name
documentSchema.index({ name: 'text', description: 'text' });

const Document = mongoose.model('Document', documentSchema);

export default Document; 