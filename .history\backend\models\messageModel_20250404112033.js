import mongoose from 'mongoose';

const messageSchema = mongoose.Schema(
  {
    sender: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    recipient: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    project: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Project'
    },
    content: {
      type: String,
      required: [true, 'Message content is required']
    },
    attachments: [
      {
        name: String,
        url: String,
        type: String,
        size: Number
      }
    ],
    isRead: {
      type: Boolean,
      default: false
    },
    readAt: {
      type: Date
    },
    isEdited: {
      type: Boolean,
      default: false
    },
    isDeleted: {
      type: Boolean,
      default: false
    },
    messageType: {
      type: String,
      enum: ['text', 'file', 'system'],
      default: 'text'
    },
    replyTo: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Message'
    },
    chatRoom: {
      type: String,
      required: true
    }
  },
  {
    timestamps: true
  }
);

// Create indexes for efficient message retrieval
messageSchema.index({ chatRoom: 1, createdAt: -1 });
messageSchema.index({ sender: 1, recipient: 1, createdAt: -1 });
messageSchema.index({ project: 1, createdAt: -1 });

const Message = mongoose.model('Message', messageSchema);

export default Message; 