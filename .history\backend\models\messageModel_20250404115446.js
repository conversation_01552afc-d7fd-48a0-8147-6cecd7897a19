import mongoose from 'mongoose';

// Message Schema
const messageSchema = new mongoose.Schema({
  conversation: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Conversation',
    required: true
  },
  sender: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  content: {
    type: String,
    required: true,
    trim: true
  },
  attachments: [{
    fileKey: String,
    fileName: String,
    fileType: String,
    fileSize: Number
  }],
  readBy: [{
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    readAt: {
      type: Date,
      default: Date.now
    }
  }],
  deletedFor: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }],
  metadata: {
    type: Object,
    default: {}
  }
}, {
  timestamps: true
});

// Create indexes for faster queries
messageSchema.index({ conversation: 1, createdAt: -1 });
messageSchema.index({ sender: 1 });
messageSchema.index({ 'readBy.user': 1 });
messageSchema.index({ deletedFor: 1 });

// Conversation Schema
const conversationSchema = new mongoose.Schema({
  participants: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  }],
  project: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Project'
  },
  title: {
    type: String,
    trim: true
  },
  lastMessage: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Message'
  },
  type: {
    type: String,
    enum: ['direct', 'group', 'project'],
    default: 'direct'
  },
  unreadCount: {
    type: Map,
    of: Number,
    default: {}
  },
  isActive: {
    type: Boolean,
    default: true
  },
  metadata: {
    type: Object,
    default: {}
  }
}, {
  timestamps: true
});

// Create indexes for faster queries
conversationSchema.index({ participants: 1 });
conversationSchema.index({ project: 1 });
conversationSchema.index({ isActive: 1 });

// Static method to get unread messages count for a user
conversationSchema.statics.getUnreadCountForUser = async function(userId) {
  const conversations = await this.find({ 
    participants: userId,
    isActive: true
  });
  
  let totalUnread = 0;
  
  for (const conversation of conversations) {
    totalUnread += conversation.unreadCount.get(userId.toString()) || 0;
  }
  
  return totalUnread;
};

// Method to update unread counts after a new message
conversationSchema.methods.updateUnreadCounts = async function(senderId, messageId) {
  for (const participantId of this.participants) {
    // Don't increase unread count for the sender
    if (participantId.toString() !== senderId.toString()) {
      const currentCount = this.unreadCount.get(participantId.toString()) || 0;
      this.unreadCount.set(participantId.toString(), currentCount + 1);
    }
  }
  
  // Update the last message
  this.lastMessage = messageId;
  
  await this.save();
};

// Method to reset unread count for a user
conversationSchema.methods.resetUnreadCountForUser = async function(userId) {
  this.unreadCount.set(userId.toString(), 0);
  await this.save();
};

const Message = mongoose.model('Message', messageSchema);
const Conversation = mongoose.model('Conversation', conversationSchema);

export { Message, Conversation }; 