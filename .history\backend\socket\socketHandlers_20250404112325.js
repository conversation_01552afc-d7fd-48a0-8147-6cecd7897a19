import Message from '../models/messageModel.js';
import User from '../models/userModel.js';
import jwt from 'jsonwebtoken';

// Middleware to authenticate socket connections
const authenticateSocket = async (socket, next) => {
  try {
    const token = socket.handshake.auth.token;

    if (!token) {
      return next(new Error('Authentication error: No token provided'));
    }

    // Verify token
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    const user = await User.findById(decoded.id).select('-password');

    if (!user) {
      return next(new Error('Authentication error: User not found'));
    }

    // Attach user to socket
    socket.user = user;
    next();
  } catch (error) {
    console.error('Socket authentication error:', error);
    next(new Error('Authentication error: Invalid token'));
  }
};

// Map to store active user connections
const userSockets = new Map();

// Handle socket connections
export const setupSocketHandlers = (io) => {
  // Apply authentication middleware
  io.use(authenticateSocket);

  io.on('connection', (socket) => {
    console.log(`User connected: ${socket.user.name} (${socket.user._id})`);
    
    // Store user socket for direct messaging
    if (!userSockets.has(socket.user._id.toString())) {
      userSockets.set(socket.user._id.toString(), new Set());
    }
    userSockets.get(socket.user._id.toString()).add(socket.id);
    
    // Send online status to user's contacts
    io.emit('user_status', {
      userId: socket.user._id,
      status: 'online',
      timestamp: new Date()
    });
    
    // Join personal room for direct messages
    socket.join(`user:${socket.user._id}`);
    
    // Handle joining project rooms
    socket.on('join_project', (projectId) => {
      if (!projectId) return;
      
      socket.join(`project:${projectId}`);
      console.log(`${socket.user.name} joined project room: ${projectId}`);
      
      // Notify others in the room
      socket.to(`project:${projectId}`).emit('user_joined_project', {
        userId: socket.user._id,
        userName: socket.user.name,
        projectId
      });
    });
    
    // Handle leaving project rooms
    socket.on('leave_project', (projectId) => {
      if (!projectId) return;
      
      socket.leave(`project:${projectId}`);
      console.log(`${socket.user.name} left project room: ${projectId}`);
    });
    
    // Handle chat messages
    socket.on('send_message', async (messageData) => {
      try {
        const { content, recipient, project, attachments, replyTo, messageType } = messageData;
        
        // Validate required fields
        if (!content) {
          socket.emit('message_error', { error: 'Message content is required' });
          return;
        }
        
        if (!recipient && !project) {
          socket.emit('message_error', { error: 'Recipient or project is required' });
          return;
        }
        
        // Create a message object
        const newMessage = new Message({
          sender: socket.user._id,
          recipient,
          project,
          content,
          attachments: attachments || [],
          replyTo,
          messageType: messageType || 'text',
          chatRoom: project ? `project:${project}` : `dm:${socket.user._id}_${recipient}`
        });
        
        // Save to database
        await newMessage.save();
        
        // Populate sender info
        await newMessage.populate('sender', 'name email role company companyLogo');
        
        // Emit to the appropriate room
        if (project) {
          io.to(`project:${project}`).emit('new_message', newMessage);
        } else {
          // For direct messages, emit to both sender and recipient
          io.to(`user:${socket.user._id}`).to(`user:${recipient}`).emit('new_message', newMessage);
          
          // Send notification to recipient if they're not in the chat
          const recipientSockets = userSockets.get(recipient);
          if (recipientSockets && recipientSockets.size > 0) {
            io.to(Array.from(recipientSockets)).emit('message_notification', {
              message: newMessage,
              sender: {
                _id: socket.user._id,
                name: socket.user.name
              }
            });
          }
        }
      } catch (error) {
        console.error('Message send error:', error);
        socket.emit('message_error', { error: 'Failed to send message' });
      }
    });
    
    // Handle real-time bid notifications
    socket.on('new_bid', (bidData) => {
      const { projectId, bidId } = bidData;
      
      if (!projectId || !bidId) {
        socket.emit('bid_error', { error: 'Project ID and Bid ID are required' });
        return;
      }
      
      // Notify all users in the project room
      io.to(`project:${projectId}`).emit('bid_notification', {
        bidId,
        projectId,
        message: `New bid received from ${socket.user.name}`,
        timestamp: new Date()
      });
    });
    
    // Handle bid status updates
    socket.on('bid_status_changed', (data) => {
      const { bidId, projectId, status, vendorId } = data;
      
      // Broadcast to project room and vendor
      io.to(`project:${projectId}`).to(`user:${vendorId}`).emit('bid_update', {
        bidId,
        projectId,
        status,
        updatedBy: socket.user._id,
        timestamp: new Date()
      });
    });
    
    // Handle typing indicators
    socket.on('typing', (data) => {
      const { chatRoom, isTyping } = data;
      
      if (!chatRoom) return;
      
      socket.to(chatRoom).emit('typing_indicator', {
        userId: socket.user._id,
        userName: socket.user.name,
        isTyping,
        timestamp: new Date()
      });
    });
    
    // Handle document access notifications
    socket.on('document_accessed', (data) => {
      const { documentId, projectId } = data;
      
      if (!documentId || !projectId) return;
      
      socket.to(`project:${projectId}`).emit('document_activity', {
        documentId,
        userId: socket.user._id,
        userName: socket.user.name,
        action: 'accessed',
        timestamp: new Date()
      });
    });
    
    // Handle user disconnect
    socket.on('disconnect', () => {
      console.log(`User disconnected: ${socket.user.name}`);
      
      // Remove from active users map
      const userSocketsSet = userSockets.get(socket.user._id.toString());
      if (userSocketsSet) {
        userSocketsSet.delete(socket.id);
        if (userSocketsSet.size === 0) {
          userSockets.delete(socket.user._id.toString());
          
          // Notify contacts that user is offline
          io.emit('user_status', {
            userId: socket.user._id,
            status: 'offline',
            timestamp: new Date()
          });
        }
      }
    });
  });
}; 