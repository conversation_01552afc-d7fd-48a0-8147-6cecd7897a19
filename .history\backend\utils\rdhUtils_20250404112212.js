/**
 * Utility functions for Reversible Data Hiding (RDH) in images and documents
 * Implements histogram shifting technique for data embedding
 */

// Helper to calculate PSNR (Peak Signal-to-Noise Ratio) to measure image quality
export const calculatePSNR = (originalArray, modifiedArray) => {
  if (originalArray.length !== modifiedArray.length) {
    throw new Error('Arrays must have the same dimensions');
  }
  
  let mse = 0;
  const n = originalArray.length;
  
  for (let i = 0; i < n; i++) {
    mse += Math.pow(originalArray[i] - modifiedArray[i], 2);
  }
  
  mse /= n;
  
  if (mse === 0) return Infinity;
  
  // For 8-bit images, max value is 255
  const maxValue = 255;
  const psnr = 10 * Math.log10(Math.pow(maxValue, 2) / mse);
  
  return psnr;
};

// Helper to find optimal delta for histogram shifting
export const findOptimalDelta = (imageData, minVal, maxVal) => {
  let maxCapacity = 0;
  let optimalDelta = minVal;
  let optimalMap = [];
  let optimalPeak = 0;
  
  for (let delta = minVal; delta < maxVal; delta++) {
    // Create mapping
    const map = new Array(imageData.width).fill(0);
    const used = new Array(imageData.width).fill(false);
    
    map[0] = 0;
    used[0] = true;
    
    for (let i = 1; i < map.length; i++) {
      map[i] = (map[i - 1] + delta) % imageData.width;
      if (used[map[i]]) {
        map[i] = (map[i] + 1) % imageData.width;
      }
      used[map[i]] = true;
    }
    
    // Calculate differences
    const differences = [];
    for (let i = 0; i < imageData.width / 2; i++) {
      const x = 2 * i;
      const y = x + 1;
      
      for (let j = 0; j < imageData.height; j++) {
        const idx1 = j * imageData.width + map[x];
        const idx2 = j * imageData.width + map[y];
        differences.push(imageData.data[idx1] - imageData.data[idx2]);
      }
    }
    
    // Find peak (most frequent difference value)
    const histogram = {};
    let maxCount = 0;
    let peak = 0;
    
    differences.forEach(diff => {
      histogram[diff] = (histogram[diff] || 0) + 1;
      if (histogram[diff] > maxCount) {
        maxCount = histogram[diff];
        peak = diff;
      }
    });
    
    if (maxCount > maxCapacity) {
      maxCapacity = maxCount;
      optimalDelta = delta;
      optimalMap = [...map];
      optimalPeak = peak;
    }
  }
  
  return {
    delta: optimalDelta,
    capacity: maxCapacity,
    map: optimalMap,
    peak: optimalPeak
  };
};

// Apply histogram shifting to prepare for data embedding
export const histogramShifting = (differences, peak) => {
  return differences.map(diff => diff > peak ? diff + 1 : diff);
};

// Embed data into the transformed image
export const embedData = (differences, peak, binaryData) => {
  const result = [...differences];
  let dataIndex = 0;
  
  for (let i = 0; i < result.length && dataIndex < binaryData.length; i++) {
    if (result[i] === peak) {
      if (binaryData[dataIndex] === '1') {
        result[i] += 1;
      }
      dataIndex++;
    }
  }
  
  return {
    embeddedDifferences: result,
    embeddedDataLength: dataIndex
  };
};

// Extract hidden data from the image
export const extractData = (embeddedDifferences, peak, length) => {
  let extractedData = '';
  let count = 0;
  
  for (let i = 0; i < embeddedDifferences.length && count < length; i++) {
    if (embeddedDifferences[i] === peak || embeddedDifferences[i] === peak + 1) {
      extractedData += embeddedDifferences[i] === peak ? '0' : '1';
      count++;
    }
  }
  
  return extractedData;
};

// Reconstruct the image after data extraction
export const reconstructImage = (differences, peak) => {
  return differences.map(diff => diff > peak ? diff - 1 : diff);
};

// Convert string to binary
export const textToBinary = (text) => {
  let binary = '';
  for (let i = 0; i < text.length; i++) {
    const charCode = text.charCodeAt(i).toString(2).padStart(8, '0');
    binary += charCode;
  }
  return binary;
};

// Convert binary to string
export const binaryToText = (binary) => {
  let text = '';
  for (let i = 0; i < binary.length; i += 8) {
    const byte = binary.substr(i, 8);
    if (byte.length === 8) {
      text += String.fromCharCode(parseInt(byte, 2));
    }
  }
  return text;
}; 