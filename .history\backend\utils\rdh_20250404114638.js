/**
 * Reversible Data Hiding (RDH) Utility
 * 
 * This module provides functions for embedding and extracting data from documents
 * using reversible data hiding techniques. The implementation supports multiple
 * security levels and various file types.
 * 
 * Security Levels:
 * 1 - Low: Basic embedding with higher capacity but lower robustness
 * 2 - Medium: Balanced capacity and robustness
 * 3 - High: Highest robustness with lower capacity
 */

import fs from 'fs';
import path from 'path';
import { createHash } from 'crypto';
import sharp from 'sharp';
import { PDFDocument } from 'pdf-lib';
import { v4 as uuidv4 } from 'uuid';
import os from 'os';

/**
 * Embeds data in a document using appropriate RDH method
 * @param {string} filePath - Path to the original file
 * @param {object} data - Data to embed in the document
 * @param {number} securityLevel - Security level (1-3)
 * @returns {string} - Path to the file with embedded data
 */
export const embedDataInDocument = async (filePath, data, securityLevel) => {
  // Validate inputs
  if (!fs.existsSync(filePath)) {
    throw new Error('File not found');
  }
  
  if (!data || typeof data !== 'object') {
    throw new Error('Invalid data format: must be an object');
  }
  
  if (![1, 2, 3].includes(securityLevel)) {
    securityLevel = 1; // Default to low security if invalid
  }
  
  const fileExt = path.extname(filePath).toLowerCase();
  const outputFilePath = path.join(
    os.tmpdir(), 
    `rdh-secured-${uuidv4()}${fileExt}`
  );
  
  // Stringify and encrypt data
  const dataString = JSON.stringify(data);
  const encryptedData = encryptData(dataString, securityLevel);
  
  // Choose RDH method based on file type
  if (['.jpg', '.jpeg', '.png', '.bmp', '.webp', '.tiff'].includes(fileExt)) {
    return await embedInImage(filePath, encryptedData, outputFilePath, securityLevel);
  } else if (['.pdf'].includes(fileExt)) {
    return await embedInPDF(filePath, encryptedData, outputFilePath, securityLevel);
  } else if (['.doc', '.docx'].includes(fileExt)) {
    return await embedInOfficeDoc(filePath, encryptedData, outputFilePath, securityLevel);
  } else {
    // For unsupported file types, use metadata-based embedding
    return await embedInFileMetadata(filePath, encryptedData, outputFilePath);
  }
};

/**
 * Extracts embedded data from a document
 * @param {string} filePath - Path to the file with embedded data
 * @param {number} securityLevel - Security level used for embedding
 * @returns {object} - Extracted data
 */
export const extractDataFromDocument = async (filePath, securityLevel) => {
  if (!fs.existsSync(filePath)) {
    throw new Error('File not found');
  }
  
  if (![1, 2, 3].includes(securityLevel)) {
    securityLevel = 1; // Default to low security if invalid
  }
  
  const fileExt = path.extname(filePath).toLowerCase();
  
  // Choose extraction method based on file type
  let encryptedData;
  
  if (['.jpg', '.jpeg', '.png', '.bmp', '.webp', '.tiff'].includes(fileExt)) {
    encryptedData = await extractFromImage(filePath, securityLevel);
  } else if (['.pdf'].includes(fileExt)) {
    encryptedData = await extractFromPDF(filePath, securityLevel);
  } else if (['.doc', '.docx'].includes(fileExt)) {
    encryptedData = await extractFromOfficeDoc(filePath, securityLevel);
  } else {
    // For unsupported file types, use metadata-based extraction
    encryptedData = await extractFromFileMetadata(filePath);
  }
  
  // Decrypt and parse the data
  try {
    const dataString = decryptData(encryptedData, securityLevel);
    return JSON.parse(dataString);
  } catch (error) {
    throw new Error(`Failed to decrypt or parse extracted data: ${error.message}`);
  }
};

/**
 * Encrypts data string based on security level
 * @param {string} data - Data to encrypt
 * @param {number} securityLevel - Security level
 * @returns {string} - Encrypted data
 */
const encryptData = (data, securityLevel) => {
  // Simple XOR encryption (for demonstration)
  // In production, use a proper crypto library with appropriate security
  const key = createHash('sha256').update(`globalconnect-${securityLevel}`).digest('hex');
  let encrypted = '';
  
  for (let i = 0; i < data.length; i++) {
    const charCode = data.charCodeAt(i) ^ key.charCodeAt(i % key.length);
    encrypted += String.fromCharCode(charCode);
  }
  
  return Buffer.from(encrypted).toString('base64');
};

/**
 * Decrypts data string based on security level
 * @param {string} encrypted - Encrypted data
 * @param {number} securityLevel - Security level
 * @returns {string} - Decrypted data
 */
const decryptData = (encrypted, securityLevel) => {
  const key = createHash('sha256').update(`globalconnect-${securityLevel}`).digest('hex');
  const data = Buffer.from(encrypted, 'base64').toString();
  let decrypted = '';
  
  for (let i = 0; i < data.length; i++) {
    const charCode = data.charCodeAt(i) ^ key.charCodeAt(i % key.length);
    decrypted += String.fromCharCode(charCode);
  }
  
  return decrypted;
};

/**
 * Embeds data in an image using histogram shifting
 * @param {string} imagePath - Path to the original image
 * @param {string} data - Data to embed
 * @param {string} outputPath - Path for the output image
 * @param {number} securityLevel - Security level
 * @returns {string} - Path to the image with embedded data
 */
const embedInImage = async (imagePath, data, outputPath, securityLevel) => {
  // Convert data to binary string
  const binaryData = Array.from(data)
    .map(char => char.charCodeAt(0).toString(2).padStart(8, '0'))
    .join('');
  
  // Load the image
  const image = sharp(imagePath);
  const metadata = await image.metadata();
  
  // Get the image data as raw pixels
  const { data: pixels, info } = await image.raw().toBuffer({ resolveWithObject: true });
  
  // Embed data based on security level
  // Each level uses different embedding techniques
  let modifiedPixels;
  
  switch (securityLevel) {
    case 3: // High security - LSB embedding in selected channels
      modifiedPixels = embedHighSecurity(pixels, binaryData, info);
      break;
    case 2: // Medium security - Difference expansion
      modifiedPixels = embedMediumSecurity(pixels, binaryData, info);
      break;
    case 1: // Low security - Simple LSB replacement
    default:
      modifiedPixels = embedLowSecurity(pixels, binaryData, info);
      break;
  }
  
  // Create a new image with embedded data
  await sharp(modifiedPixels, {
    raw: {
      width: info.width,
      height: info.height,
      channels: info.channels
    }
  })
  .toFormat(metadata.format)
  .toFile(outputPath);
  
  return outputPath;
};

/**
 * Extracts data from an image with embedded data
 * @param {string} imagePath - Path to the image with embedded data
 * @param {number} securityLevel - Security level used for embedding
 * @returns {string} - Extracted data
 */
const extractFromImage = async (imagePath, securityLevel) => {
  // Load the image
  const image = sharp(imagePath);
  const { data: pixels, info } = await image.raw().toBuffer({ resolveWithObject: true });
  
  // Extract data based on security level
  let binaryData;
  
  switch (securityLevel) {
    case 3: // High security
      binaryData = extractHighSecurity(pixels, info);
      break;
    case 2: // Medium security
      binaryData = extractMediumSecurity(pixels, info);
      break;
    case 1: // Low security
    default:
      binaryData = extractLowSecurity(pixels, info);
      break;
  }
  
  // Convert binary string back to text
  let extractedData = '';
  for (let i = 0; i < binaryData.length; i += 8) {
    const byte = binaryData.substr(i, 8);
    if (byte.length === 8) {
      extractedData += String.fromCharCode(parseInt(byte, 2));
    }
  }
  
  return extractedData;
};

/**
 * Low security embedding - simple LSB embedding
 */
const embedLowSecurity = (pixels, binaryData, info) => {
  const result = Buffer.from(pixels);
  const dataLength = binaryData.length;
  
  // Store data length in first 32 pixels (32-bit integer)
  const lengthBinary = dataLength.toString(2).padStart(32, '0');
  for (let i = 0; i < 32; i++) {
    // Clear the LSB and set it to the corresponding bit of data length
    result[i] = (result[i] & 0xFE) | parseInt(lengthBinary[i], 2);
  }
  
  // Embed data in the LSB of pixels starting from index 32
  for (let i = 0; i < dataLength; i++) {
    if (32 + i < result.length) {
      // Clear the LSB and set it to the corresponding bit of data
      result[32 + i] = (result[32 + i] & 0xFE) | parseInt(binaryData[i], 2);
    } else {
      break; // Ran out of pixels
    }
  }
  
  return result;
};

/**
 * Medium security embedding - difference expansion
 */
const embedMediumSecurity = (pixels, binaryData, info) => {
  const result = Buffer.from(pixels);
  const dataLength = binaryData.length;
  
  // Store data length in first 32 pixels using a more robust method
  const lengthBinary = dataLength.toString(2).padStart(32, '0');
  for (let i = 0; i < 32; i++) {
    // Use 2 LSBs of every 4th channel component
    const pixelIndex = i * 4;
    if (pixelIndex + 3 < result.length) {
      result[pixelIndex] = (result[pixelIndex] & 0xFC) | parseInt(lengthBinary.substring(i*2, i*2+2), 2);
    }
  }
  
  // Calculate pairs of adjacent pixels for difference expansion
  let dataIndex = 0;
  for (let i = 128; i < result.length - 1 && dataIndex < dataLength; i += 2) {
    const diff = Math.floor((result[i] - result[i+1]) / 2);
    const sum = result[i] + result[i+1];
    
    // Embed data bit in the LSB of difference
    const newDiff = (diff << 1) | parseInt(binaryData[dataIndex], 2);
    
    // Calculate new pixel values
    result[i] = Math.min(255, Math.max(0, Math.floor((sum + newDiff) / 2)));
    result[i+1] = Math.min(255, Math.max(0, Math.floor((sum - newDiff) / 2)));
    
    dataIndex++;
  }
  
  return result;
};

/**
 * High security embedding - selective channel embedding with redundancy
 */
const embedHighSecurity = (pixels, binaryData, info) => {
  const result = Buffer.from(pixels);
  const dataLength = binaryData.length;
  
  // Store data length with redundancy
  const lengthBinary = dataLength.toString(2).padStart(32, '0');
  for (let i = 0; i < 32; i++) {
    // Embed each bit in 3 different locations for redundancy
    for (let j = 0; j < 3; j++) {
      const pixelIndex = (i * 3 + j) * 8; // Spread out more
      if (pixelIndex < result.length) {
        result[pixelIndex] = (result[pixelIndex] & 0xFE) | parseInt(lengthBinary[i], 2);
      }
    }
  }
  
  // Embed data with redundancy - each bit gets embedded 3 times
  // Use a pseudorandom pattern based on a hash of pixel values
  const hashSeed = createHash('md5').update(result.slice(0, 64)).digest('hex');
  let dataIndex = 0;
  
  while (dataIndex < dataLength) {
    // Generate 3 different positions for the same bit using hash-based seeding
    const positions = [];
    for (let j = 0; j < 3; j++) {
      const hashValue = parseInt(hashSeed.substring((dataIndex * 3 + j) % 28, (dataIndex * 3 + j) % 28 + 4), 16);
      positions.push(128 + hashValue % (result.length - 128));
    }
    
    // Embed the same bit in all 3 positions
    for (const pos of positions) {
      if (pos < result.length) {
        result[pos] = (result[pos] & 0xFE) | parseInt(binaryData[dataIndex], 2);
      }
    }
    
    dataIndex++;
  }
  
  return result;
};

/**
 * Extract data using the low security method
 */
const extractLowSecurity = (pixels, info) => {
  // Extract data length from first 32 pixels
  let lengthBinary = '';
  for (let i = 0; i < 32; i++) {
    lengthBinary += (pixels[i] & 1).toString();
  }
  const dataLength = parseInt(lengthBinary, 2);
  
  // Extract the embedded data
  let binaryData = '';
  for (let i = 0; i < dataLength; i++) {
    if (32 + i < pixels.length) {
      binaryData += (pixels[32 + i] & 1).toString();
    } else {
      break;
    }
  }
  
  return binaryData;
};

/**
 * Extract data using the medium security method
 */
const extractMediumSecurity = (pixels, info) => {
  // Extract data length
  let lengthBinary = '';
  for (let i = 0; i < 32; i++) {
    const pixelIndex = i * 4;
    if (pixelIndex + 3 < pixels.length) {
      lengthBinary += (pixels[pixelIndex] & 3).toString(2).padStart(2, '0');
    }
  }
  const dataLength = parseInt(lengthBinary.substring(0, 32), 2);
  
  // Extract the embedded data from pixel pairs
  let binaryData = '';
  for (let i = 128; i < pixels.length - 1 && binaryData.length < dataLength; i += 2) {
    const diff = Math.floor(pixels[i] - pixels[i+1]);
    binaryData += (diff & 1).toString();
  }
  
  return binaryData;
};

/**
 * Extract data using the high security method
 */
const extractHighSecurity = (pixels, info) => {
  // Extract data length with majority voting for redundancy
  let lengthBinary = '';
  for (let i = 0; i < 32; i++) {
    // Get 3 copies of the same bit
    const bits = [];
    for (let j = 0; j < 3; j++) {
      const pixelIndex = (i * 3 + j) * 8;
      if (pixelIndex < pixels.length) {
        bits.push(pixels[pixelIndex] & 1);
      }
    }
    
    // Majority vote to determine correct bit
    const sum = bits.reduce((a, b) => a + b, 0);
    lengthBinary += (sum >= bits.length / 2 ? 1 : 0).toString();
  }
  const dataLength = parseInt(lengthBinary, 2);
  
  // Extract embedded data with redundancy recovery
  const hashSeed = createHash('md5').update(Buffer.from(pixels.slice(0, 64))).digest('hex');
  let binaryData = '';
  
  for (let dataIndex = 0; dataIndex < dataLength; dataIndex++) {
    // Get 3 copies of the same bit from different positions
    const positions = [];
    for (let j = 0; j < 3; j++) {
      const hashValue = parseInt(hashSeed.substring((dataIndex * 3 + j) % 28, (dataIndex * 3 + j) % 28 + 4), 16);
      positions.push(128 + hashValue % (pixels.length - 128));
    }
    
    // Extract bits and use majority voting
    const bits = [];
    for (const pos of positions) {
      if (pos < pixels.length) {
        bits.push(pixels[pos] & 1);
      }
    }
    
    // Majority vote
    const sum = bits.reduce((a, b) => a + b, 0);
    binaryData += (sum >= bits.length / 2 ? 1 : 0).toString();
  }
  
  return binaryData;
};

/**
 * Embeds data in a PDF document
 * @param {string} pdfPath - Path to the original PDF
 * @param {string} data - Data to embed
 * @param {string} outputPath - Path for the output PDF
 * @param {number} securityLevel - Security level
 * @returns {string} - Path to the PDF with embedded data
 */
const embedInPDF = async (pdfPath, data, outputPath, securityLevel) => {
  // Read the PDF file
  const pdfBytes = fs.readFileSync(pdfPath);
  const pdfDoc = await PDFDocument.load(pdfBytes);
  
  // Add data as document metadata
  pdfDoc.setTitle(`${pdfDoc.getTitle() || 'Document'}_${data.substring(0, 10)}`);
  pdfDoc.setSubject(`GlobalConnect Secured ${securityLevel}`);
  
  // Add custom metadata field to store the data
  pdfDoc.setCustomMetadata('gc_secured_data', data);
  
  // For higher security levels, add redundancy
  if (securityLevel >= 2) {
    // Add data to XMP metadata
    pdfDoc.setProducer(`GlobalConnect Secure ${data.substring(0, 20)}`);
    
    // Add to keywords for another layer of redundancy
    pdfDoc.setKeywords([`GC_DATA:${data.substring(0, 30)}`]);
  }
  
  if (securityLevel === 3) {
    // Add invisible text annotations for even more redundancy
    try {
      // Get the first page
      const pages = pdfDoc.getPages();
      if (pages.length > 0) {
        const firstPage = pages[0];
        // Add an invisible text annotation with the data
        firstPage.drawText(`GC_DATA:${data}`, {
          x: 0,
          y: 0,
          size: 0.01,
          color: { r: 1, g: 1, b: 1, a: 0.01 }, // Almost invisible
        });
      }
    } catch (error) {
      // If annotation fails, continue without it
      console.error('Failed to add annotation:', error);
    }
  }
  
  // Save the PDF with embedded data
  const modifiedPdfBytes = await pdfDoc.save();
  fs.writeFileSync(outputPath, modifiedPdfBytes);
  
  return outputPath;
};

/**
 * Extracts data from a PDF document with embedded data
 * @param {string} pdfPath - Path to the PDF with embedded data
 * @param {number} securityLevel - Security level used for embedding
 * @returns {string} - Extracted data
 */
const extractFromPDF = async (pdfPath, securityLevel) => {
  // Read the PDF file
  const pdfBytes = fs.readFileSync(pdfPath);
  const pdfDoc = await PDFDocument.load(pdfBytes);
  
  // Try to extract from custom metadata first (most reliable)
  let extractedData = pdfDoc.getCustomMetadata('gc_secured_data');
  
  if (!extractedData && securityLevel >= 2) {
    // Try to extract from producer field
    const producer = pdfDoc.getProducer() || '';
    if (producer.startsWith('GlobalConnect Secure ')) {
      extractedData = producer.substring('GlobalConnect Secure '.length);
    }
    
    // Try to extract from keywords
    if (!extractedData) {
      const keywords = pdfDoc.getKeywords();
      for (const keyword of keywords) {
        if (keyword.startsWith('GC_DATA:')) {
          extractedData = keyword.substring('GC_DATA:'.length);
          break;
        }
      }
    }
  }
  
  // Title is the fallback if everything else fails
  if (!extractedData) {
    const title = pdfDoc.getTitle() || '';
    const index = title.lastIndexOf('_');
    if (index > 0) {
      extractedData = title.substring(index + 1);
    }
  }
  
  if (!extractedData) {
    throw new Error('No embedded data found in this PDF');
  }
  
  return extractedData;
};

/**
 * Embeds data in an Office document
 * For demonstration, this uses a simple metadata approach
 * In production, use more sophisticated techniques for Word docs
 */
const embedInOfficeDoc = async (filePath, data, outputPath, securityLevel) => {
  // For simplicity, we'll just use metadata embedding
  // In a real implementation, you would use a library like officegen
  return await embedInFileMetadata(filePath, data, outputPath);
};

/**
 * Extracts data from an Office document with embedded data
 */
const extractFromOfficeDoc = async (filePath, securityLevel) => {
  // For simplicity, we'll just use metadata extraction
  return await extractFromFileMetadata(filePath);
};

/**
 * Embeds data in file metadata (fallback method)
 * @param {string} filePath - Path to the original file
 * @param {string} data - Data to embed
 * @param {string} outputPath - Path for the output file
 * @returns {string} - Path to the file with embedded metadata
 */
const embedInFileMetadata = async (filePath, data, outputPath) => {
  // For files that can't be processed with RDH, just copy and append data in filename
  fs.copyFileSync(filePath, outputPath);
  
  // Store a reference to the data
  const metadataFile = `${outputPath}.metadata`;
  fs.writeFileSync(metadataFile, data);
  
  return outputPath;
};

/**
 * Extracts data from file metadata (fallback method)
 * @param {string} filePath - Path to the file with embedded metadata
 * @returns {string} - Extracted data
 */
const extractFromFileMetadata = async (filePath) => {
  const metadataFile = `${filePath}.metadata`;
  
  if (fs.existsSync(metadataFile)) {
    return fs.readFileSync(metadataFile, 'utf8');
  }
  
  throw new Error('No metadata file found');
};

/**
 * Utility function to convert text to binary
 * @param {string} text - Text to convert
 * @returns {string} - Binary representation
 */
export const textToBinary = (text) => {
  return Array.from(text)
    .map(char => char.charCodeAt(0).toString(2).padStart(8, '0'))
    .join('');
};

/**
 * Utility function to convert binary to text
 * @param {string} binary - Binary representation
 * @returns {string} - Original text
 */
export const binaryToText = (binary) => {
  let text = '';
  for (let i = 0; i < binary.length; i += 8) {
    const byte = binary.substr(i, 8);
    if (byte.length === 8) {
      text += String.fromCharCode(parseInt(byte, 2));
    }
  }
  return text;
}; 