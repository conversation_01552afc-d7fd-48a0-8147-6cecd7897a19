import AWS from 'aws-sdk';
import { v4 as uuidv4 } from 'uuid';
import path from 'path';

// Configure AWS
AWS.config.update({
  region: process.env.AWS_REGION,
  accessKeyId: process.env.AWS_ACCESS_KEY_ID,
  secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY
});

const s3 = new AWS.S3();

/**
 * Upload file to S3
 * @param {Object} file - File object from multer middleware
 * @param {String} folderName - S3 folder name (optional)
 * @returns {Promise<Object>} - Upload result with file URL and key
 */
export const uploadFileToS3 = async (file, folderName = 'uploads') => {
  try {
    const fileExtension = path.extname(file.originalname);
    const fileName = `${uuidv4()}${fileExtension}`;
    const key = folderName ? `${folderName}/${fileName}` : fileName;

    const params = {
      Bucket: process.env.S3_BUCKET_NAME,
      Key: key,
      Body: file.buffer,
      ContentType: file.mimetype,
      ACL: 'public-read'
    };

    const uploadResult = await s3.upload(params).promise();

    return {
      url: uploadResult.Location,
      key: uploadResult.Key
    };
  } catch (error) {
    console.error('S3 Upload Error:', error);
    throw new Error(`Failed to upload file to S3: ${error.message}`);
  }
};

/**
 * Delete file from S3
 * @param {String} key - S3 file key to delete
 * @returns {Promise<Object>} - Deletion result
 */
export const deleteFileFromS3 = async (key) => {
  try {
    const params = {
      Bucket: process.env.S3_BUCKET_NAME,
      Key: key
    };

    const deleteResult = await s3.deleteObject(params).promise();
    return deleteResult;
  } catch (error) {
    console.error('S3 Delete Error:', error);
    throw new Error(`Failed to delete file from S3: ${error.message}`);
  }
};

/**
 * Get signed URL for temporary access to private S3 files
 * @param {String} key - S3 file key
 * @param {Number} expires - URL expiration time in seconds (default: 3600)
 * @returns {String} - Signed URL
 */
export const getSignedUrl = (key, expires = 3600) => {
  const params = {
    Bucket: process.env.S3_BUCKET_NAME,
    Key: key,
    Expires: expires
  };

  return s3.getSignedUrl('getObject', params);
}; 