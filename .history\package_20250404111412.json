{"name": "global-connect", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "server": "node backend/server.js", "dev:all": "concurrently \"npm run dev\" \"npm run server\""}, "dependencies": {"lucide-react": "^0.344.0", "react": "^18.3.1", "react-dom": "^18.3.1", "@reduxjs/toolkit": "^2.2.1", "react-redux": "^9.1.0", "axios": "^1.6.7", "chart.js": "^4.4.1", "d3": "^7.8.5", "express": "^4.18.2", "mongoose": "^8.1.1", "socket.io": "^4.7.4", "socket.io-client": "^4.7.4", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.4.1", "aws-sdk": "^2.1550.0", "react-router-dom": "^6.22.0", "uuid": "^9.0.1", "styled-components": "^6.1.8", "react-chartjs-2": "^5.2.0"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@types/node": "^20.11.5", "@types/d3": "^7.4.3", "@types/express": "^4.17.21", "@types/jsonwebtoken": "^9.0.5", "@types/bcryptjs": "^2.4.6", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2", "nodemon": "^3.0.3", "concurrently": "^8.2.2"}}