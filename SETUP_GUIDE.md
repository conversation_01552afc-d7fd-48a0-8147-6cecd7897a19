# GlobalConnect Setup Guide

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ and npm
- MongoDB (local or cloud)
- Cloudinary account (for file uploads)

### 1. Install Dependencies

```bash
# Install Node.js dependencies
npm install

# Install Python dependencies (for advanced features)
cd backend
pip install -r requirements.txt
cd ..
```

### 2. Environment Configuration

#### Required Environment Variables

**Cloudinary (REQUIRED for file uploads):**
```env
CLOUDINARY_CLOUD_NAME=your_cloud_name_here
CLOUDINARY_API_KEY=your_api_key_here
CLOUDINARY_API_SECRET=your_api_secret_here
```

**MongoDB (REQUIRED):**
```env
MONGO_URI=mongodb://localhost:27017/globalconnect
# OR for MongoDB Atlas:
# MONGO_URI=mongodb+srv://username:<EMAIL>/globalconnect
```

**JWT Secret (REQUIRED):**
```env
JWT_SECRET=your_super_secure_jwt_secret_here
```

#### How to Get Cloudinary Credentials

1. Go to [Cloudinary.com](https://cloudinary.com)
2. Sign up for a free account
3. Go to Dashboard
4. Copy your:
   - Cloud Name
   - API Key
   - API Secret

#### Optional Environment Variables

**Email (for notifications):**
```env
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_app_password
```

**Payment (for premium features):**
```env
STRIPE_SECRET_KEY=your_stripe_secret_key
STRIPE_PUBLISHABLE_KEY=your_stripe_publishable_key
```

### 3. Database Setup

#### Option A: Local MongoDB
```bash
# Install MongoDB locally
# Windows (with Chocolatey):
choco install mongodb

# macOS (with Homebrew):
brew install mongodb-community

# Start MongoDB service
mongod
```

#### Option B: MongoDB Atlas (Cloud)
1. Go to [MongoDB Atlas](https://www.mongodb.com/atlas)
2. Create a free cluster
3. Get connection string
4. Update MONGO_URI in .env

### 4. Start the Application

```bash
# Start both frontend and backend
npm run dev:all

# OR start separately:
# Terminal 1 - Backend
npm run server

# Terminal 2 - Frontend  
npm run dev

# Terminal 3 - Python services (optional)
cd backend
python app.py
```

### 5. Access the Application

- Frontend: http://localhost:5173
- Backend API: http://localhost:5000
- Python Services: http://localhost:5001

## 🔧 Configuration Details

### File Upload Configuration

The application uses Cloudinary for file storage with the following features:
- **Automatic optimization** for images
- **Secure URLs** with access control
- **Multiple file formats** support
- **CDN delivery** for fast global access

### Security Features

- **JWT Authentication** with secure tokens
- **Password hashing** with bcrypt
- **Rate limiting** to prevent abuse
- **File type validation** for uploads
- **CORS protection** for API access

### Database Schema

The application includes the following collections:
- **users** - User accounts and profiles
- **projects** - Project listings and details
- **bids** - Vendor bids on projects
- **messages** - Real-time messaging
- **documents** - File attachments and metadata
- **analytics** - Usage tracking and insights

## 🎨 UI Features

### Design System
- **White + Green theme** with professional styling
- **Responsive design** for all devices
- **Smooth animations** and transitions
- **Loading states** and micro-interactions
- **Accessibility** compliant components

### Key Components
- **Authentication** with social login options
- **Dashboard** with role-based views
- **Project management** with advanced filtering
- **Real-time bidding** system
- **Secure messaging** between users
- **File upload** with drag-and-drop
- **Analytics** and reporting

## 🚨 Troubleshooting

### Common Issues

**1. MongoDB Connection Error**
```
Error: connect ECONNREFUSED 127.0.0.1:27017
```
Solution: Make sure MongoDB is running locally or check your Atlas connection string.

**2. Cloudinary Upload Error**
```
Error: Must supply cloud_name
```
Solution: Verify your Cloudinary credentials in the .env file.

**3. Port Already in Use**
```
Error: listen EADDRINUSE :::5000
```
Solution: Kill the process using the port or change the PORT in .env.

**4. Module Not Found**
```
Error: Cannot find module 'xyz'
```
Solution: Run `npm install` to install missing dependencies.

### Environment Validation

Run the environment checker:
```bash
npm run check-env
```

This will verify all required environment variables are set correctly.

## 📚 Additional Resources

- [Cloudinary Documentation](https://cloudinary.com/documentation)
- [MongoDB Documentation](https://docs.mongodb.com/)
- [React Documentation](https://react.dev/)
- [Node.js Documentation](https://nodejs.org/docs/)
- [Tailwind CSS Documentation](https://tailwindcss.com/docs)

## 🤝 Support

If you encounter any issues:
1. Check this setup guide
2. Review the troubleshooting section
3. Check the console for error messages
4. Ensure all environment variables are set correctly

## 🔄 Development Workflow

### Making Changes
1. Frontend changes: Edit files in `src/`
2. Backend changes: Edit files in `backend/`
3. Styling: Update `tailwind.config.js` or component styles
4. Database: Modify models in `backend/models/`

### Testing
```bash
# Run tests (when implemented)
npm test

# Lint code
npm run lint

# Type check
npm run type-check
```

### Production Deployment
1. Set NODE_ENV=production
2. Build frontend: `npm run build`
3. Configure production database
4. Set up SSL certificates
5. Configure reverse proxy (nginx)
6. Set up monitoring and logging
