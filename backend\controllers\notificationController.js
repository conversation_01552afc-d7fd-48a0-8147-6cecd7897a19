import Notification from '../models/notificationModel.js';
import asyncHandler from '../utils/asyncHandler.js';

// @desc    Get all notifications for the current user
// @route   GET /api/notifications
// @access  Private
export const getNotifications = asyncHandler(async (req, res) => {
  const { page = 1, limit = 20, unreadOnly = false } = req.query;
  
  // Build query
  const query = {
    recipient: req.user._id
  };
  
  if (unreadOnly === 'true') {
    query.isRead = false;
  }
  
  // Execute query with pagination
  const skip = (parseInt(page) - 1) * parseInt(limit);
  
  const notifications = await Notification.find(query)
    .sort({ createdAt: -1 })
    .skip(skip)
    .limit(parseInt(limit));
    
  // Get total count
  const total = await Notification.countDocuments(query);
  
  // Get unread count
  const unreadCount = await Notification.countDocuments({
    recipient: req.user._id,
    isRead: false
  });
  
  res.json({
    notifications,
    unreadCount,
    total,
    currentPage: parseInt(page),
    totalPages: Math.ceil(total / parseInt(limit))
  });
});

// @desc    Mark a notification as read
// @route   PUT /api/notifications/:id/read
// @access  Private
export const markNotificationAsRead = asyncHandler(async (req, res) => {
  const notification = await Notification.findById(req.params.id);
  
  if (!notification) {
    res.status(404);
    throw new Error('Notification not found');
  }
  
  // Check ownership
  if (notification.recipient.toString() !== req.user._id.toString()) {
    res.status(403);
    throw new Error('Not authorized to access this notification');
  }
  
  // Mark as read
  notification.isRead = true;
  notification.readAt = new Date();
  await notification.save();
  
  res.json(notification);
});

// @desc    Mark all notifications as read
// @route   PUT /api/notifications/read-all
// @access  Private
export const markAllNotificationsAsRead = asyncHandler(async (req, res) => {
  const { type } = req.query;
  
  // Build update query
  const query = {
    recipient: req.user._id,
    isRead: false
  };
  
  // If type is specified, only mark notifications of that type as read
  if (type) {
    query.type = type;
  }
  
  // Update all notifications
  const result = await Notification.updateMany(
    query,
    {
      isRead: true,
      readAt: new Date()
    }
  );
  
  // Get unread count after update
  const unreadCount = await Notification.countDocuments({
    recipient: req.user._id,
    isRead: false
  });
  
  res.json({
    success: true,
    markedCount: result.nModified || result.modifiedCount || 0,
    unreadCount
  });
});

// @desc    Delete a notification
// @route   DELETE /api/notifications/:id
// @access  Private
export const deleteNotification = asyncHandler(async (req, res) => {
  const notification = await Notification.findById(req.params.id);
  
  if (!notification) {
    res.status(404);
    throw new Error('Notification not found');
  }
  
  // Check ownership
  if (notification.recipient.toString() !== req.user._id.toString()) {
    res.status(403);
    throw new Error('Not authorized to delete this notification');
  }
  
  // Delete notification
  await notification.remove();
  
  res.json({ success: true, message: 'Notification deleted' });
}); 