import mongoose from 'mongoose';

const analyticsSchema = mongoose.Schema(
  {
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    project: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Project'
    },
    type: {
      type: String,
      enum: [
        'project_view',
        'project_create',
        'bid_submit',
        'bid_accept',
        'bid_reject',
        'message_send',
        'document_upload',
        'document_view',
        'login',
        'signup',
        'profile_view',
        'search'
      ],
      required: true
    },
    metadata: {
      type: mongoose.Schema.Types.Mixed
    },
    ip: {
      type: String
    },
    userAgent: {
      type: String
    },
    country: {
      type: String
    },
    city: {
      type: String
    }
  },
  {
    timestamps: true
  }
);

// Create indexes for efficient analytics retrieval
analyticsSchema.index({ user: 1, createdAt: -1 });
analyticsSchema.index({ project: 1, type: 1, createdAt: -1 });
analyticsSchema.index({ type: 1, createdAt: -1 });
analyticsSchema.index({ createdAt: 1 });

const Analytics = mongoose.model('Analytics', analyticsSchema);

export default Analytics; 