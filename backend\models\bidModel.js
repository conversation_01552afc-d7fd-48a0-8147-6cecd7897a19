import mongoose from 'mongoose';

const bidSchema = mongoose.Schema(
  {
    project: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Project',
      required: true
    },
    vendor: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    amount: {
      type: Number,
      required: [true, 'Please add a bid amount']
    },
    proposal: {
      type: String,
      required: [true, 'Please add a proposal']
    },
    deliveryTime: {
      type: Number,
      required: [true, 'Please specify delivery time in days']
    },
    status: {
      type: String,
      enum: ['pending', 'accepted', 'rejected', 'withdrawn'],
      default: 'pending'
    },
    isCounterOffer: {
      type: Boolean,
      default: false
    },
    originalBid: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Bid'
    },
    revisions: {
      type: Number,
      default: 1
    },
    attachments: [
      {
        name: String,
        url: String,
        type: String
      }
    ],
    milestones: [
      {
        title: String,
        description: String,
        dueDate: Date,
        amount: Number,
        status: {
          type: String,
          enum: ['pending', 'completed', 'in-progress'],
          default: 'pending'
        }
      }
    ],
    optimizationScore: {
      type: Number,
      min: 0,
      max: 100
    },
    competitiveAnalysis: {
      averageAmount: Number,
      lowestAmount: Number,
      highestAmount: Number,
      bidCount: Number
    }
  },
  {
    timestamps: true
  }
);

// Create an index for efficient querying of bids for a project
bidSchema.index({ project: 1, vendor: 1 }, { unique: true });

// Middleware to update project when bid is accepted
bidSchema.pre('save', async function(next) {
  if (this.isModified('status') && this.status === 'accepted') {
    try {
      await this.model('Project').findByIdAndUpdate(this.project, {
        assignedVendor: this.vendor,
        winningBid: this._id,
        status: 'in-progress'
      });
    } catch (error) {
      next(error);
    }
  }
  next();
});

const Bid = mongoose.model('Bid', bidSchema);

export default Bid; 