import mongoose from 'mongoose';

const projectSchema = mongoose.Schema(
  {
    title: {
      type: String,
      required: [true, 'Please add a title'],
      trim: true,
      maxlength: [100, 'Title cannot be more than 100 characters']
    },
    description: {
      type: String,
      required: [true, 'Please add a description']
    },
    client: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    budget: {
      type: Number,
      required: [true, 'Please add a budget amount']
    },
    deadline: {
      type: Date,
      required: [true, 'Please add a deadline']
    },
    status: {
      type: String,
      enum: ['open', 'in-progress', 'review', 'completed', 'cancelled'],
      default: 'open'
    },
    category: {
      type: String,
      required: [true, 'Please add a category'],
      enum: [
        'web-development',
        'mobile-development',
        'design',
        'content',
        'marketing',
        'data-analytics',
        'other'
      ]
    },
    skills: {
      type: [String],
      required: [true, 'Please add at least one required skill']
    },
    attachments: [
      {
        name: String,
        url: String,
        type: String
      }
    ],
    visibility: {
      type: String,
      enum: ['public', 'invite-only', 'private'],
      default: 'public'
    },
    assignedVendor: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    winningBid: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Bid'
    },
    mlPriceRecommendation: {
      type: Number
    },
    priceOptimizationApplied: {
      type: Boolean,
      default: false
    }
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
  }
);

// Virtual for bids
projectSchema.virtual('bids', {
  ref: 'Bid',
  localField: '_id',
  foreignField: 'project',
  justOne: false
});

// Virtual for documents
projectSchema.virtual('documents', {
  ref: 'Document',
  localField: '_id',
  foreignField: 'project',
  justOne: false
});

// Cascade delete bids when a project is deleted
projectSchema.pre('remove', async function(next) {
  await this.model('Bid').deleteMany({ project: this._id });
  await this.model('Document').deleteMany({ project: this._id });
  await this.model('Message').deleteMany({ project: this._id });
  next();
});

const Project = mongoose.model('Project', projectSchema);

export default Project; 