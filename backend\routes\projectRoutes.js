import express from 'express';
import {
  createProject,
  getProjects,
  getProjectById,
  updateProject,
  deleteProject
} from '../controllers/projectController.js';
import { authMiddleware, clientMiddleware, adminMiddleware } from '../middleware/authMiddleware.js';

const router = express.Router();

// All routes require authentication
router.use(authMiddleware);

// Get all projects with filtering
router.get('/', getProjects);

// Get project by ID
router.get('/:id', getProjectById);

// Create project (only clients can create projects)
router.post('/', clientMiddleware, createProject);

// Update project (only project owner or admin)
router.put('/:id', updateProject);

// Delete project (only project owner or admin)
router.delete('/:id', deleteProject);

export default router; 