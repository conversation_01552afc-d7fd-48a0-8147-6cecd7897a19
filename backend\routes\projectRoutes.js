import express from 'express';
import {
  createProject,
  getProjects,
  getProjectById,
  updateProject,
  deleteProject
} from '../controllers/projectController.js';
import { authMiddleware, clientMiddleware, adminMiddleware } from '../middleware/authMiddleware.js';

const router = express.Router();

// Public routes (no authentication required)
// Get all projects with filtering - public for browsing
router.get('/', getProjects);

// Get project by ID - public for viewing
router.get('/:id', getProjectById);

// Protected routes (authentication required)
router.use(authMiddleware);

// Create project (only clients can create projects)
router.post('/', clientMiddleware, createProject);

// Update project (only project owner or admin)
router.put('/:id', updateProject);

// Delete project (only project owner or admin)
router.delete('/:id', deleteProject);

export default router; 