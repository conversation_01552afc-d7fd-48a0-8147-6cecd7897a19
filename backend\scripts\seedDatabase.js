import mongoose from 'mongoose';
import dotenv from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';
import User from '../models/userModel.js';
import Project from '../models/projectModel.js';
import Bid from '../models/bidModel.js';
import { Message, Conversation } from '../models/messageModel.js';
import Document from '../models/documentModel.js';
import Notification from '../models/notificationModel.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

dotenv.config({ path: path.join(__dirname, '../.env') });

// Connect to MongoDB
const connectDB = async () => {
  try {
    const conn = await mongoose.connect(process.env.MONGO_URI);
    console.log(`MongoDB Connected: ${conn.connection.host}`);
  } catch (error) {
    console.error(error);
    process.exit(1);
  }
};

// Clear existing data
const clearData = async () => {
  try {
    await User.deleteMany();
    await Project.deleteMany();
    await Bid.deleteMany();
    await Message.deleteMany();
    await Conversation.deleteMany();
    await Document.deleteMany();
    await Notification.deleteMany();
    console.log('Data cleared successfully');
  } catch (error) {
    console.error('Error clearing data:', error);
  }
};

// Create users
const createUsers = async () => {
  const users = [
    // Admin
    {
      name: 'System Administrator',
      email: '<EMAIL>',
      password: 'admin123',
      role: 'admin',
      location: 'San Francisco, CA, USA',
      bio: 'Platform administrator with full system access.'
    },
    
    // Clients
    {
      name: 'Sarah Johnson',
      email: '<EMAIL>',
      password: 'client123',
      role: 'client',
      company: 'TechCorp Inc.',
      location: 'New York, NY, USA',
      bio: 'CEO of TechCorp Inc., leading digital transformation initiatives.',
      website: 'https://techcorp.com'
    },
    {
      name: 'Michael Chen',
      email: '<EMAIL>',
      password: 'client123',
      role: 'client',
      company: 'Global Marketing Solutions',
      location: 'London, UK',
      bio: 'Marketing director focused on global brand expansion.',
      website: 'https://gms.com'
    },
    {
      name: 'Emily Rodriguez',
      email: '<EMAIL>',
      password: 'client123',
      role: 'client',
      company: 'StartupXYZ',
      location: 'Austin, TX, USA',
      bio: 'Startup founder building the next generation of fintech solutions.',
      website: 'https://startupxyz.com'
    },
    
    // Vendors
    {
      name: 'David Kim',
      email: '<EMAIL>',
      password: 'vendor123',
      role: 'vendor',
      company: 'DK Development',
      location: 'Seoul, South Korea',
      bio: 'Full-stack developer with 8+ years of experience in React, Node.js, and cloud technologies.',
      skills: ['React', 'Node.js', 'Python', 'AWS', 'MongoDB', 'TypeScript'],
      rating: 4.9,
      website: 'https://dkdev.com'
    },
    {
      name: 'Lisa Wang',
      email: '<EMAIL>',
      password: 'vendor123',
      role: 'vendor',
      company: 'Wang Design Studio',
      location: 'Toronto, Canada',
      bio: 'UI/UX designer specializing in modern, user-centered design solutions.',
      skills: ['Figma', 'Adobe Creative Suite', 'Prototyping', 'User Research', 'Wireframing'],
      rating: 4.8,
      website: 'https://wangdesign.com'
    },
    {
      name: 'Carlos Martinez',
      email: '<EMAIL>',
      password: 'vendor123',
      role: 'vendor',
      company: 'Martinez Digital',
      location: 'Barcelona, Spain',
      bio: 'Digital marketing expert with proven track record in SEO, PPC, and social media.',
      skills: ['SEO', 'PPC', 'Social Media Marketing', 'Google Analytics', 'Content Strategy'],
      rating: 4.7,
      website: 'https://martinezdigital.com'
    },
    {
      name: 'Priya Sharma',
      email: '<EMAIL>',
      password: 'vendor123',
      role: 'vendor',
      company: 'Sharma Mobile Solutions',
      location: 'Mumbai, India',
      bio: 'Mobile app developer specializing in cross-platform solutions.',
      skills: ['React Native', 'Flutter', 'iOS', 'Android', 'Firebase', 'API Integration'],
      rating: 4.9,
      website: 'https://sharmamobile.com'
    },
    {
      name: 'James Wilson',
      email: '<EMAIL>',
      password: 'vendor123',
      role: 'vendor',
      company: 'Wilson Content',
      location: 'Sydney, Australia',
      bio: 'Professional content writer and copywriter with expertise in technical documentation.',
      skills: ['Technical Writing', 'Copywriting', 'SEO Writing', 'Content Strategy', 'Editing'],
      rating: 4.6,
      website: 'https://wilsoncontent.com'
    },
    {
      name: 'Anna Kowalski',
      email: '<EMAIL>',
      password: 'vendor123',
      role: 'vendor',
      company: 'Kowalski Analytics',
      location: 'Warsaw, Poland',
      bio: 'Data scientist and analytics expert helping businesses make data-driven decisions.',
      skills: ['Python', 'R', 'Machine Learning', 'Data Visualization', 'SQL', 'Tableau'],
      rating: 4.8,
      website: 'https://kowalskianalytics.com'
    }
  ];

  const createdUsers = await User.insertMany(users);
  console.log(`${createdUsers.length} users created`);
  return createdUsers;
};

// Create projects
const createProjects = async (users) => {
  const clients = users.filter(user => user.role === 'client');
  const vendors = users.filter(user => user.role === 'vendor');
  
  const projects = [
    // Active Projects
    {
      title: 'E-commerce Platform Development',
      description: 'Build a modern, scalable e-commerce platform with React frontend and Node.js backend. Must include payment integration, inventory management, and admin dashboard.',
      client: clients[0]._id, // Sarah Johnson
      budget: 25000,
      deadline: new Date('2024-03-15'),
      status: 'open',
      category: 'web-development',
      skills: ['React', 'Node.js', 'MongoDB', 'Payment Integration', 'AWS'],
      visibility: 'public'
    },
    {
      title: 'Mobile Banking App',
      description: 'Develop a secure mobile banking application with biometric authentication, transaction history, and real-time notifications.',
      client: clients[1]._id, // Michael Chen
      budget: 35000,
      deadline: new Date('2024-04-20'),
      status: 'in-progress',
      category: 'mobile-development',
      skills: ['React Native', 'Security', 'Banking APIs', 'Biometric Auth'],
      assignedVendor: vendors[2]._id, // Priya Sharma
      visibility: 'public'
    },
    {
      title: 'Brand Identity Design',
      description: 'Create a complete brand identity package including logo, color palette, typography, and brand guidelines for a fintech startup.',
      client: clients[2]._id, // Emily Rodriguez
      budget: 8500,
      deadline: new Date('2024-02-28'),
      status: 'open',
      category: 'design',
      skills: ['Logo Design', 'Brand Guidelines', 'Adobe Creative Suite', 'Typography'],
      visibility: 'public'
    },
    {
      title: 'SEO Optimization & Content Strategy',
      description: 'Comprehensive SEO audit and optimization for corporate website, including content strategy and link building campaign.',
      client: clients[0]._id, // Sarah Johnson
      budget: 12000,
      deadline: new Date('2024-03-30'),
      status: 'open',
      category: 'marketing',
      skills: ['SEO', 'Content Strategy', 'Google Analytics', 'Link Building'],
      visibility: 'public'
    },
    {
      title: 'Data Analytics Dashboard',
      description: 'Build an interactive analytics dashboard to visualize business metrics and KPIs with real-time data updates.',
      client: clients[1]._id, // Michael Chen
      budget: 18000,
      deadline: new Date('2024-04-15'),
      status: 'open',
      category: 'data-analytics',
      skills: ['Python', 'Tableau', 'Data Visualization', 'SQL', 'API Integration'],
      visibility: 'public'
    },
    
    // Completed Projects
    {
      title: 'Corporate Website Redesign',
      description: 'Complete redesign of corporate website with modern UI/UX and responsive design.',
      client: clients[0]._id, // Sarah Johnson
      budget: 15000,
      deadline: new Date('2024-01-10'),
      status: 'completed',
      category: 'web-development',
      skills: ['React', 'UI/UX Design', 'Responsive Design'],
      assignedVendor: vendors[0]._id, // David Kim
      visibility: 'public'
    },
    {
      title: 'Technical Documentation',
      description: 'Create comprehensive technical documentation for API and user guides.',
      client: clients[2]._id, // Emily Rodriguez
      budget: 5500,
      deadline: new Date('2024-01-05'),
      status: 'completed',
      category: 'content',
      skills: ['Technical Writing', 'API Documentation', 'User Guides'],
      assignedVendor: vendors[3]._id, // James Wilson
      visibility: 'public'
    },
    {
      title: 'Social Media Campaign',
      description: 'Design and execute a 3-month social media marketing campaign across multiple platforms.',
      client: clients[1]._id, // Michael Chen
      budget: 9500,
      deadline: new Date('2023-12-20'),
      status: 'completed',
      category: 'marketing',
      skills: ['Social Media Marketing', 'Content Creation', 'Campaign Management'],
      assignedVendor: vendors[1]._id, // Carlos Martinez
      visibility: 'public'
    }
  ];

  const createdProjects = await Project.insertMany(projects);
  console.log(`${createdProjects.length} projects created`);
  return createdProjects;
};

// Create bids
const createBids = async (users, projects) => {
  const vendors = users.filter(user => user.role === 'vendor');
  const openProjects = projects.filter(project => project.status === 'open');

  console.log(`Found ${vendors.length} vendors and ${openProjects.length} open projects`);
  
  const bids = [
    // Bids for E-commerce Platform Development
    {
      project: openProjects[0]._id,
      vendor: vendors[0]._id, // David Kim
      amount: 23000,
      deliveryTime: 56, // 8 weeks
      proposal: 'I will build a modern e-commerce platform using React and Node.js with the latest best practices. The solution will include a responsive frontend, secure backend API, payment integration with Stripe, and comprehensive admin dashboard.',
      status: 'pending'
    },
    {
      project: openProjects[0]._id,
      vendor: vendors[2]._id, // Priya Sharma
      amount: 24500,
      deliveryTime: 70, // 10 weeks
      proposal: 'Full-stack e-commerce solution with mobile-first approach. I will deliver a scalable platform with React frontend, Node.js backend, and mobile app for better customer engagement.',
      status: 'pending'
    },
    
    // Bids for Brand Identity Design
    {
      project: openProjects[2]._id,
      vendor: vendors[1]._id, // Lisa Wang
      amount: 7800,
      deliveryTime: 21, // 3 weeks
      proposal: 'Complete brand identity package with multiple logo concepts, comprehensive brand guidelines, and marketing material templates. I will provide 3 initial concepts and unlimited revisions.',
      status: 'pending'
    },
    
    // Bids for SEO Optimization
    {
      project: openProjects[3]._id,
      vendor: vendors[2]._id, // Carlos Martinez
      amount: 11500,
      deliveryTime: 42, // 6 weeks
      proposal: 'Comprehensive SEO strategy including technical audit, on-page optimization, content strategy, and link building campaign. Expected 40-60% increase in organic traffic.',
      status: 'pending'
    },
    
    // Bids for Data Analytics Dashboard (this should be openProjects[3])
    {
      project: openProjects[3]._id,
      vendor: vendors[5]._id, // Anna Kowalski
      amount: 17500,
      deliveryTime: 49, // 7 weeks
      proposal: 'Interactive analytics dashboard using Python, Tableau, and real-time data processing. Will include custom visualizations, automated reporting, and mobile-responsive design.',
      status: 'pending'
    }
  ];

  const createdBids = await Bid.insertMany(bids);
  console.log(`${createdBids.length} bids created`);
  return createdBids;
};

// Create conversations and messages
const createMessagesAndConversations = async (users, projects) => {
  const clients = users.filter(user => user.role === 'client');
  const vendors = users.filter(user => user.role === 'vendor');

  // Create conversations first
  const conversations = [
    {
      participants: [clients[0]._id, vendors[0]._id], // Sarah Johnson & David Kim
      project: projects[0]._id,
      title: 'E-commerce Platform Development Discussion',
      type: 'project'
    },
    {
      participants: [clients[1]._id, vendors[2]._id], // Michael Chen & Priya Sharma
      project: projects[1]._id,
      title: 'Mobile Banking App Progress',
      type: 'project'
    },
    {
      participants: [clients[2]._id, vendors[1]._id], // Emily Rodriguez & Lisa Wang
      project: projects[2]._id,
      title: 'Brand Identity Design',
      type: 'project'
    }
  ];

  const createdConversations = await Conversation.insertMany(conversations);

  // Create messages for each conversation
  const messages = [
    // Conversation 1: Sarah Johnson and David Kim
    {
      conversation: createdConversations[0]._id,
      sender: clients[0]._id,
      content: 'Hi David, I reviewed your bid for the e-commerce platform. Your proposal looks comprehensive. Can we discuss the timeline in more detail?'
    },
    {
      conversation: createdConversations[0]._id,
      sender: vendors[0]._id,
      content: 'Hello Sarah, thank you for considering my proposal. I can definitely adjust the timeline based on your priorities. Would you like to schedule a call to discuss the project phases?'
    },
    {
      conversation: createdConversations[0]._id,
      sender: clients[0]._id,
      content: 'That sounds great! I\'m particularly interested in the payment integration phase. How do you plan to handle multiple payment gateways?'
    },

    // Conversation 2: Michael Chen and Priya Sharma
    {
      conversation: createdConversations[1]._id,
      sender: clients[1]._id,
      content: 'Hi Priya, the mobile banking app is looking great! I tested the latest build and the biometric authentication works perfectly.'
    },
    {
      conversation: createdConversations[1]._id,
      sender: vendors[2]._id,
      content: 'Thank you Michael! I\'m glad you\'re happy with the progress. The transaction history module will be ready for testing by tomorrow.'
    },

    // Conversation 3: Emily Rodriguez and Lisa Wang
    {
      conversation: createdConversations[2]._id,
      sender: clients[2]._id,
      content: 'Lisa, I love the initial logo concepts you shared! The second option really captures our brand vision. Can we explore some color variations?'
    },
    {
      conversation: createdConversations[2]._id,
      sender: vendors[1]._id,
      content: 'Absolutely! I\'ll prepare several color palette options for that concept. I\'m thinking we could explore both modern and traditional fintech color schemes.'
    }
  ];

  const createdMessages = await Message.insertMany(messages);

  // Update conversations with last message
  for (let i = 0; i < createdConversations.length; i++) {
    const conversationMessages = createdMessages.filter(msg =>
      msg.conversation.toString() === createdConversations[i]._id.toString()
    );
    if (conversationMessages.length > 0) {
      const lastMessage = conversationMessages[conversationMessages.length - 1];
      createdConversations[i].lastMessage = lastMessage._id;
      await createdConversations[i].save();
    }
  }

  console.log(`${createdConversations.length} conversations and ${createdMessages.length} messages created`);
  return { conversations: createdConversations, messages: createdMessages };
};

// Create documents
const createDocuments = async (users, projects) => {
  const documents = [
    {
      title: 'E-commerce Requirements Document',
      description: 'Detailed requirements and specifications for the e-commerce platform project',
      project: projects[0]._id,
      uploadedBy: users.find(u => u.email === '<EMAIL>')._id,
      fileKey: 'documents/ecommerce-requirements-2024.pdf',
      fileName: 'ecommerce-requirements.pdf',
      fileType: 'application/pdf',
      fileSize: 2457600, // 2.4 MB
      securityLevel: 2,
      permissions: [projects[0].client.toString()],
      metadata: {
        category: 'requirements',
        version: '1.0'
      }
    },
    {
      title: 'Mobile Banking App Wireframes',
      description: 'UI/UX wireframes and design mockups for the mobile banking application',
      project: projects[1]._id,
      uploadedBy: users.find(u => u.email === '<EMAIL>')._id,
      fileKey: 'documents/banking-wireframes-v2.fig',
      fileName: 'banking-wireframes.fig',
      fileType: 'application/figma',
      fileSize: 9123456, // 8.7 MB
      securityLevel: 3,
      permissions: [projects[1].client.toString(), projects[1].assignedVendor.toString()],
      metadata: {
        category: 'design',
        version: '2.0'
      }
    },
    {
      title: 'Brand Guidelines Draft',
      description: 'Initial brand identity guidelines and style guide',
      project: projects[2]._id,
      uploadedBy: users.find(u => u.email === '<EMAIL>')._id,
      fileKey: 'documents/brand-guidelines-draft.pdf',
      fileName: 'brand-guidelines-v1.pdf',
      fileType: 'application/pdf',
      fileSize: ********, // 15.2 MB
      securityLevel: 1,
      permissions: [projects[2].client.toString()],
      metadata: {
        category: 'branding',
        version: '1.0'
      }
    },
    {
      title: 'API Documentation',
      description: 'Comprehensive API documentation and integration guides',
      project: projects[5]._id, // Completed project
      uploadedBy: users.find(u => u.email === '<EMAIL>')._id,
      fileKey: 'documents/api-documentation-final.pdf',
      fileName: 'api-docs.pdf',
      fileType: 'application/pdf',
      fileSize: 3456789, // 3.3 MB
      securityLevel: 1,
      permissions: [],
      metadata: {
        category: 'documentation',
        version: 'final'
      }
    }
  ];

  const createdDocuments = await Document.insertMany(documents);
  console.log(`${createdDocuments.length} documents created`);
  return createdDocuments;
};

// Create notifications
const createNotifications = async (users, projects, bids) => {
  const notifications = [
    {
      recipient: users.find(u => u.email === '<EMAIL>')._id,
      type: 'bid',
      title: 'New Bid Received',
      content: 'David Kim submitted a bid for your E-commerce Platform Development project',
      reference: {
        model: 'Bid',
        id: bids[0]._id
      },
      isRead: false
    },
    {
      recipient: users.find(u => u.email === '<EMAIL>')._id,
      type: 'project_update',
      title: 'Project Milestone Completed',
      content: 'Mobile Banking App has reached 35% completion',
      reference: {
        model: 'Project',
        id: projects[1]._id
      },
      isRead: true
    },
    {
      recipient: users.find(u => u.email === '<EMAIL>')._id,
      type: 'message',
      title: 'New Message',
      content: 'Sarah Johnson sent you a message about E-commerce Platform Development',
      reference: {
        model: 'Project',
        id: projects[0]._id
      },
      isRead: false
    },
    {
      recipient: users.find(u => u.email === '<EMAIL>')._id,
      type: 'project_update',
      title: 'Project Deadline Reminder',
      content: 'Brand Identity Design project deadline is in 5 days',
      reference: {
        model: 'Project',
        id: projects[2]._id
      },
      isRead: false
    },
    {
      recipient: users.find(u => u.email === '<EMAIL>')._id,
      type: 'system',
      title: 'Payment Released',
      content: 'Payment of $5,500 has been released for Technical Documentation project',
      reference: {
        model: 'Project',
        id: projects[6]._id
      },
      isRead: true
    }
  ];

  const createdNotifications = await Notification.insertMany(notifications);
  console.log(`${createdNotifications.length} notifications created`);
  return createdNotifications;
};

// Main seeding function
const seedDatabase = async () => {
  try {
    await connectDB();
    await clearData();

    const users = await createUsers();
    const projects = await createProjects(users);
    const bids = await createBids(users, projects);
    const { conversations, messages } = await createMessagesAndConversations(users, projects);
    const documents = await createDocuments(users, projects);
    const notifications = await createNotifications(users, projects, bids);

    console.log('Database seeded successfully!');
    console.log(`Created:`);
    console.log(`- ${users.length} users`);
    console.log(`- ${projects.length} projects`);
    console.log(`- ${bids.length} bids`);
    console.log(`- ${conversations.length} conversations`);
    console.log(`- ${messages.length} messages`);
    console.log(`- ${documents.length} documents`);
    console.log(`- ${notifications.length} notifications`);

    process.exit(0);
  } catch (error) {
    console.error('Error seeding database:', error);
    process.exit(1);
  }
};

// Run the seeding
seedDatabase();
