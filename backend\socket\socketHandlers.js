import { Server } from 'socket.io';
import jwt from 'jsonwebtoken';
import { Message, Conversation } from '../models/messageModel.js';
import User from '../models/userModel.js';
import Notification from '../models/notificationModel.js';
import Bid from '../models/bidModel.js';
import Project from '../models/projectModel.js';

export const initializeSocketIO = (server) => {
  const io = new Server(server, {
    cors: {
      origin: process.env.FRONTEND_URL || 'http://localhost:3000',
      methods: ['GET', 'POST'],
      credentials: true
    },
    pingTimeout: 60000
  });
  
  // Store connected users
  const connectedUsers = new Map();
  
  // Make io available to express routes
  server.app.set('io', io);
  
  // Authentication middleware
  io.use(async (socket, next) => {
    try {
      const token = socket.handshake.auth.token;
      
      if (!token) {
        return next(new Error('Authentication error: Token missing'));
      }
      
      // Verify token
      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      const user = await User.findById(decoded.id).select('-password');
      
      if (!user) {
        return next(new Error('Authentication error: User not found'));
      }
      
      // Attach user to socket
      socket.user = user;
      next();
    } catch (error) {
      return next(new Error('Authentication error: Invalid token'));
    }
  });
  
  io.on('connection', (socket) => {
    console.log(`User connected: ${socket.user._id}`);
    
    // Add user to connected users map
    connectedUsers.set(socket.user._id.toString(), socket.id);
    
    // Join user's personal room
    socket.join(`user:${socket.user._id}`);
    
    // Send user online status to friends/contacts
    io.emit('user_status_change', {
      userId: socket.user._id,
      status: 'online'
    });
    
    // Handle joining conversation rooms
    socket.on('join_conversation', async (conversationId) => {
      try {
        // Verify the conversation exists and user is a participant
        const conversation = await Conversation.findById(conversationId);
        
        if (!conversation) {
          socket.emit('error', { message: 'Conversation not found' });
          return;
        }
        
        const isParticipant = conversation.participants.some(
          p => p.toString() === socket.user._id.toString()
        );
        
        if (!isParticipant) {
          socket.emit('error', { message: 'Not authorized to join this conversation' });
          return;
        }
        
        // Join the conversation room
        socket.join(`conversation:${conversationId}`);
        
        // Reset unread count for user when joining conversation
        await conversation.resetUnreadCountForUser(socket.user._id);
        
        socket.emit('conversation_joined', { conversationId });
      } catch (error) {
        socket.emit('error', { message: error.message });
      }
    });
    
    // Handle leaving conversation rooms
    socket.on('leave_conversation', (conversationId) => {
      socket.leave(`conversation:${conversationId}`);
    });
    
    // Handle typing indicator
    socket.on('typing', ({ conversationId, isTyping }) => {
      socket.to(`conversation:${conversationId}`).emit('typing', {
        userId: socket.user._id,
        conversationId,
        isTyping
      });
    });
    
    // Handle message sending
    socket.on('send_message', async (messageData) => {
      try {
        const { conversationId, content, attachments = [] } = messageData;
        
        // Verify the conversation exists and user is a participant
        const conversation = await Conversation.findById(conversationId);
        
        if (!conversation) {
          socket.emit('error', { message: 'Conversation not found' });
          return;
        }
        
        const isParticipant = conversation.participants.some(
          p => p.toString() === socket.user._id.toString()
        );
        
        if (!isParticipant) {
          socket.emit('error', { message: 'Not authorized to send messages to this conversation' });
          return;
        }
        
        // Create new message
        const message = await Message.create({
          conversation: conversationId,
          sender: socket.user._id,
          content,
          attachments,
          readBy: [{ user: socket.user._id }] // Mark as read by sender
        });
        
        // Update conversation with last message info and unread counts
        await conversation.updateUnreadCounts(socket.user._id, message._id);
        
        // Populate message before broadcasting
        const populatedMessage = await Message.findById(message._id)
          .populate('sender', 'name email profileImage role');
          
        // Broadcast to all users in the conversation
        io.to(`conversation:${conversationId}`).emit('new_message', {
          message: populatedMessage,
          conversation: conversation._id
        });
        
        // Create notifications for offline participants
        conversation.participants.forEach(async (participantId) => {
          if (participantId.toString() !== socket.user._id.toString()) {
            // Check if user is online
            if (!connectedUsers.has(participantId.toString())) {
              // Create notification for offline user
              await Notification.create({
                recipient: participantId,
                type: 'message',
                title: `New message from ${socket.user.name}`,
                content: content.substring(0, 100) + (content.length > 100 ? '...' : ''),
                reference: {
                  model: 'Message',
                  id: message._id
                },
                metadata: {
                  conversationId,
                  senderId: socket.user._id,
                  senderName: socket.user.name,
                  senderImage: socket.user.profileImage || null
                }
              });
            }
          }
        });
      } catch (error) {
        socket.emit('error', { message: error.message });
      }
    });
    
    // Handle bid notifications
    socket.on('bid_placed', async ({ bidId, projectId }) => {
      try {
        const bid = await Bid.findById(bidId)
          .populate('vendor', 'name email')
          .populate('project', 'title client');
        
        if (!bid) {
          return;
        }
        
        // Get project owner's socket
        const ownerSocketId = connectedUsers.get(bid.project.client.toString());
        
        // Send real-time notification to project owner if online
        if (ownerSocketId) {
          io.to(ownerSocketId).emit('new_bid', {
            bid,
            projectId
          });
        }
        
        // Create notification for project owner
        await Notification.create({
          recipient: bid.project.client,
          type: 'bid',
          title: 'New bid received',
          content: `${bid.vendor.name} placed a bid on your project: ${bid.project.title}`,
          reference: {
            model: 'Bid',
            id: bid._id
          },
          metadata: {
            projectId,
            bidId: bid._id,
            amount: bid.amount,
            vendorId: bid.vendor._id,
            vendorName: bid.vendor.name
          }
        });
      } catch (error) {
        console.error('Socket error (bid_placed):', error);
      }
    });
    
    // Handle bid status change notifications
    socket.on('bid_status_changed', async ({ bidId, status }) => {
      try {
        const bid = await Bid.findById(bidId)
          .populate('vendor', 'name')
          .populate('project', 'title');
        
        if (!bid) {
          return;
        }
        
        // Get vendor's socket
        const vendorSocketId = connectedUsers.get(bid.vendor._id.toString());
        
        // Send real-time notification to vendor if online
        if (vendorSocketId) {
          io.to(vendorSocketId).emit('bid_status_update', {
            bidId,
            status,
            projectTitle: bid.project.title
          });
        }
        
        // Create notification for vendor
        let title, content;
        if (status === 'accepted') {
          title = 'Bid Accepted';
          content = `Your bid on project "${bid.project.title}" has been accepted!`;
        } else if (status === 'rejected') {
          title = 'Bid Rejected';
          content = `Your bid on project "${bid.project.title}" has been rejected.`;
        } else if (status === 'countered') {
          title = 'Counter Offer Received';
          content = `You received a counter offer for your bid on "${bid.project.title}".`;
        }
        
        if (title && content) {
          await Notification.create({
            recipient: bid.vendor._id,
            type: 'bid_update',
            title,
            content,
            reference: {
              model: 'Bid',
              id: bid._id
            },
            metadata: {
              projectId: bid.project._id,
              bidId: bid._id,
              status
            }
          });
        }
      } catch (error) {
        console.error('Socket error (bid_status_changed):', error);
      }
    });
    
    // Handle project status changes
    socket.on('project_status_changed', async ({ projectId, status }) => {
      try {
        const project = await Project.findById(projectId)
          .populate('client', 'name')
          .populate('assignedVendor', 'name');
        
        if (!project) {
          return;
        }
        
        // Determine who should be notified
        let recipientId = null;
        let notificationTitle = '';
        let notificationContent = '';
        
        if (status === 'in-progress' && project.assignedVendor) {
          // Notify vendor that project has started
          recipientId = project.assignedVendor._id;
          notificationTitle = 'Project Started';
          notificationContent = `The project "${project.title}" has been started by ${project.client.name}.`;
        } else if (status === 'completed' && project.assignedVendor) {
          // Notify vendor that project is completed
          recipientId = project.assignedVendor._id;
          notificationTitle = 'Project Completed';
          notificationContent = `The project "${project.title}" has been marked as completed by ${project.client.name}.`;
        } else if (status === 'cancelled') {
          // Notify vendor that project was cancelled
          if (project.assignedVendor) {
            recipientId = project.assignedVendor._id;
            notificationTitle = 'Project Cancelled';
            notificationContent = `The project "${project.title}" has been cancelled by ${project.client.name}.`;
          }
        }
        
        if (recipientId) {
          // Get recipient's socket
          const recipientSocketId = connectedUsers.get(recipientId.toString());
          
          // Send real-time notification if recipient is online
          if (recipientSocketId) {
            io.to(recipientSocketId).emit('project_update', {
              projectId,
              status,
              title: project.title
            });
          }
          
          // Create notification
          await Notification.create({
            recipient: recipientId,
            type: 'project_update',
            title: notificationTitle,
            content: notificationContent,
            reference: {
              model: 'Project',
              id: project._id
            },
            metadata: {
              projectId: project._id,
              status
            }
          });
        }
      } catch (error) {
        console.error('Socket error (project_status_changed):', error);
      }
    });
    
    // Handle disconnection
    socket.on('disconnect', () => {
      console.log(`User disconnected: ${socket.user._id}`);
      
      // Remove user from connected users map
      connectedUsers.delete(socket.user._id.toString());
      
      // Broadcast user offline status
      io.emit('user_status_change', {
        userId: socket.user._id,
        status: 'offline'
      });
    });
  });
  
  return io;
}; 