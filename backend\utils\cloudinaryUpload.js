import { v2 as cloudinary } from 'cloudinary';
import { v4 as uuidv4 } from 'uuid';
import path from 'path';
import fs from 'fs';

// Configure Cloudinary
cloudinary.config({
  cloud_name: process.env.CLOUDINARY_CLOUD_NAME,
  api_key: process.env.CLOUDINARY_API_KEY,
  api_secret: process.env.CLOUDINARY_API_SECRET
});

/**
 * Upload file to Cloudinary
 * @param {Object} file - File object from multer middleware
 * @param {String} folderName - Cloudinary folder name (optional)
 * @returns {Promise<Object>} - Upload result with file URL and key
 */
export const uploadFileToCloudinary = async (file, folderName = 'uploads') => {
  try {
    const fileExtension = path.extname(file.originalname);
    const fileName = `${uuidv4()}${fileExtension}`;
    const publicId = folderName ? `${folderName}/${fileName}` : fileName;

    // Determine resource type based on file type
    let resourceType = 'auto';
    if (file.mimetype.startsWith('image/')) {
      resourceType = 'image';
    } else if (file.mimetype.startsWith('video/')) {
      resourceType = 'video';
    } else {
      resourceType = 'raw'; // For documents, PDFs, etc.
    }

    const uploadResult = await new Promise((resolve, reject) => {
      cloudinary.uploader.upload_stream(
        {
          resource_type: resourceType,
          public_id: publicId,
          folder: folderName,
          use_filename: true,
          unique_filename: true
        },
        (error, result) => {
          if (error) {
            reject(error);
          } else {
            resolve(result);
          }
        }
      ).end(file.buffer);
    });

    return {
      url: uploadResult.secure_url,
      key: uploadResult.public_id,
      resourceType: uploadResult.resource_type,
      format: uploadResult.format
    };
  } catch (error) {
    console.error('Cloudinary Upload Error:', error);
    throw new Error(`Failed to upload file to Cloudinary: ${error.message}`);
  }
};

/**
 * Delete file from Cloudinary
 * @param {String} key - Cloudinary public_id to delete
 * @param {String} resourceType - Resource type (image, video, raw)
 * @returns {Promise<Object>} - Deletion result
 */
export const deleteFileFromCloudinary = async (key, resourceType = 'auto') => {
  try {
    const deleteResult = await cloudinary.uploader.destroy(key, {
      resource_type: resourceType
    });

    return {
      success: deleteResult.result === 'ok',
      result: deleteResult.result
    };
  } catch (error) {
    console.error('Cloudinary Delete Error:', error);
    throw new Error(`Failed to delete file from Cloudinary: ${error.message}`);
  }
};

/**
 * Get secure URL for Cloudinary files
 * @param {String} key - Cloudinary public_id
 * @param {Object} options - Transformation options
 * @returns {String} - Secure URL
 */
export const getSecureUrl = (key, options = {}) => {
  try {
    return cloudinary.url(key, {
      secure: true,
      ...options
    });
  } catch (error) {
    console.error('Cloudinary URL Error:', error);
    return null;
  }
};

// Backward compatibility exports
export const uploadFileToS3 = uploadFileToCloudinary;
export const deleteFileFromS3 = deleteFileFromCloudinary;
export const getSignedUrl = getSecureUrl;