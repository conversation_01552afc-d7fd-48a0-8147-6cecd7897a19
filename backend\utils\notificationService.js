import Notification from '../models/notificationModel.js';
import { io } from '../server.js';

class NotificationService {
  // Create a new notification
  async createNotification({
    recipient,
    type,
    title,
    content,
    reference,
    metadata = {}
  }) {
    try {
      const notification = await Notification.create({
        recipient,
        type,
        title,
        content,
        reference,
        metadata
      });

      // Emit real-time notification via Socket.IO
      if (io) {
        io.to(`user_${recipient}`).emit('new_notification', {
          notification: await notification.populate('recipient', 'name email'),
          unreadCount: await Notification.getUnreadCountForUser(recipient)
        });
      }

      return notification;
    } catch (error) {
      console.error('Error creating notification:', error);
      throw error;
    }
  }

  // Create notification for new message
  async notifyNewMessage(messageData) {
    const { conversation, message, recipients } = messageData;
    
    for (const recipientId of recipients) {
      // Don't notify the sender
      if (recipientId.toString() === message.sender.toString()) continue;

      await this.createNotification({
        recipient: recipientId,
        type: 'message',
        title: 'New Message',
        content: `You have a new message${conversation.project ? ` in project: ${conversation.project.title}` : ''}`,
        reference: {
          model: 'Message',
          id: message._id
        },
        metadata: {
          conversationId: conversation._id,
          senderId: message.sender,
          projectId: conversation.project?._id
        }
      });
    }
  }

  // Create notification for new bid
  async notifyNewBid(bidData) {
    const { bid, project } = bidData;

    await this.createNotification({
      recipient: project.client,
      type: 'bid',
      title: 'New Bid Received',
      content: `You received a new bid of $${bid.amount.toLocaleString()} for "${project.title}"`,
      reference: {
        model: 'Bid',
        id: bid._id
      },
      metadata: {
        projectId: project._id,
        vendorId: bid.vendor,
        amount: bid.amount
      }
    });
  }

  // Create notification for bid status update
  async notifyBidStatusUpdate(bidData) {
    const { bid, project, status } = bidData;

    let title, content;
    switch (status) {
      case 'accepted':
        title = 'Bid Accepted!';
        content = `Congratulations! Your bid for "${project.title}" has been accepted.`;
        break;
      case 'rejected':
        title = 'Bid Update';
        content = `Your bid for "${project.title}" was not selected this time.`;
        break;
      case 'withdrawn':
        title = 'Bid Withdrawn';
        content = `Your bid for "${project.title}" has been withdrawn.`;
        break;
      default:
        title = 'Bid Update';
        content = `Your bid status for "${project.title}" has been updated.`;
    }

    await this.createNotification({
      recipient: bid.vendor,
      type: 'bid_update',
      title,
      content,
      reference: {
        model: 'Bid',
        id: bid._id
      },
      metadata: {
        projectId: project._id,
        status,
        amount: bid.amount
      }
    });
  }

  // Create notification for project status update
  async notifyProjectStatusUpdate(projectData) {
    const { project, status, recipients } = projectData;

    let title, content;
    switch (status) {
      case 'in-progress':
        title = 'Project Started';
        content = `Project "${project.title}" has started and is now in progress.`;
        break;
      case 'review':
        title = 'Project Under Review';
        content = `Project "${project.title}" is now under review.`;
        break;
      case 'completed':
        title = 'Project Completed';
        content = `Project "${project.title}" has been completed successfully.`;
        break;
      case 'cancelled':
        title = 'Project Cancelled';
        content = `Project "${project.title}" has been cancelled.`;
        break;
      default:
        title = 'Project Update';
        content = `Project "${project.title}" status has been updated.`;
    }

    for (const recipientId of recipients) {
      await this.createNotification({
        recipient: recipientId,
        type: 'project_update',
        title,
        content,
        reference: {
          model: 'Project',
          id: project._id
        },
        metadata: {
          status,
          budget: project.budget
        }
      });
    }
  }

  // Create notification for document upload
  async notifyDocumentUpload(documentData) {
    const { document, project, recipients } = documentData;

    for (const recipientId of recipients) {
      // Don't notify the uploader
      if (recipientId.toString() === document.uploadedBy.toString()) continue;

      await this.createNotification({
        recipient: recipientId,
        type: 'document',
        title: 'New Document Uploaded',
        content: `A new document "${document.title}" has been uploaded to project "${project.title}"`,
        reference: {
          model: 'Document',
          id: document._id
        },
        metadata: {
          projectId: project._id,
          uploaderId: document.uploadedBy,
          fileName: document.fileName
        }
      });
    }
  }

  // Create system notification
  async notifySystem(notificationData) {
    const { recipients, title, content, metadata = {} } = notificationData;

    for (const recipientId of recipients) {
      await this.createNotification({
        recipient: recipientId,
        type: 'system',
        title,
        content,
        reference: {
          model: 'User',
          id: recipientId
        },
        metadata
      });
    }
  }

  // Bulk create notifications
  async createBulkNotifications(notifications) {
    try {
      const createdNotifications = await Notification.insertMany(notifications);
      
      // Emit real-time notifications
      if (io) {
        for (const notification of createdNotifications) {
          const unreadCount = await Notification.getUnreadCountForUser(notification.recipient);
          io.to(`user_${notification.recipient}`).emit('new_notification', {
            notification,
            unreadCount
          });
        }
      }

      return createdNotifications;
    } catch (error) {
      console.error('Error creating bulk notifications:', error);
      throw error;
    }
  }

  // Clean up old notifications (called by cron job)
  async cleanupOldNotifications() {
    try {
      const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
      const result = await Notification.deleteMany({
        createdAt: { $lt: thirtyDaysAgo },
        isRead: true
      });
      
      console.log(`Cleaned up ${result.deletedCount} old notifications`);
      return result.deletedCount;
    } catch (error) {
      console.error('Error cleaning up notifications:', error);
      throw error;
    }
  }

  // Get notification statistics
  async getNotificationStats(userId) {
    try {
      const stats = await Notification.aggregate([
        { $match: { recipient: userId } },
        {
          $group: {
            _id: '$type',
            total: { $sum: 1 },
            unread: {
              $sum: {
                $cond: [{ $eq: ['$isRead', false] }, 1, 0]
              }
            }
          }
        }
      ]);

      return stats.reduce((acc, stat) => {
        acc[stat._id] = {
          total: stat.total,
          unread: stat.unread
        };
        return acc;
      }, {});
    } catch (error) {
      console.error('Error getting notification stats:', error);
      throw error;
    }
  }
}

export default new NotificationService();
