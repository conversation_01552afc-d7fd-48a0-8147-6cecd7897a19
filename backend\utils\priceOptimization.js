/**
 * ML Price Optimization utilities for the dynamic bidding engine
 * Simulates ML functions for price recommendation based on historical data
 */

// Calculate the optimal price based on historical project data
export const calculateOptimalPrice = async (project, historicalData) => {
  try {
    // In a real implementation, this would use a trained ML model
    // For this implementation, we'll use a simplified approach
    
    // 1. Filter relevant historical projects by category and required skills
    const relevantProjects = historicalData.filter(p => {
      // Match by category
      const categoryMatch = p.category === project.category;
      
      // Match by at least one skill
      const skillsMatch = p.skills.some(skill => 
        project.skills.includes(skill)
      );
      
      return categoryMatch && skillsMatch;
    });
    
    if (relevantProjects.length === 0) {
      // If no relevant projects, return a price within 10% of the budget
      return {
        recommendedPrice: Math.round(project.budget * 0.9),
        confidence: 'low',
        explanation: 'No historical data available for this project type'
      };
    }
    
    // 2. Calculate statistics from relevant projects
    const successfulProjects = relevantProjects.filter(p => 
      p.status === 'completed' && p.winningBid
    );
    
    // Calculate average successful bid amount
    const avgSuccessfulBid = successfulProjects.length > 0 
      ? successfulProjects.reduce((sum, p) => sum + p.winningBid.amount, 0) / successfulProjects.length 
      : 0;
    
    // Calculate market rates (average of all bids)
    const allBids = relevantProjects.flatMap(p => p.bids || []);
    const avgMarketRate = allBids.length > 0 
      ? allBids.reduce((sum, b) => sum + b.amount, 0) / allBids.length 
      : 0;
    
    // 3. Analyze bid success rates at different price points
    const bidSuccessRates = [];
    
    // Group bids into price brackets (10% increments of budget)
    for (let i = 0.5; i <= 1.0; i += 0.1) {
      const lowerBound = project.budget * (i - 0.1);
      const upperBound = project.budget * i;
      
      const bidsInRange = allBids.filter(b => 
        b.amount >= lowerBound && b.amount <= upperBound
      );
      
      const successfulBidsInRange = bidsInRange.filter(b => 
        b.status === 'accepted'
      );
      
      const successRate = bidsInRange.length > 0 
        ? successfulBidsInRange.length / bidsInRange.length 
        : 0;
      
      bidSuccessRates.push({
        pricePoint: i,
        successRate,
        count: bidsInRange.length
      });
    }
    
    // 4. Determine optimal price based on highest success rate
    // with sufficient data points
    const validRates = bidSuccessRates.filter(r => r.count >= 3);
    let optimalRate = validRates.length > 0
      ? validRates.reduce((prev, current) => 
          current.successRate > prev.successRate ? current : prev
        )
      : null;
    
    // If we don't have enough data, use the project with the most similar budget
    if (!optimalRate) {
      const mostSimilarProject = relevantProjects.reduce((prev, current) => {
        const prevDiff = Math.abs(prev.budget - project.budget);
        const currDiff = Math.abs(current.budget - project.budget);
        return currDiff < prevDiff ? current : prev;
      });
      
      const targetPrice = mostSimilarProject.winningBid 
        ? mostSimilarProject.winningBid.amount
        : project.budget * 0.85;
      
      return {
        recommendedPrice: Math.round(targetPrice),
        confidence: 'low',
        explanation: 'Based on the most similar historical project'
      };
    }
    
    // Calculate recommended price using the optimal price point
    const recommendedPrice = Math.round(project.budget * optimalRate.pricePoint);
    
    // Determine confidence level based on data volume
    let confidence = 'low';
    if (validRates.length > 5 && optimalRate.count > 10) {
      confidence = 'high';
    } else if (validRates.length > 3 && optimalRate.count > 5) {
      confidence = 'medium';
    }
    
    return {
      recommendedPrice,
      confidence,
      explanation: `Based on ${successfulProjects.length} similar completed projects`,
      statistics: {
        avgSuccessfulBid,
        avgMarketRate,
        optimalPricePoint: optimalRate.pricePoint,
        successRate: optimalRate.successRate,
        sampleSize: optimalRate.count
      }
    };
    
  } catch (error) {
    console.error('Price optimization error:', error);
    // Fallback to a simple calculation if the ML approach fails
    return {
      recommendedPrice: Math.round(project.budget * 0.85),
      confidence: 'low',
      explanation: 'Error in price optimization calculation'
    };
  }
};

// Calculate competitive analysis for a bid
export const calculateCompetitiveAnalysis = async (bid, allBids) => {
  try {
    if (!allBids || allBids.length === 0) {
      return {
        averageAmount: bid.amount,
        lowestAmount: bid.amount,
        highestAmount: bid.amount,
        bidCount: 1
      };
    }
    
    const amounts = allBids.map(b => b.amount);
    const averageAmount = amounts.reduce((sum, a) => sum + a, 0) / amounts.length;
    const lowestAmount = Math.min(...amounts);
    const highestAmount = Math.max(...amounts);
    
    return {
      averageAmount,
      lowestAmount,
      highestAmount,
      bidCount: allBids.length
    };
  } catch (error) {
    console.error('Competitive analysis error:', error);
    return null;
  }
};

// Calculate bid optimization score (0-100)
export const calculateOptimizationScore = async (bid, project, competitiveAnalysis) => {
  try {
    if (!competitiveAnalysis) return 50;
    
    const scores = {
      price: 0,         // 0-40 points
      deliveryTime: 0,  // 0-30 points
      vendorRating: 0,  // 0-20 points
      proposal: 0       // 0-10 points
    };
    
    // Price score (lower is better, but not too much lower than average)
    if (bid.amount < competitiveAnalysis.lowestAmount) {
      scores.price = 20; // Too low might be suspicious
    } else if (bid.amount > competitiveAnalysis.highestAmount) {
      scores.price = 10; // Too high is rarely optimal
    } else {
      // Calculate relative position between lowest and highest
      const range = competitiveAnalysis.highestAmount - competitiveAnalysis.lowestAmount;
      if (range === 0) {
        scores.price = 35;
      } else {
        const position = (bid.amount - competitiveAnalysis.lowestAmount) / range;
        // Optimal is slightly below average
        scores.price = 40 - Math.abs(0.4 - position) * 40;
      }
    }
    
    // Delivery time score (faster is better)
    const projectDeadlineDays = Math.ceil((new Date(project.deadline) - new Date()) / (1000 * 60 * 60 * 24));
    const deliveryRatio = bid.deliveryTime / projectDeadlineDays;
    
    if (deliveryRatio <= 0.5) {
      scores.deliveryTime = 30;
    } else if (deliveryRatio <= 0.7) {
      scores.deliveryTime = 25;
    } else if (deliveryRatio <= 0.9) {
      scores.deliveryTime = 20;
    } else {
      scores.deliveryTime = 15;
    }
    
    // Vendor rating score
    const vendorRating = bid.vendor.rating || 0;
    scores.vendorRating = vendorRating * 4; // 0-5 rating * 4 = 0-20 points
    
    // Proposal score (based on length for simplicity)
    const proposalLength = bid.proposal.length;
    if (proposalLength >= 1000) {
      scores.proposal = 10;
    } else if (proposalLength >= 500) {
      scores.proposal = 8;
    } else if (proposalLength >= 200) {
      scores.proposal = 5;
    } else {
      scores.proposal = 3;
    }
    
    // Calculate total score
    const totalScore = Math.round(
      scores.price + scores.deliveryTime + scores.vendorRating + scores.proposal
    );
    
    return Math.min(100, Math.max(0, totalScore));
  } catch (error) {
    console.error('Optimization score calculation error:', error);
    return 50; // Default score on error
  }
}; 