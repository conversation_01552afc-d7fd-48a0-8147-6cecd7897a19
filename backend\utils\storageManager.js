import fs from 'fs';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';
import { fileURLToPath } from 'url';

const __dirname = path.dirname(fileURLToPath(import.meta.url));

/**
 * Flexible storage manager that can use different storage backends
 */
class StorageManager {
  constructor(storageType = 'local') {
    this.storageType = storageType;
    this.uploadsDir = path.join(__dirname, '../../uploads');
    
    // Ensure uploads directory exists
    if (!fs.existsSync(this.uploadsDir)) {
      fs.mkdirSync(this.uploadsDir, { recursive: true });
    }
  }

  /**
   * Upload file using the configured storage backend
   */
  async uploadFile(file, folderName = 'documents') {
    switch (this.storageType) {
      case 'local':
        return await this.uploadToLocal(file, folderName);
      case 'cloudinary':
        return await this.uploadToCloudinary(file, folderName);
      case 'firebase':
        return await this.uploadToFirebase(file, folderName);
      default:
        return await this.uploadToLocal(file, folderName);
    }
  }

  /**
   * Local file storage implementation
   */
  async uploadToLocal(file, folderName) {
    try {
      const folderPath = path.join(this.uploadsDir, folderName);
      
      // Create folder if it doesn't exist
      if (!fs.existsSync(folderPath)) {
        fs.mkdirSync(folderPath, { recursive: true });
      }

      const fileExtension = path.extname(file.originalname);
      const fileName = `${uuidv4()}${fileExtension}`;
      const filePath = path.join(folderPath, fileName);

      // Write file to disk
      fs.writeFileSync(filePath, file.buffer);

      return {
        url: `/uploads/${folderName}/${fileName}`,
        key: `${folderName}/${fileName}`,
        path: filePath,
        size: file.size,
        type: file.mimetype
      };
    } catch (error) {
      throw new Error(`Local upload failed: ${error.message}`);
    }
  }

  /**
   * Cloudinary upload implementation
   */
  async uploadToCloudinary(file, folderName) {
    try {
      // This would require cloudinary package
      // const { v2: cloudinary } = await import('cloudinary');
      
      // For now, fallback to local storage
      console.log('Cloudinary not configured, falling back to local storage');
      return await this.uploadToLocal(file, folderName);
    } catch (error) {
      console.log('Cloudinary upload failed, falling back to local storage');
      return await this.uploadToLocal(file, folderName);
    }
  }

  /**
   * Firebase upload implementation
   */
  async uploadToFirebase(file, folderName) {
    try {
      // This would require firebase-admin package
      // const admin = await import('firebase-admin');
      
      // For now, fallback to local storage
      console.log('Firebase not configured, falling back to local storage');
      return await this.uploadToLocal(file, folderName);
    } catch (error) {
      console.log('Firebase upload failed, falling back to local storage');
      return await this.uploadToLocal(file, folderName);
    }
  }

  /**
   * Delete file from storage
   */
  async deleteFile(key) {
    switch (this.storageType) {
      case 'local':
        return await this.deleteFromLocal(key);
      case 'cloudinary':
        return await this.deleteFromCloudinary(key);
      case 'firebase':
        return await this.deleteFromFirebase(key);
      default:
        return await this.deleteFromLocal(key);
    }
  }

  /**
   * Delete file from local storage
   */
  async deleteFromLocal(key) {
    try {
      const filePath = path.join(this.uploadsDir, key);
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
        return { success: true };
      }
      return { success: false, error: 'File not found' };
    } catch (error) {
      throw new Error(`Local delete failed: ${error.message}`);
    }
  }

  /**
   * Delete from Cloudinary
   */
  async deleteFromCloudinary(key) {
    // Implementation would go here
    return { success: true };
  }

  /**
   * Delete from Firebase
   */
  async deleteFromFirebase(key) {
    // Implementation would go here
    return { success: true };
  }

  /**
   * Get file URL
   */
  getFileUrl(key) {
    switch (this.storageType) {
      case 'local':
        return `${process.env.CLIENT_URL || 'http://localhost:5173'}/uploads/${key}`;
      case 'cloudinary':
        // Would return Cloudinary URL
        return `${process.env.CLIENT_URL || 'http://localhost:5173'}/uploads/${key}`;
      case 'firebase':
        // Would return Firebase URL
        return `${process.env.CLIENT_URL || 'http://localhost:5173'}/uploads/${key}`;
      default:
        return `${process.env.CLIENT_URL || 'http://localhost:5173'}/uploads/${key}`;
    }
  }
}

// Create singleton instance
const storageManager = new StorageManager(process.env.STORAGE_TYPE || 'local');

export default storageManager;

// Export individual functions for backward compatibility
export const uploadFile = (file, folderName) => storageManager.uploadFile(file, folderName);
export const deleteFile = (key) => storageManager.deleteFile(key);
export const getFileUrl = (key) => storageManager.getFileUrl(key);
