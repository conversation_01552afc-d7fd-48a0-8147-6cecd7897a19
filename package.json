{"name": "global-connect", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "server": "node backend/server.js", "dev:all": "concurrently \"npm run dev\" \"npm run server\"", "check-env": "node scripts/check-env.js", "setup": "npm install && npm run check-env", "start": "npm run build && npm run server"}, "dependencies": {"@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "@reduxjs/toolkit": "^2.2.1", "axios": "^1.6.7", "bcryptjs": "^2.4.3", "chart.js": "^4.4.1", "cloudinary": "^2.7.0", "clsx": "^2.1.1", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "d3": "^7.8.5", "dotenv": "^16.4.1", "express": "^4.18.2", "framer-motion": "^12.23.6", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.344.0", "mongoose": "^8.1.1", "morgan": "^1.10.1", "multer": "^2.0.2", "pdf-lib": "^1.17.1", "react": "^18.3.1", "react-chartjs-2": "^5.2.0", "react-dom": "^18.3.1", "react-redux": "^9.1.0", "react-router-dom": "^6.22.0", "sharp": "^0.34.3", "socket.io": "^4.7.4", "socket.io-client": "^4.7.4", "styled-components": "^6.1.8", "uuid": "^9.0.1"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/bcryptjs": "^2.4.6", "@types/d3": "^7.4.3", "@types/express": "^4.17.21", "@types/jsonwebtoken": "^9.0.5", "@types/node": "^20.11.5", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "concurrently": "^8.2.2", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "nodemon": "^3.0.3", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2"}}