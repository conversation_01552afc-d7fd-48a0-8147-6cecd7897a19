#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __dirname = path.dirname(fileURLToPath(import.meta.url));

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m'
};

function colorize(text, color) {
  return `${colors[color]}${text}${colors.reset}`;
}

function checkEnvironment() {
  console.log(colorize('\n🔍 GlobalConnect Environment Checker\n', 'cyan'));

  const envPath = path.join(__dirname, '../backend/.env');
  
  if (!fs.existsSync(envPath)) {
    console.log(colorize('❌ .env file not found!', 'red'));
    console.log(colorize('Please create backend/.env file with required variables.', 'yellow'));
    return false;
  }

  // Read .env file
  const envContent = fs.readFileSync(envPath, 'utf8');
  const envVars = {};
  
  envContent.split('\n').forEach(line => {
    const [key, value] = line.split('=');
    if (key && value) {
      envVars[key.trim()] = value.trim();
    }
  });

  let allGood = true;
  const issues = [];

  // Required variables
  const required = [
    {
      key: 'NODE_ENV',
      description: 'Application environment',
      example: 'development'
    },
    {
      key: 'PORT',
      description: 'Server port',
      example: '5000'
    },
    {
      key: 'MONGO_URI',
      description: 'MongoDB connection string',
      example: 'mongodb://localhost:27017/globalconnect'
    },
    {
      key: 'JWT_SECRET',
      description: 'JWT signing secret',
      example: 'your_super_secure_jwt_secret_here',
      secure: true
    },
    {
      key: 'CLIENT_URL',
      description: 'Frontend URL',
      example: 'http://localhost:5173'
    },
    {
      key: 'CLOUDINARY_CLOUD_NAME',
      description: 'Cloudinary cloud name',
      example: 'your_cloud_name_here',
      required: true
    },
    {
      key: 'CLOUDINARY_API_KEY',
      description: 'Cloudinary API key',
      example: 'your_api_key_here',
      required: true
    },
    {
      key: 'CLOUDINARY_API_SECRET',
      description: 'Cloudinary API secret',
      example: 'your_api_secret_here',
      required: true,
      secure: true
    }
  ];

  console.log(colorize('📋 Checking Required Variables:\n', 'blue'));

  required.forEach(({ key, description, example, required: isRequired, secure }) => {
    const value = envVars[key];
    const hasValue = value && value !== example && !value.includes('your_') && !value.includes('_here');
    
    if (!value) {
      console.log(colorize(`❌ ${key}`, 'red') + ` - ${description}`);
      console.log(colorize(`   Missing! Example: ${example}`, 'yellow'));
      if (isRequired) {
        allGood = false;
        issues.push(`${key} is required but missing`);
      }
    } else if (!hasValue) {
      console.log(colorize(`⚠️  ${key}`, 'yellow') + ` - ${description}`);
      console.log(colorize(`   Using placeholder value. Please update with real credentials.`, 'yellow'));
      if (isRequired) {
        allGood = false;
        issues.push(`${key} has placeholder value`);
      }
    } else {
      const displayValue = secure ? '***' : value.length > 50 ? value.substring(0, 47) + '...' : value;
      console.log(colorize(`✅ ${key}`, 'green') + ` - ${displayValue}`);
    }
  });

  // Optional variables
  const optional = [
    'EMAIL_HOST',
    'EMAIL_PORT', 
    'EMAIL_USER',
    'EMAIL_PASS',
    'STRIPE_SECRET_KEY',
    'STRIPE_PUBLISHABLE_KEY',
    'GOOGLE_ANALYTICS_ID',
    'GOOGLE_MAPS_API_KEY'
  ];

  console.log(colorize('\n📋 Optional Variables:\n', 'blue'));

  optional.forEach(key => {
    const value = envVars[key];
    if (value && !value.includes('your_') && !value.includes('_here')) {
      const displayValue = key.includes('SECRET') || key.includes('PASS') ? '***' : value;
      console.log(colorize(`✅ ${key}`, 'green') + ` - ${displayValue}`);
    } else {
      console.log(colorize(`⚪ ${key}`, 'white') + ` - Not configured (optional)`);
    }
  });

  // Check MongoDB connection
  console.log(colorize('\n🔗 Testing Connections:\n', 'blue'));

  if (envVars.MONGO_URI && !envVars.MONGO_URI.includes('your_')) {
    console.log(colorize('📊 MongoDB:', 'cyan'));
    if (envVars.MONGO_URI.includes('localhost') || envVars.MONGO_URI.includes('127.0.0.1')) {
      console.log(colorize('   Local MongoDB detected. Make sure MongoDB is running locally.', 'yellow'));
    } else if (envVars.MONGO_URI.includes('mongodb.net')) {
      console.log(colorize('   MongoDB Atlas detected. Connection will be tested at runtime.', 'green'));
    }
  }

  // Check Cloudinary
  if (envVars.CLOUDINARY_CLOUD_NAME && !envVars.CLOUDINARY_CLOUD_NAME.includes('your_')) {
    console.log(colorize('☁️  Cloudinary: Configured', 'green'));
  } else {
    console.log(colorize('☁️  Cloudinary: Not configured (required for file uploads)', 'red'));
    allGood = false;
    issues.push('Cloudinary credentials are required');
  }

  // Summary
  console.log(colorize('\n📊 Summary:\n', 'magenta'));

  if (allGood) {
    console.log(colorize('🎉 Environment configuration looks good!', 'green'));
    console.log(colorize('You can start the application with: npm run dev:all', 'cyan'));
  } else {
    console.log(colorize('❌ Environment configuration has issues:', 'red'));
    issues.forEach(issue => {
      console.log(colorize(`   • ${issue}`, 'red'));
    });
    console.log(colorize('\n📖 Please check SETUP_GUIDE.md for detailed instructions.', 'yellow'));
  }

  // Next steps
  console.log(colorize('\n🚀 Next Steps:', 'cyan'));
  console.log('1. Update missing environment variables');
  console.log('2. Start MongoDB (if using local)');
  console.log('3. Run: npm run dev:all');
  console.log('4. Open: http://localhost:5173');

  console.log(colorize('\n📚 Resources:', 'blue'));
  console.log('• Cloudinary: https://cloudinary.com/console');
  console.log('• MongoDB Atlas: https://cloud.mongodb.com/');
  console.log('• Setup Guide: ./SETUP_GUIDE.md');

  return allGood;
}

// Run the check
checkEnvironment();
