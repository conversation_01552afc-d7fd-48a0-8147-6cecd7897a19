import React, { useState } from 'react';
import Navigation from './components/Navigation';
import Hero from './components/Hero';
import Features from './components/Features';
import Blog from './components/Blog';
import Reviews from './components/Reviews';
import Footer from './components/Footer';
import AuthModal from './components/Auth/AuthModal';
import Dashboard from './components/Dashboard/Dashboard';
import { NotificationContainer, useNotifications } from './components/UI/Notifications';

function App() {
  const [isAuthModalOpen, setIsAuthModalOpen] = useState(false);
  const [authMode, setAuthMode] = useState<'signin' | 'signup'>('signin');
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [currentUser, setCurrentUser] = useState<any>(null);
  const [currentView, setCurrentView] = useState<'landing' | 'dashboard'>('landing');

  const { notifications, removeNotification, showSuccess, showError } = useNotifications();

  const handleAuthSuccess = (userData: any) => {
    setIsAuthenticated(true);
    setCurrentUser(userData);
    setCurrentView('dashboard');
    setIsAuthModalOpen(false);
    showSuccess('Welcome!', `Successfully signed ${authMode === 'signin' ? 'in' : 'up'}`);
  };

  const handleSignOut = () => {
    setIsAuthenticated(false);
    setCurrentUser(null);
    setCurrentView('landing');
    showSuccess('Signed out', 'You have been successfully signed out');
  };

  const openAuthModal = (mode: 'signin' | 'signup') => {
    setAuthMode(mode);
    setIsAuthModalOpen(true);
  };

  if (currentView === 'dashboard' && isAuthenticated) {
    return (
      <div className="min-h-screen bg-white">
        <Navigation
          isAuthenticated={isAuthenticated}
          user={currentUser}
        />
        <Dashboard userRole={currentUser?.role || 'client'} />
        <NotificationContainer
          notifications={notifications}
          onClose={removeNotification}
        />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white">
      <Navigation
        isAuthenticated={isAuthenticated}
        user={currentUser}
      />
      <Hero />
      <Features />
      <Blog />
      <Reviews />
      <Footer />

      <AuthModal
        isOpen={isAuthModalOpen}
        onClose={() => setIsAuthModalOpen(false)}
        initialMode={authMode}
      />

      <NotificationContainer
        notifications={notifications}
        onClose={removeNotification}
      />
    </div>
  );
}

export default App;