import React, { useState, useEffect } from 'react';
import { 
  Search, 
  Filter, 
  MoreVertical, 
  Edit, 
  Trash2, 
  Eye,
  FileText,
  DollarSign,
  Calendar,
  User,
  Building
} from 'lucide-react';
import { adminService, ProjectManagement as ProjectManagementType } from '../../services/adminService';
import { Project } from '../../services/projectService';

export default function ProjectManagement() {
  const [projects, setProjects] = useState<Project[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [actionLoading, setActionLoading] = useState<string | null>(null);

  useEffect(() => {
    fetchProjects();
  }, [page, statusFilter]);

  const fetchProjects = async () => {
    try {
      setLoading(true);
      const response = await adminService.getProjects(page, 20, statusFilter);
      setProjects(response.projects);
      setTotalPages(response.totalPages);
    } catch (error: any) {
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateStatus = async (projectId: string, newStatus: string) => {
    const reason = prompt(`Enter reason for changing status to ${newStatus}:`);
    if (!reason) return;

    try {
      setActionLoading(projectId);
      await adminService.updateProjectStatus(projectId, newStatus, reason);
      await fetchProjects();
    } catch (error: any) {
      alert(error.message);
    } finally {
      setActionLoading(null);
    }
  };

  const handleDeleteProject = async (projectId: string) => {
    if (!confirm('Are you sure you want to delete this project? This action cannot be undone.')) {
      return;
    }

    try {
      setActionLoading(projectId);
      await adminService.deleteProject(projectId);
      await fetchProjects();
    } catch (error: any) {
      alert(error.message);
    } finally {
      setActionLoading(null);
    }
  };

  const getStatusColor = (status: string) => {
    const colors = {
      open: 'bg-blue-100 text-blue-700',
      'in-progress': 'bg-yellow-100 text-yellow-700',
      completed: 'bg-green-100 text-green-700',
      cancelled: 'bg-red-100 text-red-700',
      review: 'bg-purple-100 text-purple-700'
    };
    return colors[status as keyof typeof colors] || 'bg-gray-100 text-gray-700';
  };

  const getCategoryColor = (category: string) => {
    const colors = {
      'web-development': 'bg-blue-100 text-blue-700',
      'mobile-development': 'bg-green-100 text-green-700',
      'design': 'bg-purple-100 text-purple-700',
      'marketing': 'bg-orange-100 text-orange-700',
      'writing': 'bg-indigo-100 text-indigo-700',
      'other': 'bg-gray-100 text-gray-700'
    };
    return colors[category as keyof typeof colors] || 'bg-gray-100 text-gray-700';
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
        <div>
          <h2 className="text-2xl font-bold text-secondary-900">Project Management</h2>
          <p className="text-secondary-600">Monitor and manage platform projects</p>
        </div>
      </div>

      {/* Filters */}
      <div className="card p-6">
        <div className="flex flex-col lg:flex-row gap-4">
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="input-field"
          >
            <option value="all">All Status</option>
            <option value="open">Open</option>
            <option value="in-progress">In Progress</option>
            <option value="completed">Completed</option>
            <option value="cancelled">Cancelled</option>
            <option value="review">Under Review</option>
          </select>
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <div className="p-4 bg-error-50 border border-error-200 rounded-lg">
          <p className="text-error-700">{error}</p>
        </div>
      )}

      {/* Projects Table */}
      <div className="card overflow-hidden">
        {loading ? (
          <div className="flex items-center justify-center h-64">
            <div className="w-8 h-8 border-4 border-primary-200 border-t-primary-600 rounded-full animate-spin"></div>
          </div>
        ) : projects.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-64 text-secondary-500">
            <FileText className="w-12 h-12 mb-4" />
            <p>No projects found</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-secondary-50 border-b border-secondary-200">
                <tr>
                  <th className="text-left py-3 px-6 font-medium text-secondary-700">Project</th>
                  <th className="text-left py-3 px-6 font-medium text-secondary-700">Client</th>
                  <th className="text-left py-3 px-6 font-medium text-secondary-700">Budget</th>
                  <th className="text-left py-3 px-6 font-medium text-secondary-700">Status</th>
                  <th className="text-left py-3 px-6 font-medium text-secondary-700">Created</th>
                  <th className="text-left py-3 px-6 font-medium text-secondary-700">Actions</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-secondary-100">
                {projects.map((project) => (
                  <tr key={project._id} className="hover:bg-secondary-50">
                    <td className="py-4 px-6">
                      <div>
                        <p className="font-medium text-secondary-900 mb-1">{project.title}</p>
                        <div className="flex items-center space-x-2">
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getCategoryColor(project.category)}`}>
                            {project.category.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                          </span>
                          {project.isUrgent && (
                            <span className="px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-700">
                              Urgent
                            </span>
                          )}
                        </div>
                      </div>
                    </td>
                    <td className="py-4 px-6">
                      <div className="flex items-center space-x-2">
                        <div className="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">
                          {typeof project.client === 'object' && project.client.profileImage ? (
                            <img 
                              src={project.client.profileImage} 
                              alt={project.client.name}
                              className="w-8 h-8 rounded-full object-cover"
                            />
                          ) : (
                            <User className="w-4 h-4 text-primary-600" />
                          )}
                        </div>
                        <div>
                          <p className="text-sm font-medium text-secondary-900">
                            {typeof project.client === 'object' ? project.client.name : 'Unknown'}
                          </p>
                          <p className="text-xs text-secondary-500">
                            {typeof project.client === 'object' ? project.client.email : ''}
                          </p>
                        </div>
                      </div>
                    </td>
                    <td className="py-4 px-6">
                      <div className="flex items-center text-sm">
                        <DollarSign className="w-4 h-4 mr-1 text-secondary-400" />
                        <span className="font-medium text-secondary-900">
                          {project.budget.toLocaleString()}
                        </span>
                      </div>
                    </td>
                    <td className="py-4 px-6">
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(project.status)}`}>
                        {project.status.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                      </span>
                    </td>
                    <td className="py-4 px-6">
                      <div className="flex items-center text-sm text-secondary-600">
                        <Calendar className="w-4 h-4 mr-1" />
                        {new Date(project.createdAt).toLocaleDateString()}
                      </div>
                    </td>
                    <td className="py-4 px-6">
                      <div className="flex items-center space-x-2">
                        <button
                          onClick={() => window.open(`/projects/${project._id}`, '_blank')}
                          className="p-1 text-secondary-400 hover:text-primary-600 transition-colors duration-200"
                          title="View project"
                        >
                          <Eye className="w-4 h-4" />
                        </button>
                        
                        <div className="relative group">
                          <button className="p-1 text-secondary-400 hover:text-secondary-600 transition-colors duration-200">
                            <MoreVertical className="w-4 h-4" />
                          </button>
                          
                          <div className="absolute right-0 top-full mt-1 bg-white border border-secondary-200 rounded-lg shadow-lg z-10 min-w-40 opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none group-hover:pointer-events-auto">
                            {project.status !== 'completed' && (
                              <button
                                onClick={() => handleUpdateStatus(project._id, 'completed')}
                                disabled={actionLoading === project._id}
                                className="w-full text-left px-3 py-2 text-sm text-secondary-700 hover:bg-secondary-50 transition-colors duration-200 disabled:opacity-50"
                              >
                                Mark Completed
                              </button>
                            )}
                            {project.status !== 'cancelled' && (
                              <button
                                onClick={() => handleUpdateStatus(project._id, 'cancelled')}
                                disabled={actionLoading === project._id}
                                className="w-full text-left px-3 py-2 text-sm text-secondary-700 hover:bg-secondary-50 transition-colors duration-200 disabled:opacity-50"
                              >
                                Cancel Project
                              </button>
                            )}
                            <button
                              onClick={() => handleDeleteProject(project._id)}
                              disabled={actionLoading === project._id}
                              className="w-full text-left px-3 py-2 text-sm text-error-600 hover:bg-error-50 transition-colors duration-200 disabled:opacity-50"
                            >
                              Delete Project
                            </button>
                          </div>
                        </div>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between">
          <p className="text-sm text-secondary-600">
            Page {page} of {totalPages}
          </p>
          <div className="flex space-x-2">
            <button
              onClick={() => setPage(page - 1)}
              disabled={page === 1}
              className="btn-secondary disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Previous
            </button>
            <button
              onClick={() => setPage(page + 1)}
              disabled={page === totalPages}
              className="btn-secondary disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Next
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
