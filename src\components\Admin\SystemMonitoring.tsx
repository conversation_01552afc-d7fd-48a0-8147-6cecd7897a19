import React, { useState, useEffect } from 'react';
import { 
  Activity, 
  Server, 
  Database, 
  HardDrive, 
  Cpu, 
  Wifi, 
  AlertTriangle,
  CheckCircle,
  XCircle,
  Clock,
  RefreshCw,
  Download
} from 'lucide-react';

interface SystemMetrics {
  cpu: {
    usage: number;
    cores: number;
    temperature: number;
  };
  memory: {
    used: number;
    total: number;
    percentage: number;
  };
  storage: {
    used: number;
    total: number;
    percentage: number;
  };
  network: {
    inbound: number;
    outbound: number;
    latency: number;
  };
  database: {
    connections: number;
    queries: number;
    responseTime: number;
  };
  uptime: number;
  lastBackup: string;
}

interface SystemAlert {
  id: string;
  type: 'error' | 'warning' | 'info';
  message: string;
  timestamp: string;
  resolved: boolean;
}

export default function SystemMonitoring() {
  const [metrics, setMetrics] = useState<SystemMetrics | null>(null);
  const [alerts, setAlerts] = useState<SystemAlert[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    fetchSystemData();
    
    // Set up auto-refresh every 30 seconds
    const interval = setInterval(fetchSystemData, 30000);
    return () => clearInterval(interval);
  }, []);

  const fetchSystemData = async () => {
    try {
      setRefreshing(true);
      
      // Simulate system metrics (in real app, this would come from backend)
      const mockMetrics: SystemMetrics = {
        cpu: {
          usage: Math.random() * 100,
          cores: 8,
          temperature: 45 + Math.random() * 20
        },
        memory: {
          used: 6.4,
          total: 16,
          percentage: 40 + Math.random() * 30
        },
        storage: {
          used: 450,
          total: 1000,
          percentage: 45 + Math.random() * 30
        },
        network: {
          inbound: Math.random() * 100,
          outbound: Math.random() * 50,
          latency: 10 + Math.random() * 20
        },
        database: {
          connections: Math.floor(Math.random() * 100),
          queries: Math.floor(Math.random() * 1000),
          responseTime: Math.random() * 100
        },
        uptime: 99.9,
        lastBackup: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString()
      };

      const mockAlerts: SystemAlert[] = [
        {
          id: '1',
          type: 'warning',
          message: 'High CPU usage detected (85%)',
          timestamp: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
          resolved: false
        },
        {
          id: '2',
          type: 'info',
          message: 'Database backup completed successfully',
          timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
          resolved: true
        },
        {
          id: '3',
          type: 'error',
          message: 'Failed to connect to external API',
          timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),
          resolved: true
        }
      ];

      setMetrics(mockMetrics);
      setAlerts(mockAlerts);
    } catch (error) {
      console.error('Failed to fetch system data:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const getStatusColor = (percentage: number, reverse: boolean = false) => {
    if (reverse) {
      if (percentage > 90) return 'text-error-600';
      if (percentage > 70) return 'text-warning-600';
      return 'text-success-600';
    } else {
      if (percentage < 70) return 'text-error-600';
      if (percentage < 90) return 'text-warning-600';
      return 'text-success-600';
    }
  };

  const getProgressColor = (percentage: number, reverse: boolean = false) => {
    if (reverse) {
      if (percentage > 90) return 'bg-error-500';
      if (percentage > 70) return 'bg-warning-500';
      return 'bg-success-500';
    } else {
      if (percentage < 70) return 'bg-error-500';
      if (percentage < 90) return 'bg-warning-500';
      return 'bg-success-500';
    }
  };

  const formatUptime = (uptime: number) => {
    return `${uptime.toFixed(2)}%`;
  };

  const formatBytes = (bytes: number) => {
    if (bytes >= 1024) {
      return `${(bytes / 1024).toFixed(1)} TB`;
    }
    return `${bytes.toFixed(1)} GB`;
  };

  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 1) {
      const diffInMinutes = Math.floor(diffInHours * 60);
      return `${diffInMinutes}m ago`;
    } else if (diffInHours < 24) {
      return `${Math.floor(diffInHours)}h ago`;
    } else {
      return date.toLocaleDateString();
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="w-8 h-8 border-4 border-primary-200 border-t-primary-600 rounded-full animate-spin"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
        <div>
          <h2 className="text-2xl font-bold text-secondary-900">System Monitoring</h2>
          <p className="text-secondary-600">Real-time system performance and health metrics</p>
        </div>
        
        <div className="flex items-center space-x-4 mt-4 lg:mt-0">
          <button
            onClick={fetchSystemData}
            disabled={refreshing}
            className="btn-secondary"
          >
            <RefreshCw className={`w-5 h-5 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            Refresh
          </button>
          <button className="btn-primary">
            <Download className="w-5 h-5 mr-2" />
            Export Logs
          </button>
        </div>
      </div>

      {/* System Status Overview */}
      {metrics && (
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
          {/* CPU Usage */}
          <div className="card p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center">
                <Cpu className="w-5 h-5 text-blue-600 mr-2" />
                <h3 className="font-semibold text-secondary-900">CPU Usage</h3>
              </div>
              <span className={`text-sm font-medium ${getStatusColor(metrics.cpu.usage, true)}`}>
                {metrics.cpu.usage.toFixed(1)}%
              </span>
            </div>
            <div className="w-full bg-secondary-200 rounded-full h-2 mb-2">
              <div 
                className={`h-2 rounded-full ${getProgressColor(metrics.cpu.usage, true)}`}
                style={{ width: `${metrics.cpu.usage}%` }}
              ></div>
            </div>
            <p className="text-xs text-secondary-600">
              {metrics.cpu.cores} cores • {metrics.cpu.temperature.toFixed(1)}°C
            </p>
          </div>

          {/* Memory Usage */}
          <div className="card p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center">
                <HardDrive className="w-5 h-5 text-green-600 mr-2" />
                <h3 className="font-semibold text-secondary-900">Memory</h3>
              </div>
              <span className={`text-sm font-medium ${getStatusColor(metrics.memory.percentage, true)}`}>
                {metrics.memory.percentage.toFixed(1)}%
              </span>
            </div>
            <div className="w-full bg-secondary-200 rounded-full h-2 mb-2">
              <div 
                className={`h-2 rounded-full ${getProgressColor(metrics.memory.percentage, true)}`}
                style={{ width: `${metrics.memory.percentage}%` }}
              ></div>
            </div>
            <p className="text-xs text-secondary-600">
              {formatBytes(metrics.memory.used)} / {formatBytes(metrics.memory.total)}
            </p>
          </div>

          {/* Storage */}
          <div className="card p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center">
                <Database className="w-5 h-5 text-purple-600 mr-2" />
                <h3 className="font-semibold text-secondary-900">Storage</h3>
              </div>
              <span className={`text-sm font-medium ${getStatusColor(metrics.storage.percentage, true)}`}>
                {metrics.storage.percentage.toFixed(1)}%
              </span>
            </div>
            <div className="w-full bg-secondary-200 rounded-full h-2 mb-2">
              <div 
                className={`h-2 rounded-full ${getProgressColor(metrics.storage.percentage, true)}`}
                style={{ width: `${metrics.storage.percentage}%` }}
              ></div>
            </div>
            <p className="text-xs text-secondary-600">
              {formatBytes(metrics.storage.used)} / {formatBytes(metrics.storage.total)}
            </p>
          </div>

          {/* Network */}
          <div className="card p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center">
                <Wifi className="w-5 h-5 text-orange-600 mr-2" />
                <h3 className="font-semibold text-secondary-900">Network</h3>
              </div>
              <span className="text-sm font-medium text-success-600">
                {metrics.network.latency.toFixed(0)}ms
              </span>
            </div>
            <div className="space-y-1">
              <div className="flex justify-between text-xs">
                <span className="text-secondary-600">In: {metrics.network.inbound.toFixed(1)} MB/s</span>
              </div>
              <div className="flex justify-between text-xs">
                <span className="text-secondary-600">Out: {metrics.network.outbound.toFixed(1)} MB/s</span>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Detailed Metrics */}
      {metrics && (
        <div className="grid lg:grid-cols-2 gap-6">
          {/* Database Performance */}
          <div className="card p-6">
            <h3 className="text-lg font-semibold text-secondary-900 mb-4">Database Performance</h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-secondary-600">Active Connections</span>
                <span className="font-medium text-secondary-900">{metrics.database.connections}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-secondary-600">Queries/sec</span>
                <span className="font-medium text-secondary-900">{metrics.database.queries}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-secondary-600">Avg Response Time</span>
                <span className="font-medium text-secondary-900">{metrics.database.responseTime.toFixed(1)}ms</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-secondary-600">Uptime</span>
                <span className={`font-medium ${getStatusColor(metrics.uptime)}`}>
                  {formatUptime(metrics.uptime)}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-secondary-600">Last Backup</span>
                <span className="font-medium text-secondary-900">{formatTime(metrics.lastBackup)}</span>
              </div>
            </div>
          </div>

          {/* System Alerts */}
          <div className="card p-6">
            <h3 className="text-lg font-semibold text-secondary-900 mb-4">System Alerts</h3>
            <div className="space-y-3 max-h-64 overflow-y-auto">
              {alerts.map((alert) => (
                <div key={alert.id} className={`p-3 rounded-lg border ${
                  alert.type === 'error' 
                    ? 'bg-error-50 border-error-200'
                    : alert.type === 'warning'
                    ? 'bg-warning-50 border-warning-200'
                    : 'bg-blue-50 border-blue-200'
                }`}>
                  <div className="flex items-start justify-between">
                    <div className="flex items-start space-x-2">
                      {alert.type === 'error' ? (
                        <XCircle className="w-4 h-4 text-error-600 mt-0.5" />
                      ) : alert.type === 'warning' ? (
                        <AlertTriangle className="w-4 h-4 text-warning-600 mt-0.5" />
                      ) : (
                        <CheckCircle className="w-4 h-4 text-blue-600 mt-0.5" />
                      )}
                      <div>
                        <p className={`text-sm font-medium ${
                          alert.type === 'error' 
                            ? 'text-error-900'
                            : alert.type === 'warning'
                            ? 'text-warning-900'
                            : 'text-blue-900'
                        }`}>
                          {alert.message}
                        </p>
                        <p className="text-xs text-secondary-500 mt-1">
                          {formatTime(alert.timestamp)}
                        </p>
                      </div>
                    </div>
                    {alert.resolved && (
                      <span className="text-xs text-success-600 font-medium">Resolved</span>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
