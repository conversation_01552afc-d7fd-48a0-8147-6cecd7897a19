import React, { useState, useEffect } from 'react';
import { 
  TrendingUp, 
  Users, 
  DollarSign, 
  FileText, 
  Calendar,
  Filter,
  Download,
  RefreshCw,
  Bar<PERSON>hart3,
  <PERSON><PERSON><PERSON>,
  Activity
} from 'lucide-react';
import { analyticsService, DashboardStats, UserAnalytics, PlatformAnalytics } from '../../services/analyticsService';
import { User } from '../../services/authService';
import StatsCard from './StatsCard';
import ChartCard from './ChartCard';
import DateRangePicker from './DateRangePicker';

interface AnalyticsDashboardProps {
  user: User;
}

export default function AnalyticsDashboard({ user }: AnalyticsDashboardProps) {
  const [dashboardStats, setDashboardStats] = useState<DashboardStats | null>(null);
  const [userAnalytics, setUserAnalytics] = useState<UserAnalytics | null>(null);
  const [platformAnalytics, setPlatformAnalytics] = useState<PlatformAnalytics | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [dateRange, setDateRange] = useState(() => analyticsService.getDateRange(30));
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    fetchAnalytics();
  }, [dateRange]);

  const fetchAnalytics = async () => {
    try {
      setLoading(true);
      setError('');

      const [dashboardData, userAnalyticsData, platformData] = await Promise.allSettled([
        analyticsService.getDashboardStats(),
        analyticsService.getUserAnalytics(dateRange.startDate, dateRange.endDate),
        user.role === 'admin' 
          ? analyticsService.getPlatformAnalytics(dateRange.startDate, dateRange.endDate)
          : Promise.resolve(null)
      ]);

      if (dashboardData.status === 'fulfilled') {
        setDashboardStats(dashboardData.value);
      }

      if (userAnalyticsData.status === 'fulfilled') {
        setUserAnalytics(userAnalyticsData.value);
      }

      if (platformData.status === 'fulfilled' && platformData.value) {
        setPlatformAnalytics(platformData.value);
      }

    } catch (error: any) {
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchAnalytics();
    setRefreshing(false);
  };

  const handleDateRangeChange = (startDate: string, endDate: string) => {
    setDateRange({ startDate, endDate });
  };

  const handleExportData = () => {
    // Implement data export functionality
    const data = {
      dashboardStats,
      userAnalytics,
      platformAnalytics,
      dateRange
    };
    
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `analytics-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  if (loading && !dashboardStats) {
    return (
      <div className="min-h-screen bg-secondary-50 flex items-center justify-center">
        <div className="text-center">
          <div className="w-12 h-12 border-4 border-primary-200 border-t-primary-600 rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-secondary-600">Loading analytics...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-secondary-50">
      <div className="container-custom py-8">
        {/* Header */}
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-8">
          <div>
            <h1 className="text-3xl font-display font-bold text-secondary-900 mb-2">
              Analytics Dashboard
            </h1>
            <p className="text-secondary-600">
              Track your performance and gain insights into your activity
            </p>
          </div>
          
          <div className="flex items-center space-x-4 mt-4 lg:mt-0">
            <DateRangePicker
              startDate={dateRange.startDate}
              endDate={dateRange.endDate}
              onChange={handleDateRangeChange}
            />
            
            <button
              onClick={handleRefresh}
              disabled={refreshing}
              className="btn-secondary"
            >
              <RefreshCw className={`w-5 h-5 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
              Refresh
            </button>
            
            <button
              onClick={handleExportData}
              className="btn-primary"
            >
              <Download className="w-5 h-5 mr-2" />
              Export
            </button>
          </div>
        </div>

        {/* Error Message */}
        {error && (
          <div className="mb-6 p-4 bg-error-50 border border-error-200 rounded-lg">
            <p className="text-error-700">{error}</p>
          </div>
        )}

        {/* Dashboard Stats */}
        {dashboardStats && (
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {dashboardStats.role === 'client' ? (
              <>
                <StatsCard
                  title="Total Projects"
                  value={dashboardStats.totalProjects || 0}
                  icon={FileText}
                  color="primary"
                />
                <StatsCard
                  title="Active Projects"
                  value={dashboardStats.activeProjects || 0}
                  icon={Activity}
                  color="warning"
                />
                <StatsCard
                  title="Completed Projects"
                  value={dashboardStats.completedProjects || 0}
                  icon={TrendingUp}
                  color="success"
                />
                <StatsCard
                  title="Total Bids Received"
                  value={dashboardStats.totalBids || 0}
                  icon={Users}
                  color="info"
                />
              </>
            ) : (
              <>
                <StatsCard
                  title="Total Bids"
                  value={dashboardStats.totalBids || 0}
                  icon={FileText}
                  color="primary"
                />
                <StatsCard
                  title="Accepted Bids"
                  value={dashboardStats.acceptedBids || 0}
                  icon={TrendingUp}
                  color="success"
                />
                <StatsCard
                  title="Pending Bids"
                  value={dashboardStats.pendingBids || 0}
                  icon={Activity}
                  color="warning"
                />
                <StatsCard
                  title="Active Projects"
                  value={dashboardStats.activeProjects || 0}
                  icon={Users}
                  color="info"
                />
              </>
            )}
          </div>
        )}

        {/* User Analytics Charts */}
        {userAnalytics && (
          <div className="grid lg:grid-cols-2 gap-6 mb-8">
            {/* Projects/Bids Over Time */}
            <ChartCard
              title={userAnalytics.user.role === 'client' ? 'Projects Over Time' : 'Bids Over Time'}
              type="line"
              data={userAnalytics.projects?.overTime || userAnalytics.bids?.overTime || []}
              color="primary"
            />

            {/* Status Distribution */}
            {userAnalytics.projects?.stats && (
              <ChartCard
                title="Project Status Distribution"
                type="doughnut"
                data={Object.entries(userAnalytics.projects.stats.status).map(([status, count]) => ({
                  label: status.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase()),
                  value: count
                }))}
                color="mixed"
              />
            )}

            {userAnalytics.bids && (
              <ChartCard
                title="Bid Success Rate"
                type="doughnut"
                data={[
                  { label: 'Accepted', value: userAnalytics.bids.accepted || 0 },
                  { label: 'Rejected', value: userAnalytics.bids.rejected || 0 },
                  { label: 'Pending', value: userAnalytics.bids.pending || 0 },
                  { label: 'Withdrawn', value: userAnalytics.bids.withdrawn || 0 }
                ]}
                color="mixed"
              />
            )}
          </div>
        )}

        {/* Platform Analytics (Admin Only) */}
        {user.role === 'admin' && platformAnalytics && (
          <>
            <div className="mb-6">
              <h2 className="text-2xl font-display font-bold text-secondary-900 mb-4">
                Platform Analytics
              </h2>
            </div>

            {/* Platform Stats */}
            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
              <StatsCard
                title="Total Users"
                value={platformAnalytics.users.total}
                subtitle={`${platformAnalytics.users.clients} clients, ${platformAnalytics.users.vendors} vendors`}
                icon={Users}
                color="primary"
              />
              <StatsCard
                title="Total Projects"
                value={platformAnalytics.projects.total}
                subtitle={`${analyticsService.formatPercentage(platformAnalytics.projects.completionRate)} completion rate`}
                icon={FileText}
                color="success"
              />
              <StatsCard
                title="Total Bids"
                value={platformAnalytics.bids.total}
                subtitle={`${analyticsService.formatPercentage(platformAnalytics.bids.acceptanceRate)} acceptance rate`}
                icon={TrendingUp}
                color="warning"
              />
              <StatsCard
                title="Avg Bids/Project"
                value={platformAnalytics.bids.avgPerProject.toFixed(1)}
                icon={BarChart3}
                color="info"
              />
            </div>

            {/* Platform Charts */}
            <div className="grid lg:grid-cols-2 gap-6 mb-8">
              <ChartCard
                title="User Growth"
                type="line"
                data={platformAnalytics.users.overTime}
                color="primary"
                multiSeries={true}
              />

              <ChartCard
                title="Project Categories"
                type="doughnut"
                data={Object.entries(platformAnalytics.projects.categories).map(([category, count]) => ({
                  label: category.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase()),
                  value: count
                }))}
                color="mixed"
              />

              <ChartCard
                title="Projects Over Time"
                type="bar"
                data={platformAnalytics.projects.overTime}
                color="success"
              />

              <ChartCard
                title="Project Status Distribution"
                type="doughnut"
                data={[
                  { label: 'Open', value: platformAnalytics.projects.open },
                  { label: 'In Progress', value: platformAnalytics.projects.inProgress },
                  { label: 'Completed', value: platformAnalytics.projects.completed }
                ]}
                color="mixed"
              />
            </div>
          </>
        )}

        {/* Insights */}
        {userAnalytics && (
          <div className="card p-6">
            <h3 className="text-lg font-semibold text-secondary-900 mb-4">
              Insights & Recommendations
            </h3>
            <div className="space-y-3">
              {analyticsService.generateInsights(userAnalytics).map((insight, index) => (
                <div key={index} className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-primary-600 rounded-full mt-2"></div>
                  <p className="text-secondary-700">{insight}</p>
                </div>
              ))}
              {analyticsService.generateInsights(userAnalytics).length === 0 && (
                <p className="text-secondary-600">
                  Keep using the platform to generate personalized insights!
                </p>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
