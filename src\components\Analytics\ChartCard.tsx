import React from 'react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
} from 'chart.js';
import { Line, Bar, Doughnut } from 'react-chartjs-2';
import { analyticsService } from '../../services/analyticsService';

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
);

interface ChartDataPoint {
  date?: string;
  count?: number;
  label?: string;
  value?: number;
  [key: string]: any;
}

interface ChartCardProps {
  title: string;
  type: 'line' | 'bar' | 'doughnut';
  data: ChartDataPoint[];
  color: 'primary' | 'success' | 'warning' | 'error' | 'info' | 'mixed';
  height?: number;
  multiSeries?: boolean;
  loading?: boolean;
}

export default function ChartCard({ 
  title, 
  type, 
  data, 
  color, 
  height = 300,
  multiSeries = false,
  loading = false 
}: ChartCardProps) {
  const colors = analyticsService.getChartColors();

  const getChartData = () => {
    if (type === 'doughnut') {
      const labels = data.map(item => item.label || '');
      const values = data.map(item => item.value || item.count || 0);
      
      return {
        labels,
        datasets: [
          {
            data: values,
            backgroundColor: color === 'mixed' 
              ? [...colors.primary, ...colors.success, ...colors.warning, ...colors.error, ...colors.info].slice(0, values.length)
              : colors[color as keyof typeof colors].slice(0, values.length),
            borderWidth: 0,
            hoverOffset: 4,
          },
        ],
      };
    }

    if (multiSeries && data.length > 0) {
      // Handle multi-series data (e.g., user growth with clients and vendors)
      const labels = data.map(item => {
        if (item.date) {
          return new Date(item.date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
        }
        return item.label || '';
      });

      const datasets = [];
      const seriesKeys = Object.keys(data[0]).filter(key => key !== 'date' && key !== 'label');

      seriesKeys.forEach((key, index) => {
        const colorArray = colors[color as keyof typeof colors] || colors.primary;
        datasets.push({
          label: key.charAt(0).toUpperCase() + key.slice(1),
          data: data.map(item => item[key] || 0),
          borderColor: colorArray[index % colorArray.length],
          backgroundColor: type === 'bar' ? colorArray[index % colorArray.length] + '80' : 'transparent',
          borderWidth: 2,
          fill: false,
          tension: 0.4,
        });
      });

      return { labels, datasets };
    }

    // Single series data
    const labels = data.map(item => {
      if (item.date) {
        return new Date(item.date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
      }
      return item.label || '';
    });

    const values = data.map(item => item.count || item.value || 0);
    const colorArray = colors[color as keyof typeof colors] || colors.primary;

    return {
      labels,
      datasets: [
        {
          label: title,
          data: values,
          borderColor: colorArray[0],
          backgroundColor: type === 'bar' ? colorArray[0] + '80' : 'transparent',
          borderWidth: 2,
          fill: false,
          tension: 0.4,
        },
      ],
    };
  };

  const getChartOptions = () => {
    const baseOptions = {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          position: 'top' as const,
          labels: {
            usePointStyle: true,
            padding: 20,
            font: {
              size: 12,
            },
          },
        },
        tooltip: {
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          titleColor: 'white',
          bodyColor: 'white',
          borderColor: 'rgba(255, 255, 255, 0.1)',
          borderWidth: 1,
          cornerRadius: 8,
          displayColors: true,
          callbacks: {
            label: function(context: any) {
              if (type === 'doughnut') {
                const total = context.dataset.data.reduce((a: number, b: number) => a + b, 0);
                const percentage = ((context.parsed * 100) / total).toFixed(1);
                return `${context.label}: ${context.parsed} (${percentage}%)`;
              }
              return `${context.dataset.label}: ${context.parsed}`;
            }
          }
        },
      },
    };

    if (type === 'doughnut') {
      return {
        ...baseOptions,
        cutout: '60%',
        plugins: {
          ...baseOptions.plugins,
          legend: {
            ...baseOptions.plugins.legend,
            position: 'right' as const,
          },
        },
      };
    }

    return {
      ...baseOptions,
      scales: {
        x: {
          grid: {
            display: false,
          },
          ticks: {
            font: {
              size: 11,
            },
          },
        },
        y: {
          beginAtZero: true,
          grid: {
            color: 'rgba(0, 0, 0, 0.05)',
          },
          ticks: {
            font: {
              size: 11,
            },
          },
        },
      },
    };
  };

  const renderChart = () => {
    const chartData = getChartData();
    const options = getChartOptions();

    switch (type) {
      case 'line':
        return <Line data={chartData} options={options} />;
      case 'bar':
        return <Bar data={chartData} options={options} />;
      case 'doughnut':
        return <Doughnut data={chartData} options={options} />;
      default:
        return null;
    }
  };

  if (loading) {
    return (
      <div className="card p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-secondary-200 rounded w-1/3 mb-4"></div>
          <div className="h-64 bg-secondary-100 rounded"></div>
        </div>
      </div>
    );
  }

  if (!data || data.length === 0) {
    return (
      <div className="card p-6">
        <h3 className="text-lg font-semibold text-secondary-900 mb-4">{title}</h3>
        <div className="flex items-center justify-center h-64 text-secondary-500">
          <div className="text-center">
            <div className="w-16 h-16 bg-secondary-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
            </div>
            <p className="text-sm">No data available</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="card p-6">
      <h3 className="text-lg font-semibold text-secondary-900 mb-4">{title}</h3>
      <div style={{ height: `${height}px` }}>
        {renderChart()}
      </div>
    </div>
  );
}
