import React, { useState } from 'react';
import { Calendar, ChevronDown } from 'lucide-react';
import { analyticsService } from '../../services/analyticsService';

interface DateRangePickerProps {
  startDate: string;
  endDate: string;
  onChange: (startDate: string, endDate: string) => void;
}

export default function DateRangePicker({ startDate, endDate, onChange }: DateRangePickerProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [customStartDate, setCustomStartDate] = useState(startDate);
  const [customEndDate, setCustomEndDate] = useState(endDate);

  const presetRanges = [
    { label: 'Last 7 days', getValue: () => analyticsService.getDateRange(7) },
    { label: 'Last 30 days', getValue: () => analyticsService.getDateRange(30) },
    { label: 'Last 90 days', getValue: () => analyticsService.getDateRange(90) },
    { label: 'Last 6 months', getValue: () => analyticsService.getMonthRange(6) },
    { label: 'Last year', getValue: () => analyticsService.getMonthRange(12) },
  ];

  const getCurrentRangeLabel = () => {
    const preset = presetRanges.find(range => {
      const { startDate: presetStart, endDate: presetEnd } = range.getValue();
      return presetStart === startDate && presetEnd === endDate;
    });
    
    if (preset) {
      return preset.label;
    }
    
    return `${new Date(startDate).toLocaleDateString()} - ${new Date(endDate).toLocaleDateString()}`;
  };

  const handlePresetSelect = (preset: typeof presetRanges[0]) => {
    const { startDate: newStart, endDate: newEnd } = preset.getValue();
    onChange(newStart, newEnd);
    setIsOpen(false);
  };

  const handleCustomRangeApply = () => {
    if (customStartDate && customEndDate && customStartDate <= customEndDate) {
      onChange(customStartDate, customEndDate);
      setIsOpen(false);
    }
  };

  const handleCustomRangeCancel = () => {
    setCustomStartDate(startDate);
    setCustomEndDate(endDate);
    setIsOpen(false);
  };

  return (
    <div className="relative">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="btn-secondary flex items-center space-x-2"
      >
        <Calendar className="w-4 h-4" />
        <span className="text-sm">{getCurrentRangeLabel()}</span>
        <ChevronDown className={`w-4 h-4 transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`} />
      </button>

      {isOpen && (
        <>
          {/* Backdrop */}
          <div 
            className="fixed inset-0 z-10" 
            onClick={() => setIsOpen(false)}
          />
          
          {/* Dropdown */}
          <div className="absolute right-0 top-full mt-2 w-80 bg-white border border-secondary-200 rounded-lg shadow-lg z-20">
            <div className="p-4">
              <h3 className="text-sm font-semibold text-secondary-900 mb-3">Select Date Range</h3>
              
              {/* Preset Ranges */}
              <div className="space-y-1 mb-4">
                {presetRanges.map((preset, index) => (
                  <button
                    key={index}
                    onClick={() => handlePresetSelect(preset)}
                    className="w-full text-left px-3 py-2 text-sm text-secondary-700 hover:bg-secondary-50 rounded-md transition-colors duration-200"
                  >
                    {preset.label}
                  </button>
                ))}
              </div>

              {/* Custom Range */}
              <div className="border-t border-secondary-200 pt-4">
                <h4 className="text-sm font-medium text-secondary-900 mb-3">Custom Range</h4>
                
                <div className="grid grid-cols-2 gap-3 mb-4">
                  <div>
                    <label className="block text-xs font-medium text-secondary-700 mb-1">
                      Start Date
                    </label>
                    <input
                      type="date"
                      value={customStartDate}
                      onChange={(e) => setCustomStartDate(e.target.value)}
                      max={customEndDate}
                      className="input-field text-sm"
                    />
                  </div>
                  <div>
                    <label className="block text-xs font-medium text-secondary-700 mb-1">
                      End Date
                    </label>
                    <input
                      type="date"
                      value={customEndDate}
                      onChange={(e) => setCustomEndDate(e.target.value)}
                      min={customStartDate}
                      max={new Date().toISOString().split('T')[0]}
                      className="input-field text-sm"
                    />
                  </div>
                </div>

                <div className="flex justify-end space-x-2">
                  <button
                    onClick={handleCustomRangeCancel}
                    className="px-3 py-1.5 text-sm text-secondary-600 hover:text-secondary-800 transition-colors duration-200"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleCustomRangeApply}
                    disabled={!customStartDate || !customEndDate || customStartDate > customEndDate}
                    className="btn-primary text-sm px-3 py-1.5 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Apply
                  </button>
                </div>
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  );
}
