import React from 'react';
import { LucideIcon } from 'lucide-react';
import { analyticsService } from '../../services/analyticsService';

interface StatsCardProps {
  title: string;
  value: number | string;
  subtitle?: string;
  icon: LucideIcon;
  color: 'primary' | 'success' | 'warning' | 'error' | 'info';
  trend?: {
    value: number;
    isPositive: boolean;
  };
  loading?: boolean;
}

export default function StatsCard({ 
  title, 
  value, 
  subtitle, 
  icon: Icon, 
  color, 
  trend,
  loading = false 
}: StatsCardProps) {
  const getColorClasses = (color: string) => {
    const colorMap = {
      primary: {
        bg: 'bg-primary-100',
        text: 'text-primary-600',
        accent: 'text-primary-700'
      },
      success: {
        bg: 'bg-success-100',
        text: 'text-success-600',
        accent: 'text-success-700'
      },
      warning: {
        bg: 'bg-warning-100',
        text: 'text-warning-600',
        accent: 'text-warning-700'
      },
      error: {
        bg: 'bg-error-100',
        text: 'text-error-600',
        accent: 'text-error-700'
      },
      info: {
        bg: 'bg-blue-100',
        text: 'text-blue-600',
        accent: 'text-blue-700'
      }
    };
    return colorMap[color as keyof typeof colorMap] || colorMap.primary;
  };

  const colors = getColorClasses(color);

  const formatValue = (val: number | string): string => {
    if (typeof val === 'string') return val;
    if (val >= 1000000) return `${(val / 1000000).toFixed(1)}M`;
    if (val >= 1000) return `${(val / 1000).toFixed(1)}K`;
    return val.toLocaleString();
  };

  if (loading) {
    return (
      <div className="card p-6 animate-pulse">
        <div className="flex items-center justify-between">
          <div className="flex-1">
            <div className="h-4 bg-secondary-200 rounded w-3/4 mb-2"></div>
            <div className="h-8 bg-secondary-200 rounded w-1/2 mb-2"></div>
            <div className="h-3 bg-secondary-200 rounded w-2/3"></div>
          </div>
          <div className={`w-12 h-12 ${colors.bg} rounded-lg`}></div>
        </div>
      </div>
    );
  }

  return (
    <div className="card p-6 hover:shadow-medium transition-shadow duration-200">
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <p className="text-sm font-medium text-secondary-600 mb-1">{title}</p>
          <p className={`text-2xl font-bold ${colors.accent} mb-1`}>
            {formatValue(value)}
          </p>
          
          {subtitle && (
            <p className="text-xs text-secondary-500">{subtitle}</p>
          )}
          
          {trend && (
            <div className="flex items-center mt-2">
              <span className={`text-xs font-medium ${
                trend.isPositive ? 'text-success-600' : 'text-error-600'
              }`}>
                {trend.isPositive ? '↗' : '↘'} {analyticsService.formatPercentage(Math.abs(trend.value))}
              </span>
              <span className="text-xs text-secondary-500 ml-1">vs last period</span>
            </div>
          )}
        </div>
        
        <div className={`w-12 h-12 ${colors.bg} rounded-lg flex items-center justify-center`}>
          <Icon className={`w-6 h-6 ${colors.text}`} />
        </div>
      </div>
    </div>
  );
}
