import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { screen, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { renderWithProviders, mockUser, fillForm } from '../../../test/utils'
import AuthModal from '../AuthModal'
import { authService } from '../../../services/authService'

// Mock the auth service
vi.mock('../../../services/authService', () => ({
  authService: {
    login: vi.fn(),
    register: vi.fn(),
    forgotPassword: vi.fn(),
    resetPassword: vi.fn(),
    verifyEmail: vi.fn()
  }
}))

const mockedAuthService = vi.mocked(authService)

describe('AuthModal Integration Tests', () => {
  const defaultProps = {
    isOpen: true,
    onClose: vi.fn(),
    onSuccess: vi.fn(),
    initialMode: 'login' as const
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('Login Flow', () => {
    it('should complete successful login flow', async () => {
      const user = userEvent.setup()
      mockedAuthService.login.mockResolvedValue({
        user: mockUser,
        token: 'mock-token'
      })

      renderWithProviders(<AuthModal {...defaultProps} />)

      // Fill in login form
      await user.type(screen.getByLabelText(/email/i), '<EMAIL>')
      await user.type(screen.getByLabelText(/password/i), 'password123')

      // Submit form
      await user.click(screen.getByRole('button', { name: /sign in/i }))

      // Wait for API call
      await waitFor(() => {
        expect(mockedAuthService.login).toHaveBeenCalledWith({
          email: '<EMAIL>',
          password: 'password123'
        })
      })

      // Check success callback
      expect(defaultProps.onSuccess).toHaveBeenCalledWith({
        user: mockUser,
        token: 'mock-token'
      })
    })

    it('should handle login errors gracefully', async () => {
      const user = userEvent.setup()
      const errorMessage = 'Invalid credentials'
      mockedAuthService.login.mockRejectedValue(new Error(errorMessage))

      renderWithProviders(<AuthModal {...defaultProps} />)

      // Fill in login form
      await user.type(screen.getByLabelText(/email/i), '<EMAIL>')
      await user.type(screen.getByLabelText(/password/i), 'wrongpassword')

      // Submit form
      await user.click(screen.getByRole('button', { name: /sign in/i }))

      // Wait for error message
      await waitFor(() => {
        expect(screen.getByText(errorMessage)).toBeInTheDocument()
      })

      // Ensure success callback was not called
      expect(defaultProps.onSuccess).not.toHaveBeenCalled()
    })

    it('should validate required fields', async () => {
      const user = userEvent.setup()
      renderWithProviders(<AuthModal {...defaultProps} />)

      // Try to submit without filling fields
      await user.click(screen.getByRole('button', { name: /sign in/i }))

      // Check for validation messages
      expect(screen.getByText(/email is required/i)).toBeInTheDocument()
      expect(screen.getByText(/password is required/i)).toBeInTheDocument()

      // Ensure API was not called
      expect(mockedAuthService.login).not.toHaveBeenCalled()
    })

    it('should validate email format', async () => {
      const user = userEvent.setup()
      renderWithProviders(<AuthModal {...defaultProps} />)

      // Enter invalid email
      await user.type(screen.getByLabelText(/email/i), 'invalid-email')
      await user.type(screen.getByLabelText(/password/i), 'password123')
      await user.click(screen.getByRole('button', { name: /sign in/i }))

      // Check for validation message
      expect(screen.getByText(/please enter a valid email/i)).toBeInTheDocument()
      expect(mockedAuthService.login).not.toHaveBeenCalled()
    })
  })

  describe('Registration Flow', () => {
    it('should complete successful registration flow', async () => {
      const user = userEvent.setup()
      mockedAuthService.register.mockResolvedValue({
        user: mockUser,
        token: 'mock-token'
      })

      renderWithProviders(
        <AuthModal {...defaultProps} initialMode="register" />
      )

      // Fill in registration form
      await user.type(screen.getByLabelText(/full name/i), 'John Doe')
      await user.type(screen.getByLabelText(/email/i), '<EMAIL>')
      await user.type(screen.getByLabelText(/password/i), 'password123')
      await user.type(screen.getByLabelText(/confirm password/i), 'password123')
      
      // Select role
      await user.selectOptions(screen.getByLabelText(/role/i), 'client')

      // Submit form
      await user.click(screen.getByRole('button', { name: /create account/i }))

      // Wait for API call
      await waitFor(() => {
        expect(mockedAuthService.register).toHaveBeenCalledWith({
          name: 'John Doe',
          email: '<EMAIL>',
          password: 'password123',
          role: 'client'
        })
      })

      expect(defaultProps.onSuccess).toHaveBeenCalled()
    })

    it('should validate password confirmation', async () => {
      const user = userEvent.setup()
      renderWithProviders(
        <AuthModal {...defaultProps} initialMode="register" />
      )

      // Fill form with mismatched passwords
      await user.type(screen.getByLabelText(/full name/i), 'John Doe')
      await user.type(screen.getByLabelText(/email/i), '<EMAIL>')
      await user.type(screen.getByLabelText(/password/i), 'password123')
      await user.type(screen.getByLabelText(/confirm password/i), 'different')

      await user.click(screen.getByRole('button', { name: /create account/i }))

      // Check for validation message
      expect(screen.getByText(/passwords do not match/i)).toBeInTheDocument()
      expect(mockedAuthService.register).not.toHaveBeenCalled()
    })

    it('should validate password strength', async () => {
      const user = userEvent.setup()
      renderWithProviders(
        <AuthModal {...defaultProps} initialMode="register" />
      )

      // Enter weak password
      await user.type(screen.getByLabelText(/password/i), '123')

      // Check for validation message
      expect(screen.getByText(/password must be at least 8 characters/i)).toBeInTheDocument()
    })
  })

  describe('Forgot Password Flow', () => {
    it('should handle forgot password request', async () => {
      const user = userEvent.setup()
      mockedAuthService.forgotPassword.mockResolvedValue(undefined)

      renderWithProviders(<AuthModal {...defaultProps} />)

      // Click forgot password link
      await user.click(screen.getByText(/forgot password/i))

      // Fill email
      await user.type(screen.getByLabelText(/email/i), '<EMAIL>')

      // Submit
      await user.click(screen.getByRole('button', { name: /send reset link/i }))

      // Wait for API call
      await waitFor(() => {
        expect(mockedAuthService.forgotPassword).toHaveBeenCalledWith('<EMAIL>')
      })

      // Check success message
      expect(screen.getByText(/reset link sent/i)).toBeInTheDocument()
    })
  })

  describe('Mode Switching', () => {
    it('should switch between login and register modes', async () => {
      const user = userEvent.setup()
      renderWithProviders(<AuthModal {...defaultProps} />)

      // Initially in login mode
      expect(screen.getByRole('button', { name: /sign in/i })).toBeInTheDocument()

      // Switch to register
      await user.click(screen.getByText(/create account/i))

      // Now in register mode
      expect(screen.getByRole('button', { name: /create account/i })).toBeInTheDocument()
      expect(screen.getByLabelText(/full name/i)).toBeInTheDocument()

      // Switch back to login
      await user.click(screen.getByText(/sign in/i))

      // Back in login mode
      expect(screen.getByRole('button', { name: /sign in/i })).toBeInTheDocument()
      expect(screen.queryByLabelText(/full name/i)).not.toBeInTheDocument()
    })
  })

  describe('Loading States', () => {
    it('should show loading state during login', async () => {
      const user = userEvent.setup()
      // Create a promise that we can control
      let resolveLogin: (value: any) => void
      const loginPromise = new Promise(resolve => {
        resolveLogin = resolve
      })
      mockedAuthService.login.mockReturnValue(loginPromise)

      renderWithProviders(<AuthModal {...defaultProps} />)

      // Fill and submit form
      await user.type(screen.getByLabelText(/email/i), '<EMAIL>')
      await user.type(screen.getByLabelText(/password/i), 'password123')
      await user.click(screen.getByRole('button', { name: /sign in/i }))

      // Check loading state
      expect(screen.getByText(/signing in/i)).toBeInTheDocument()
      expect(screen.getByRole('button', { name: /signing in/i })).toBeDisabled()

      // Resolve the promise
      resolveLogin!({ user: mockUser, token: 'mock-token' })

      // Wait for loading to finish
      await waitFor(() => {
        expect(screen.queryByText(/signing in/i)).not.toBeInTheDocument()
      })
    })
  })

  describe('Accessibility', () => {
    it('should have proper ARIA labels and roles', () => {
      renderWithProviders(<AuthModal {...defaultProps} />)

      // Check modal has proper role
      expect(screen.getByRole('dialog')).toBeInTheDocument()

      // Check form has proper labels
      expect(screen.getByLabelText(/email/i)).toBeInTheDocument()
      expect(screen.getByLabelText(/password/i)).toBeInTheDocument()
    })

    it('should handle keyboard navigation', async () => {
      const user = userEvent.setup()
      renderWithProviders(<AuthModal {...defaultProps} />)

      // Tab through form elements
      await user.tab()
      expect(screen.getByLabelText(/email/i)).toHaveFocus()

      await user.tab()
      expect(screen.getByLabelText(/password/i)).toHaveFocus()

      await user.tab()
      expect(screen.getByRole('button', { name: /sign in/i })).toHaveFocus()
    })

    it('should close modal on Escape key', async () => {
      const user = userEvent.setup()
      renderWithProviders(<AuthModal {...defaultProps} />)

      await user.keyboard('{Escape}')

      expect(defaultProps.onClose).toHaveBeenCalled()
    })
  })
})
