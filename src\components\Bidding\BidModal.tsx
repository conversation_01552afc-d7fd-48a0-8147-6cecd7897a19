import React, { useState } from 'react';
import { X, DollarSign, Calendar, FileText, Clock, TrendingUp, AlertCircle } from 'lucide-react';

interface BidModalProps {
  isOpen: boolean;
  onClose: () => void;
  project: {
    id: string;
    title: string;
    budget: number;
    deadline: string;
    description: string;
  };
  onSubmitBid: (bidData: BidData) => void;
}

interface BidData {
  amount: number;
  timeline: number;
  proposal: string;
  milestones: Milestone[];
}

interface Milestone {
  title: string;
  description: string;
  amount: number;
  timeline: number;
}

export default function BidModal({ isOpen, onClose, project, onSubmitBid }: BidModalProps) {
  const [bidData, setBidData] = useState<BidData>({
    amount: project.budget * 0.8, // Start with 80% of budget
    timeline: 30,
    proposal: '',
    milestones: [
      { title: 'Project Setup & Planning', description: '', amount: 0, timeline: 7 },
      { title: 'Development Phase', description: '', amount: 0, timeline: 14 },
      { title: 'Testing & Delivery', description: '', amount: 0, timeline: 7 }
    ]
  });

  const [activeTab, setActiveTab] = useState<'overview' | 'milestones'>('overview');

  if (!isOpen) return null;

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmitBid(bidData);
    onClose();
  };

  const updateMilestone = (index: number, field: keyof Milestone, value: string | number) => {
    const newMilestones = [...bidData.milestones];
    newMilestones[index] = { ...newMilestones[index], [field]: value };
    setBidData({ ...bidData, milestones: newMilestones });
  };

  const addMilestone = () => {
    setBidData({
      ...bidData,
      milestones: [
        ...bidData.milestones,
        { title: '', description: '', amount: 0, timeline: 7 }
      ]
    });
  };

  const removeMilestone = (index: number) => {
    if (bidData.milestones.length > 1) {
      const newMilestones = bidData.milestones.filter((_, i) => i !== index);
      setBidData({ ...bidData, milestones: newMilestones });
    }
  };

  const totalMilestoneAmount = bidData.milestones.reduce((sum, milestone) => sum + milestone.amount, 0);
  const totalMilestoneTimeline = bidData.milestones.reduce((sum, milestone) => sum + milestone.timeline, 0);

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-2xl shadow-hard max-w-4xl w-full max-h-[90vh] overflow-y-auto animate-bounce-in">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-secondary-100">
          <div>
            <h2 className="text-xl font-display font-bold text-secondary-900">Submit Your Bid</h2>
            <p className="text-secondary-600 mt-1">{project.title}</p>
          </div>
          <button 
            onClick={onClose}
            className="p-2 text-secondary-400 hover:text-secondary-600 transition-colors duration-200"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Project Summary */}
        <div className="p-6 bg-secondary-50 border-b border-secondary-100">
          <div className="grid md:grid-cols-3 gap-4">
            <div className="flex items-center space-x-2">
              <DollarSign className="w-5 h-5 text-primary-600" />
              <div>
                <p className="text-sm text-secondary-600">Client Budget</p>
                <p className="font-semibold text-secondary-900">${project.budget.toLocaleString()}</p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Calendar className="w-5 h-5 text-warning-600" />
              <div>
                <p className="text-sm text-secondary-600">Deadline</p>
                <p className="font-semibold text-secondary-900">{project.deadline}</p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <TrendingUp className="w-5 h-5 text-success-600" />
              <div>
                <p className="text-sm text-secondary-600">Your Bid</p>
                <p className="font-semibold text-primary-600">${bidData.amount.toLocaleString()}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className="flex border-b border-secondary-100">
          <button
            onClick={() => setActiveTab('overview')}
            className={`px-6 py-3 font-semibold transition-colors duration-200 ${
              activeTab === 'overview'
                ? 'text-primary-600 border-b-2 border-primary-600'
                : 'text-secondary-600 hover:text-secondary-900'
            }`}
          >
            Overview
          </button>
          <button
            onClick={() => setActiveTab('milestones')}
            className={`px-6 py-3 font-semibold transition-colors duration-200 ${
              activeTab === 'milestones'
                ? 'text-primary-600 border-b-2 border-primary-600'
                : 'text-secondary-600 hover:text-secondary-900'
            }`}
          >
            Milestones
          </button>
        </div>

        <form onSubmit={handleSubmit}>
          <div className="p-6">
            {activeTab === 'overview' && (
              <div className="space-y-6">
                {/* Bid Amount */}
                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-semibold text-secondary-700 mb-2">
                      Bid Amount ($)
                    </label>
                    <div className="relative">
                      <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-secondary-400" />
                      <input
                        type="number"
                        value={bidData.amount}
                        onChange={(e) => setBidData({ ...bidData, amount: Number(e.target.value) })}
                        className="input-field pl-10"
                        placeholder="Enter your bid amount"
                        min="1"
                        required
                      />
                    </div>
                    <p className="text-xs text-secondary-500 mt-1">
                      {bidData.amount < project.budget * 0.5 && (
                        <span className="text-warning-600">⚠️ Your bid is significantly lower than the budget</span>
                      )}
                      {bidData.amount > project.budget * 1.2 && (
                        <span className="text-error-600">⚠️ Your bid exceeds the client's budget</span>
                      )}
                    </p>
                  </div>

                  <div>
                    <label className="block text-sm font-semibold text-secondary-700 mb-2">
                      Timeline (days)
                    </label>
                    <div className="relative">
                      <Clock className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-secondary-400" />
                      <input
                        type="number"
                        value={bidData.timeline}
                        onChange={(e) => setBidData({ ...bidData, timeline: Number(e.target.value) })}
                        className="input-field pl-10"
                        placeholder="Project timeline in days"
                        min="1"
                        required
                      />
                    </div>
                  </div>
                </div>

                {/* Proposal */}
                <div>
                  <label className="block text-sm font-semibold text-secondary-700 mb-2">
                    Project Proposal
                  </label>
                  <textarea
                    value={bidData.proposal}
                    onChange={(e) => setBidData({ ...bidData, proposal: e.target.value })}
                    className="input-field h-32 resize-none"
                    placeholder="Describe your approach, experience, and why you're the best fit for this project..."
                    required
                  />
                  <p className="text-xs text-secondary-500 mt-1">
                    {bidData.proposal.length}/1000 characters
                  </p>
                </div>

                {/* Competitive Analysis */}
                <div className="bg-primary-50 rounded-lg p-4">
                  <div className="flex items-start space-x-3">
                    <TrendingUp className="w-5 h-5 text-primary-600 mt-0.5" />
                    <div>
                      <h4 className="font-semibold text-primary-900 mb-2">Competitive Positioning</h4>
                      <div className="grid md:grid-cols-2 gap-4 text-sm">
                        <div>
                          <p className="text-primary-700">Your bid vs. budget:</p>
                          <p className="font-semibold text-primary-900">
                            {((bidData.amount / project.budget) * 100).toFixed(0)}% of client budget
                          </p>
                        </div>
                        <div>
                          <p className="text-primary-700">Estimated market range:</p>
                          <p className="font-semibold text-primary-900">
                            ${(project.budget * 0.7).toLocaleString()} - ${(project.budget * 1.1).toLocaleString()}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'milestones' && (
              <div className="space-y-6">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold text-secondary-900">Project Milestones</h3>
                  <button
                    type="button"
                    onClick={addMilestone}
                    className="btn-secondary text-sm py-2 px-3"
                  >
                    Add Milestone
                  </button>
                </div>

                <div className="space-y-4">
                  {bidData.milestones.map((milestone, index) => (
                    <div key={index} className="border border-secondary-200 rounded-lg p-4">
                      <div className="flex items-center justify-between mb-3">
                        <h4 className="font-semibold text-secondary-900">Milestone {index + 1}</h4>
                        {bidData.milestones.length > 1 && (
                          <button
                            type="button"
                            onClick={() => removeMilestone(index)}
                            className="text-error-600 hover:text-error-700 text-sm"
                          >
                            Remove
                          </button>
                        )}
                      </div>
                      
                      <div className="grid md:grid-cols-2 gap-4 mb-3">
                        <input
                          type="text"
                          value={milestone.title}
                          onChange={(e) => updateMilestone(index, 'title', e.target.value)}
                          className="input-field"
                          placeholder="Milestone title"
                          required
                        />
                        <div className="grid grid-cols-2 gap-2">
                          <input
                            type="number"
                            value={milestone.amount}
                            onChange={(e) => updateMilestone(index, 'amount', Number(e.target.value))}
                            className="input-field"
                            placeholder="Amount ($)"
                            min="0"
                            required
                          />
                          <input
                            type="number"
                            value={milestone.timeline}
                            onChange={(e) => updateMilestone(index, 'timeline', Number(e.target.value))}
                            className="input-field"
                            placeholder="Days"
                            min="1"
                            required
                          />
                        </div>
                      </div>
                      
                      <textarea
                        value={milestone.description}
                        onChange={(e) => updateMilestone(index, 'description', e.target.value)}
                        className="input-field h-20 resize-none"
                        placeholder="Milestone description and deliverables..."
                      />
                    </div>
                  ))}
                </div>

                {/* Milestone Summary */}
                <div className="bg-secondary-50 rounded-lg p-4">
                  <h4 className="font-semibold text-secondary-900 mb-3">Summary</h4>
                  <div className="grid md:grid-cols-2 gap-4 text-sm">
                    <div>
                      <p className="text-secondary-600">Total Amount:</p>
                      <p className={`font-semibold ${
                        totalMilestoneAmount === bidData.amount ? 'text-success-600' : 'text-error-600'
                      }`}>
                        ${totalMilestoneAmount.toLocaleString()}
                      </p>
                    </div>
                    <div>
                      <p className="text-secondary-600">Total Timeline:</p>
                      <p className={`font-semibold ${
                        totalMilestoneTimeline === bidData.timeline ? 'text-success-600' : 'text-warning-600'
                      }`}>
                        {totalMilestoneTimeline} days
                      </p>
                    </div>
                  </div>
                  
                  {totalMilestoneAmount !== bidData.amount && (
                    <div className="flex items-center space-x-2 mt-2 text-error-600">
                      <AlertCircle className="w-4 h-4" />
                      <span className="text-sm">Milestone amounts don't match your total bid</span>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>

          {/* Footer */}
          <div className="flex items-center justify-between p-6 border-t border-secondary-100 bg-secondary-50">
            <div className="text-sm text-secondary-600">
              <p>By submitting this bid, you agree to our terms and conditions.</p>
            </div>
            <div className="flex space-x-3">
              <button
                type="button"
                onClick={onClose}
                className="btn-secondary"
              >
                Cancel
              </button>
              <button
                type="submit"
                className="btn-primary"
                disabled={totalMilestoneAmount !== bidData.amount && activeTab === 'milestones'}
              >
                Submit Bid
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>
  );
}
