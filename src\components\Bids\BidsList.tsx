import React, { useState, useEffect } from 'react';
import { 
  Star, 
  Clock, 
  DollarSign, 
  User, 
  CheckCircle, 
  XCircle, 
  Eye,
  MessageSquare,
  TrendingUp,
  Award,
  Calendar
} from 'lucide-react';
import { bidService, Bid } from '../../services/bidService';
import { Project } from '../../services/projectService';
import { User as UserType } from '../../services/authService';

interface BidsListProps {
  project: Project;
  user: UserType;
  onBidUpdate?: () => void;
}

export default function BidsList({ project, user, onBidUpdate }: BidsListProps) {
  const [bids, setBids] = useState<Bid[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [selectedBid, setSelectedBid] = useState<Bid | null>(null);
  const [showBidModal, setShowBidModal] = useState(false);

  useEffect(() => {
    fetchBids();
  }, [project._id]);

  const fetchBids = async () => {
    try {
      setLoading(true);
      const bidsData = await bidService.getProjectBids(project._id);
      setBids(bidsData);
    } catch (error: any) {
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleAcceptBid = async (bidId: string) => {
    if (!confirm('Are you sure you want to accept this bid? This will reject all other bids.')) {
      return;
    }

    try {
      await bidService.updateBidStatus(bidId, 'accepted');
      await fetchBids();
      onBidUpdate?.();
    } catch (error: any) {
      alert(error.message);
    }
  };

  const handleRejectBid = async (bidId: string) => {
    if (!confirm('Are you sure you want to reject this bid?')) {
      return;
    }

    try {
      await bidService.updateBidStatus(bidId, 'rejected');
      await fetchBids();
      onBidUpdate?.();
    } catch (error: any) {
      alert(error.message);
    }
  };

  const isProjectOwner = user._id === (typeof project.client === 'string' ? project.client : project.client._id);
  const canManageBids = isProjectOwner && project.status === 'open';

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="w-8 h-8 border-4 border-primary-200 border-t-primary-600 rounded-full animate-spin"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4 bg-error-50 border border-error-200 rounded-lg">
        <p className="text-error-700">{error}</p>
      </div>
    );
  }

  if (bids.length === 0) {
    return (
      <div className="text-center py-8">
        <div className="w-16 h-16 bg-secondary-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <DollarSign className="w-8 h-8 text-secondary-400" />
        </div>
        <h3 className="text-lg font-semibold text-secondary-900 mb-2">No Bids Yet</h3>
        <p className="text-secondary-600">
          {isProjectOwner 
            ? "No vendors have submitted bids for this project yet." 
            : "Be the first to submit a bid for this project!"
          }
        </p>
      </div>
    );
  }

  // Sort bids by score for project owners, by date for others
  const sortedBids = [...bids].sort((a, b) => {
    if (isProjectOwner) {
      const scoreA = bidService.calculateBidScore(a, project.budget);
      const scoreB = bidService.calculateBidScore(b, project.budget);
      return scoreB - scoreA; // Higher score first
    }
    return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
  });

  return (
    <div className="space-y-6">
      {/* Bids Summary */}
      <div className="grid md:grid-cols-4 gap-4">
        <div className="card p-4 text-center">
          <div className="text-2xl font-bold text-primary-600">{bids.length}</div>
          <div className="text-sm text-secondary-600">Total Bids</div>
        </div>
        <div className="card p-4 text-center">
          <div className="text-2xl font-bold text-success-600">
            {bidService.formatAmount(Math.min(...bids.map(b => b.amount)))}
          </div>
          <div className="text-sm text-secondary-600">Lowest Bid</div>
        </div>
        <div className="card p-4 text-center">
          <div className="text-2xl font-bold text-warning-600">
            {bidService.formatAmount(bids.reduce((sum, b) => sum + b.amount, 0) / bids.length)}
          </div>
          <div className="text-sm text-secondary-600">Average Bid</div>
        </div>
        <div className="card p-4 text-center">
          <div className="text-2xl font-bold text-secondary-600">
            {Math.min(...bids.map(b => b.deliveryTime))} days
          </div>
          <div className="text-sm text-secondary-600">Fastest Delivery</div>
        </div>
      </div>

      {/* Bids List */}
      <div className="space-y-4">
        {sortedBids.map((bid) => (
          <BidCard
            key={bid._id}
            bid={bid}
            project={project}
            user={user}
            canManage={canManageBids}
            onAccept={() => handleAcceptBid(bid._id)}
            onReject={() => handleRejectBid(bid._id)}
            onViewDetails={() => {
              setSelectedBid(bid);
              setShowBidModal(true);
            }}
          />
        ))}
      </div>

      {/* Bid Details Modal */}
      {showBidModal && selectedBid && (
        <BidDetailsModal
          bid={selectedBid}
          project={project}
          user={user}
          onClose={() => {
            setShowBidModal(false);
            setSelectedBid(null);
          }}
          onAccept={canManageBids ? () => handleAcceptBid(selectedBid._id) : undefined}
          onReject={canManageBids ? () => handleRejectBid(selectedBid._id) : undefined}
        />
      )}
    </div>
  );
}

// Bid Card Component
interface BidCardProps {
  bid: Bid;
  project: Project;
  user: UserType;
  canManage: boolean;
  onAccept: () => void;
  onReject: () => void;
  onViewDetails: () => void;
}

function BidCard({ bid, project, user, canManage, onAccept, onReject, onViewDetails }: BidCardProps) {
  const score = bidService.calculateBidScore(bid, project.budget);
  const recommendation = bidService.getBidRecommendation(score);
  const isProjectOwner = user._id === (typeof project.client === 'string' ? project.client : project.client._id);

  return (
    <div className={`card p-6 transition-all duration-200 hover:shadow-medium ${
      bid.status === 'accepted' ? 'ring-2 ring-success-200 bg-success-50' : ''
    }`}>
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center space-x-4">
          {/* Vendor Avatar */}
          <div className="w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center">
            {bid.vendor.companyLogo ? (
              <img 
                src={bid.vendor.companyLogo} 
                alt={bid.vendor.name}
                className="w-12 h-12 rounded-full object-cover"
              />
            ) : (
              <User className="w-6 h-6 text-primary-600" />
            )}
          </div>
          
          {/* Vendor Info */}
          <div>
            <h3 className="font-semibold text-secondary-900">{bid.vendor.name}</h3>
            {bid.vendor.company && (
              <p className="text-sm text-secondary-600">{bid.vendor.company}</p>
            )}
            {bid.vendor.rating && (
              <div className="flex items-center mt-1">
                <Star className="w-4 h-4 text-warning-500 fill-current" />
                <span className="text-sm text-secondary-600 ml-1">{bid.vendor.rating}/5</span>
              </div>
            )}
          </div>
        </div>

        {/* Status Badge */}
        <span className={`px-3 py-1 rounded-full text-sm font-medium ${bidService.getStatusColor(bid.status)}`}>
          {bidService.getStatusIcon(bid.status)} {bid.status.charAt(0).toUpperCase() + bid.status.slice(1)}
        </span>
      </div>

      {/* Bid Details */}
      <div className="grid md:grid-cols-3 gap-4 mb-4">
        <div className="flex items-center">
          <DollarSign className="w-5 h-5 text-secondary-400 mr-2" />
          <div>
            <div className="font-semibold text-secondary-900">{bidService.formatAmount(bid.amount)}</div>
            <div className="text-xs text-secondary-500">
              {((bid.amount / project.budget - 1) * 100).toFixed(1)}% vs budget
            </div>
          </div>
        </div>
        
        <div className="flex items-center">
          <Clock className="w-5 h-5 text-secondary-400 mr-2" />
          <div>
            <div className="font-semibold text-secondary-900">{bidService.formatDeliveryTime(bid.deliveryTime)}</div>
            <div className="text-xs text-secondary-500">Delivery time</div>
          </div>
        </div>

        {isProjectOwner && (
          <div className="flex items-center">
            <TrendingUp className="w-5 h-5 text-secondary-400 mr-2" />
            <div>
              <div className={`font-semibold ${recommendation.color}`}>{score}/100</div>
              <div className="text-xs text-secondary-500">Bid score</div>
            </div>
          </div>
        )}
      </div>

      {/* Proposal Preview */}
      <div className="mb-4">
        <p className="text-secondary-700 text-sm line-clamp-2">{bid.proposal}</p>
      </div>

      {/* Milestones */}
      {bid.milestones.length > 0 && (
        <div className="mb-4">
          <p className="text-sm font-medium text-secondary-700 mb-2">
            {bid.milestones.length} Milestone{bid.milestones.length !== 1 ? 's' : ''}
          </p>
          <div className="flex flex-wrap gap-2">
            {bid.milestones.slice(0, 3).map((milestone, index) => (
              <span
                key={index}
                className="px-2 py-1 bg-secondary-100 text-secondary-700 rounded text-xs"
              >
                {milestone.title} - {bidService.formatAmount(milestone.amount)}
              </span>
            ))}
            {bid.milestones.length > 3 && (
              <span className="px-2 py-1 bg-secondary-100 text-secondary-700 rounded text-xs">
                +{bid.milestones.length - 3} more
              </span>
            )}
          </div>
        </div>
      )}

      {/* Recommendation for project owner */}
      {isProjectOwner && (
        <div className="mb-4 p-3 bg-secondary-50 rounded-lg">
          <div className="flex items-center">
            <Award className="w-4 h-4 mr-2 text-secondary-400" />
            <span className={`text-sm font-medium ${recommendation.color}`}>
              {recommendation.message}
            </span>
          </div>
        </div>
      )}

      {/* Actions */}
      <div className="flex items-center justify-between">
        <button
          onClick={onViewDetails}
          className="btn-secondary text-sm"
        >
          <Eye className="w-4 h-4 mr-2" />
          View Details
        </button>

        {canManage && bid.status === 'pending' && (
          <div className="flex space-x-2">
            <button
              onClick={onReject}
              className="btn-secondary text-error-600 border-error-300 hover:bg-error-50 text-sm"
            >
              <XCircle className="w-4 h-4 mr-2" />
              Reject
            </button>
            <button
              onClick={onAccept}
              className="btn-primary text-sm"
            >
              <CheckCircle className="w-4 h-4 mr-2" />
              Accept
            </button>
          </div>
        )}

        {!canManage && bid.vendor._id === user._id && (
          <div className="flex space-x-2">
            <button className="btn-secondary text-sm">
              <MessageSquare className="w-4 h-4 mr-2" />
              Message Client
            </button>
          </div>
        )}
      </div>
    </div>
  );
}

// Bid Details Modal Component
interface BidDetailsModalProps {
  bid: Bid;
  project: Project;
  user: UserType;
  onClose: () => void;
  onAccept?: () => void;
  onReject?: () => void;
}

function BidDetailsModal({ bid, project, user, onClose, onAccept, onReject }: BidDetailsModalProps) {
  const score = bidService.calculateBidScore(bid, project.budget);
  const recommendation = bidService.getBidRecommendation(score);
  const isProjectOwner = user._id === (typeof project.client === 'string' ? project.client : project.client._id);

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold text-secondary-900">Bid Details</h2>
            <button
              onClick={onClose}
              className="text-secondary-400 hover:text-secondary-600"
            >
              <XCircle className="w-6 h-6" />
            </button>
          </div>

          <div className="grid lg:grid-cols-3 gap-6">
            {/* Main Content */}
            <div className="lg:col-span-2 space-y-6">
              {/* Vendor Info */}
              <div className="card p-4">
                <div className="flex items-center space-x-4">
                  <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center">
                    {bid.vendor.companyLogo ? (
                      <img 
                        src={bid.vendor.companyLogo} 
                        alt={bid.vendor.name}
                        className="w-16 h-16 rounded-full object-cover"
                      />
                    ) : (
                      <User className="w-8 h-8 text-primary-600" />
                    )}
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold text-secondary-900">{bid.vendor.name}</h3>
                    {bid.vendor.company && (
                      <p className="text-secondary-600">{bid.vendor.company}</p>
                    )}
                    {bid.vendor.rating && (
                      <div className="flex items-center mt-1">
                        <Star className="w-5 h-5 text-warning-500 fill-current" />
                        <span className="text-secondary-600 ml-1">{bid.vendor.rating}/5</span>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Proposal */}
              <div className="card p-4">
                <h3 className="text-lg font-semibold text-secondary-900 mb-4">Proposal</h3>
                <div className="prose prose-sm max-w-none">
                  <p className="text-secondary-700 whitespace-pre-wrap">{bid.proposal}</p>
                </div>
              </div>

              {/* Milestones */}
              {bid.milestones.length > 0 && (
                <div className="card p-4">
                  <h3 className="text-lg font-semibold text-secondary-900 mb-4">Project Milestones</h3>
                  <div className="space-y-3">
                    {bid.milestones.map((milestone, index) => (
                      <div key={index} className="p-3 bg-secondary-50 rounded-lg">
                        <div className="flex justify-between items-start mb-2">
                          <h4 className="font-medium text-secondary-900">{milestone.title}</h4>
                          <span className="font-semibold text-primary-600">
                            {bidService.formatAmount(milestone.amount)}
                          </span>
                        </div>
                        <p className="text-sm text-secondary-600 mb-2">{milestone.description}</p>
                        {milestone.deadline && (
                          <div className="flex items-center text-xs text-secondary-500">
                            <Calendar className="w-3 h-3 mr-1" />
                            Due: {new Date(milestone.deadline).toLocaleDateString()}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>

            {/* Sidebar */}
            <div className="lg:col-span-1 space-y-4">
              {/* Bid Summary */}
              <div className="card p-4">
                <h3 className="text-lg font-semibold text-secondary-900 mb-4">Bid Summary</h3>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-secondary-600">Amount:</span>
                    <span className="font-semibold text-secondary-900">
                      {bidService.formatAmount(bid.amount)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-secondary-600">Delivery:</span>
                    <span className="font-semibold text-secondary-900">
                      {bidService.formatDeliveryTime(bid.deliveryTime)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-secondary-600">Status:</span>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${bidService.getStatusColor(bid.status)}`}>
                      {bid.status.charAt(0).toUpperCase() + bid.status.slice(1)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-secondary-600">Submitted:</span>
                    <span className="font-semibold text-secondary-900">
                      {new Date(bid.createdAt).toLocaleDateString()}
                    </span>
                  </div>
                </div>
              </div>

              {/* Recommendation */}
              {isProjectOwner && (
                <div className="card p-4">
                  <h3 className="text-lg font-semibold text-secondary-900 mb-4">AI Recommendation</h3>
                  <div className="text-center mb-4">
                    <div className={`text-3xl font-bold ${recommendation.color}`}>{score}/100</div>
                    <div className="text-sm text-secondary-600">Bid Score</div>
                  </div>
                  <p className={`text-sm ${recommendation.color} text-center`}>
                    {recommendation.message}
                  </p>
                </div>
              )}

              {/* Actions */}
              {onAccept && onReject && bid.status === 'pending' && (
                <div className="space-y-2">
                  <button
                    onClick={() => {
                      onAccept();
                      onClose();
                    }}
                    className="btn-primary w-full"
                  >
                    <CheckCircle className="w-5 h-5 mr-2" />
                    Accept Bid
                  </button>
                  <button
                    onClick={() => {
                      onReject();
                      onClose();
                    }}
                    className="btn-secondary w-full text-error-600 border-error-300 hover:bg-error-50"
                  >
                    <XCircle className="w-5 h-5 mr-2" />
                    Reject Bid
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
