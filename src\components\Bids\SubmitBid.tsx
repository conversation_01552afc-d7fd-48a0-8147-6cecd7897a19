import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { 
  ArrowLeft, 
  DollarSign, 
  Calendar, 
  FileText, 
  Plus, 
  X, 
  Upload,
  Save,
  AlertCircle,
  TrendingUp,
  Clock
} from 'lucide-react';
import { projectService, Project } from '../../services/projectService';
import { bidService, CreateBidData } from '../../services/bidService';

export default function SubmitBid() {
  const { projectId } = useParams<{ projectId: string }>();
  const navigate = useNavigate();
  
  const [project, setProject] = useState<Project | null>(null);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState('');
  
  const [formData, setFormData] = useState<CreateBidData>({
    project: projectId || '',
    amount: 0,
    proposal: '',
    deliveryTime: 0,
    milestones: [],
    attachments: []
  });

  const [newMilestone, setNewMilestone] = useState({
    title: '',
    description: '',
    amount: 0,
    deadline: ''
  });

  useEffect(() => {
    if (projectId) {
      fetchProject();
    }
  }, [projectId]);

  const fetchProject = async () => {
    try {
      setLoading(true);
      const projectData = await projectService.getProjectById(projectId!);
      setProject(projectData);
      setFormData(prev => ({ ...prev, project: projectData._id }));
    } catch (error: any) {
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: name === 'amount' || name === 'deliveryTime' ? parseFloat(value) || 0 : value
    }));
    if (error) setError('');
  };

  const handleMilestoneChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setNewMilestone(prev => ({
      ...prev,
      [name]: name === 'amount' ? parseFloat(value) || 0 : value
    }));
  };

  const addMilestone = () => {
    if (!newMilestone.title.trim() || !newMilestone.description.trim() || newMilestone.amount <= 0) {
      setError('Please fill in all milestone fields');
      return;
    }

    setFormData(prev => ({
      ...prev,
      milestones: [...prev.milestones!, newMilestone]
    }));

    setNewMilestone({
      title: '',
      description: '',
      amount: 0,
      deadline: ''
    });
  };

  const removeMilestone = (index: number) => {
    setFormData(prev => ({
      ...prev,
      milestones: prev.milestones!.filter((_, i) => i !== index)
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validation
    if (!formData.amount || formData.amount <= 0) {
      setError('Please enter a valid bid amount');
      return;
    }
    
    if (!formData.proposal.trim()) {
      setError('Please provide a detailed proposal');
      return;
    }
    
    if (!formData.deliveryTime || formData.deliveryTime <= 0) {
      setError('Please specify delivery time');
      return;
    }

    // Check if total milestone amounts don't exceed bid amount
    const totalMilestoneAmount = formData.milestones!.reduce((sum, milestone) => sum + milestone.amount, 0);
    if (totalMilestoneAmount > formData.amount) {
      setError('Total milestone amounts cannot exceed the bid amount');
      return;
    }

    try {
      setSubmitting(true);
      await bidService.createBid(formData);
      navigate(`/projects/${projectId}`);
    } catch (error: any) {
      setError(error.message);
    } finally {
      setSubmitting(false);
    }
  };

  const handleCancel = () => {
    navigate(`/projects/${projectId}`);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-secondary-50 flex items-center justify-center">
        <div className="text-center">
          <div className="w-12 h-12 border-4 border-primary-200 border-t-primary-600 rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-secondary-600">Loading project details...</p>
        </div>
      </div>
    );
  }

  if (!project) {
    return (
      <div className="min-h-screen bg-secondary-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-secondary-900 mb-4">Project Not Found</h1>
          <button onClick={() => navigate('/projects')} className="btn-primary">
            Back to Projects
          </button>
        </div>
      </div>
    );
  }

  const totalMilestoneAmount = formData.milestones!.reduce((sum, milestone) => sum + milestone.amount, 0);
  const remainingAmount = formData.amount - totalMilestoneAmount;

  return (
    <div className="min-h-screen bg-secondary-50">
      <div className="container-custom py-8">
        {/* Header */}
        <div className="flex items-center mb-8">
          <button onClick={handleCancel} className="btn-secondary mr-4">
            <ArrowLeft className="w-5 h-5 mr-2" />
            Back
          </button>
          <div>
            <h1 className="text-3xl font-display font-bold text-secondary-900">Submit Bid</h1>
            <p className="text-secondary-600">Submit your proposal for this project</p>
          </div>
        </div>

        {/* Error Message */}
        {error && (
          <div className="mb-6 p-4 bg-error-50 border border-error-200 rounded-lg flex items-center">
            <AlertCircle className="w-5 h-5 text-error-600 mr-3" />
            <p className="text-error-700">{error}</p>
          </div>
        )}

        <div className="grid lg:grid-cols-3 gap-8">
          {/* Main Form */}
          <div className="lg:col-span-2">
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Bid Details */}
              <div className="card p-6">
                <h2 className="text-xl font-semibold text-secondary-900 mb-6">Bid Details</h2>
                
                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-secondary-700 mb-2">
                      Bid Amount (USD) *
                    </label>
                    <div className="relative">
                      <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-secondary-400" />
                      <input
                        type="number"
                        name="amount"
                        value={formData.amount || ''}
                        onChange={handleInputChange}
                        placeholder="Enter your bid amount"
                        min="1"
                        className="input-field pl-10"
                        required
                      />
                    </div>
                    {project.budget && (
                      <p className="text-sm text-secondary-600 mt-1">
                        Project budget: {projectService.formatBudget(project.budget)}
                      </p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-secondary-700 mb-2">
                      Delivery Time (Days) *
                    </label>
                    <div className="relative">
                      <Clock className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-secondary-400" />
                      <input
                        type="number"
                        name="deliveryTime"
                        value={formData.deliveryTime || ''}
                        onChange={handleInputChange}
                        placeholder="Number of days"
                        min="1"
                        className="input-field pl-10"
                        required
                      />
                    </div>
                    {formData.deliveryTime > 0 && (
                      <p className="text-sm text-secondary-600 mt-1">
                        {bidService.formatDeliveryTime(formData.deliveryTime)}
                      </p>
                    )}
                  </div>
                </div>
              </div>

              {/* Proposal */}
              <div className="card p-6">
                <h2 className="text-xl font-semibold text-secondary-900 mb-6">Proposal</h2>
                
                <div>
                  <label className="block text-sm font-medium text-secondary-700 mb-2">
                    Detailed Proposal *
                  </label>
                  <textarea
                    name="proposal"
                    value={formData.proposal}
                    onChange={handleInputChange}
                    rows={8}
                    placeholder="Describe your approach, experience, and why you're the best fit for this project..."
                    className="input-field resize-none"
                    required
                  />
                  <p className="text-sm text-secondary-500 mt-1">
                    {formData.proposal.length} characters (minimum 100 recommended)
                  </p>
                </div>
              </div>

              {/* Milestones */}
              <div className="card p-6">
                <h2 className="text-xl font-semibold text-secondary-900 mb-6">
                  Project Milestones (Optional)
                </h2>
                
                {/* Add Milestone Form */}
                <div className="space-y-4 mb-6">
                  <div className="grid md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-secondary-700 mb-2">
                        Milestone Title
                      </label>
                      <input
                        type="text"
                        name="title"
                        value={newMilestone.title}
                        onChange={handleMilestoneChange}
                        placeholder="e.g., Initial Design"
                        className="input-field"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-secondary-700 mb-2">
                        Amount
                      </label>
                      <div className="relative">
                        <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-secondary-400" />
                        <input
                          type="number"
                          name="amount"
                          value={newMilestone.amount || ''}
                          onChange={handleMilestoneChange}
                          placeholder="0"
                          min="0"
                          className="input-field pl-8"
                        />
                      </div>
                    </div>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-secondary-700 mb-2">
                      Description
                    </label>
                    <textarea
                      name="description"
                      value={newMilestone.description}
                      onChange={handleMilestoneChange}
                      rows={2}
                      placeholder="Describe what will be delivered in this milestone"
                      className="input-field resize-none"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-secondary-700 mb-2">
                      Deadline
                    </label>
                    <input
                      type="date"
                      name="deadline"
                      value={newMilestone.deadline}
                      onChange={handleMilestoneChange}
                      className="input-field"
                    />
                  </div>
                  
                  <button
                    type="button"
                    onClick={addMilestone}
                    className="btn-secondary"
                  >
                    <Plus className="w-4 h-4 mr-2" />
                    Add Milestone
                  </button>
                </div>

                {/* Milestone List */}
                {formData.milestones!.length > 0 && (
                  <div className="space-y-3">
                    <h3 className="text-lg font-medium text-secondary-900">Added Milestones</h3>
                    {formData.milestones!.map((milestone, index) => (
                      <div key={index} className="p-4 bg-secondary-50 rounded-lg">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <h4 className="font-medium text-secondary-900">{milestone.title}</h4>
                            <p className="text-sm text-secondary-600 mt-1">{milestone.description}</p>
                            <div className="flex items-center space-x-4 mt-2 text-sm text-secondary-500">
                              <span>{bidService.formatAmount(milestone.amount)}</span>
                              {milestone.deadline && (
                                <span>Due: {new Date(milestone.deadline).toLocaleDateString()}</span>
                              )}
                            </div>
                          </div>
                          <button
                            type="button"
                            onClick={() => removeMilestone(index)}
                            className="text-error-600 hover:text-error-700 ml-4"
                          >
                            <X className="w-4 h-4" />
                          </button>
                        </div>
                      </div>
                    ))}
                    
                    <div className="p-3 bg-primary-50 rounded-lg">
                      <div className="flex justify-between text-sm">
                        <span>Total Milestones:</span>
                        <span className="font-medium">{bidService.formatAmount(totalMilestoneAmount)}</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span>Remaining Amount:</span>
                        <span className={`font-medium ${remainingAmount < 0 ? 'text-error-600' : 'text-secondary-900'}`}>
                          {bidService.formatAmount(remainingAmount)}
                        </span>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* File Attachments */}
              <div className="card p-6">
                <h2 className="text-xl font-semibold text-secondary-900 mb-6">
                  Attachments (Optional)
                </h2>
                
                <div className="border-2 border-dashed border-secondary-300 rounded-lg p-8 text-center">
                  <Upload className="w-12 h-12 text-secondary-400 mx-auto mb-4" />
                  <p className="text-secondary-600 mb-2">Upload portfolio samples, certificates, or relevant documents</p>
                  <p className="text-sm text-secondary-500">Supported formats: PDF, DOC, DOCX, JPG, PNG (Max 10MB each)</p>
                  <button type="button" className="btn-secondary mt-4">
                    Choose Files
                  </button>
                </div>
              </div>

              {/* Submit Button */}
              <div className="flex justify-end space-x-4">
                <button
                  type="button"
                  onClick={handleCancel}
                  className="btn-secondary"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={submitting || remainingAmount < 0}
                  className="btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {submitting ? (
                    <div className="flex items-center">
                      <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                      Submitting...
                    </div>
                  ) : (
                    <>
                      <Save className="w-5 h-5 mr-2" />
                      Submit Bid
                    </>
                  )}
                </button>
              </div>
            </form>
          </div>

          {/* Sidebar - Project Info */}
          <div className="lg:col-span-1">
            <div className="card p-6 sticky top-8">
              <h3 className="text-lg font-semibold text-secondary-900 mb-4">Project Details</h3>
              
              <div className="space-y-4">
                <div>
                  <h4 className="font-medium text-secondary-900 mb-2">{project.title}</h4>
                  <p className="text-sm text-secondary-600 line-clamp-3">{project.description}</p>
                </div>
                
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-secondary-600">Budget:</span>
                    <span className="font-medium">{projectService.formatBudget(project.budget)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-secondary-600">Deadline:</span>
                    <span className="font-medium">{projectService.formatDeadline(project.deadline)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-secondary-600">Category:</span>
                    <span className="font-medium capitalize">{project.category.replace('-', ' ')}</span>
                  </div>
                </div>

                <div>
                  <p className="text-sm font-medium text-secondary-700 mb-2">Required Skills:</p>
                  <div className="flex flex-wrap gap-1">
                    {project.skills.map((skill, index) => (
                      <span
                        key={index}
                        className="px-2 py-1 bg-primary-100 text-primary-700 rounded text-xs"
                      >
                        {skill}
                      </span>
                    ))}
                  </div>
                </div>

                {/* Bid Summary */}
                {formData.amount > 0 && (
                  <div className="pt-4 border-t border-secondary-200">
                    <h4 className="text-sm font-medium text-secondary-700 mb-2">Your Bid Summary</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span>Amount:</span>
                        <span className="font-medium">{bidService.formatAmount(formData.amount)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Delivery:</span>
                        <span className="font-medium">
                          {formData.deliveryTime > 0 ? bidService.formatDeliveryTime(formData.deliveryTime) : 'Not set'}
                        </span>
                      </div>
                      {project.budget && (
                        <div className="flex justify-between">
                          <span>vs Budget:</span>
                          <span className={`font-medium ${
                            formData.amount <= project.budget ? 'text-success-600' : 'text-warning-600'
                          }`}>
                            {((formData.amount / project.budget - 1) * 100).toFixed(1)}%
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
