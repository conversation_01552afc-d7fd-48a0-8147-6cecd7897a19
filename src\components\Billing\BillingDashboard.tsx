import React, { useState, useEffect } from 'react';
import { 
  CreditCard, 
  DollarSign, 
  Download, 
  Plus, 
  TrendingUp,
  Calendar,
  ArrowUpRight,
  ArrowDownLeft,
  Wallet,
  Receipt
} from 'lucide-react';
import { paymentService, Transaction, PaymentMethod, Subscription } from '../../services/paymentService';
import { User } from '../../services/authService';
import PaymentMethodCard from './PaymentMethodCard';
import TransactionList from './TransactionList';
import AddPaymentMethodModal from './AddPaymentMethodModal';
import WithdrawalModal from './WithdrawalModal';
import SubscriptionCard from './SubscriptionCard';

interface BillingDashboardProps {
  user: User;
}

export default function BillingDashboard({ user }: BillingDashboardProps) {
  const [activeTab, setActiveTab] = useState<'overview' | 'transactions' | 'methods' | 'subscription'>('overview');
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([]);
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [subscription, setSubscription] = useState<Subscription | null>(null);
  const [loading, setLoading] = useState(true);
  const [showAddMethodModal, setShowAddMethodModal] = useState(false);
  const [showWithdrawalModal, setShowWithdrawalModal] = useState(false);

  // Mock balance data
  const [balance] = useState({
    available: 12450.75,
    pending: 2300.00,
    total: 14750.75
  });

  useEffect(() => {
    fetchBillingData();
  }, []);

  const fetchBillingData = async () => {
    try {
      setLoading(true);
      const [methodsData, transactionsData, subscriptionData] = await Promise.all([
        paymentService.getPaymentMethods(),
        paymentService.getTransactions(1, 10),
        paymentService.getSubscription()
      ]);

      setPaymentMethods(methodsData);
      setTransactions(transactionsData.transactions);
      setSubscription(subscriptionData);
    } catch (error) {
      console.error('Failed to fetch billing data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleAddPaymentMethod = async (method: Partial<PaymentMethod>) => {
    try {
      const newMethod = await paymentService.addPaymentMethod(method);
      setPaymentMethods(prev => [...prev, newMethod]);
      setShowAddMethodModal(false);
    } catch (error) {
      console.error('Failed to add payment method:', error);
    }
  };

  const handleRemovePaymentMethod = async (id: string) => {
    try {
      await paymentService.removePaymentMethod(id);
      setPaymentMethods(prev => prev.filter(pm => pm.id !== id));
    } catch (error) {
      console.error('Failed to remove payment method:', error);
    }
  };

  const handleWithdrawal = async (amount: number, methodId: string) => {
    try {
      const transaction = await paymentService.requestWithdrawal(amount, methodId);
      setTransactions(prev => [transaction, ...prev]);
      setShowWithdrawalModal(false);
    } catch (error) {
      console.error('Failed to request withdrawal:', error);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-secondary-50 flex items-center justify-center">
        <div className="text-center">
          <div className="w-12 h-12 border-4 border-primary-200 border-t-primary-600 rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-secondary-600">Loading billing information...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-secondary-50">
      <div className="container-custom py-8">
        {/* Header */}
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-8">
          <div>
            <h1 className="text-3xl font-display font-bold text-secondary-900 mb-2">
              Billing & Payments
            </h1>
            <p className="text-secondary-600">
              Manage your payments, subscriptions, and financial data
            </p>
          </div>
          
          <div className="flex items-center space-x-4 mt-4 lg:mt-0">
            <button
              onClick={() => setShowWithdrawalModal(true)}
              className="btn-secondary"
            >
              <ArrowDownLeft className="w-5 h-5 mr-2" />
              Withdraw Funds
            </button>
            <button
              onClick={() => setShowAddMethodModal(true)}
              className="btn-primary"
            >
              <Plus className="w-5 h-5 mr-2" />
              Add Payment Method
            </button>
          </div>
        </div>

        {/* Navigation Tabs */}
        <div className="mb-8">
          <nav className="flex space-x-8">
            {[
              { id: 'overview', label: 'Overview', icon: DollarSign },
              { id: 'transactions', label: 'Transactions', icon: Receipt },
              { id: 'methods', label: 'Payment Methods', icon: CreditCard },
              { id: 'subscription', label: 'Subscription', icon: Calendar }
            ].map(({ id, label, icon: Icon }) => (
              <button
                key={id}
                onClick={() => setActiveTab(id as any)}
                className={`flex items-center space-x-2 py-2 px-4 border-b-2 transition-colors duration-200 ${
                  activeTab === id
                    ? 'border-primary-600 text-primary-600'
                    : 'border-transparent text-secondary-600 hover:text-secondary-900'
                }`}
              >
                <Icon className="w-5 h-5" />
                <span className="font-medium">{label}</span>
              </button>
            ))}
          </nav>
        </div>

        {/* Content */}
        {activeTab === 'overview' && (
          <div className="space-y-8">
            {/* Balance Cards */}
            <div className="grid md:grid-cols-3 gap-6">
              <div className="card p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center">
                    <Wallet className="w-5 h-5 text-success-600 mr-2" />
                    <h3 className="font-semibold text-secondary-900">Available Balance</h3>
                  </div>
                </div>
                <p className="text-2xl font-bold text-success-600 mb-2">
                  {paymentService.formatAmount(balance.available * 100)}
                </p>
                <p className="text-sm text-secondary-600">Ready for withdrawal</p>
              </div>

              <div className="card p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center">
                    <TrendingUp className="w-5 h-5 text-warning-600 mr-2" />
                    <h3 className="font-semibold text-secondary-900">Pending</h3>
                  </div>
                </div>
                <p className="text-2xl font-bold text-warning-600 mb-2">
                  {paymentService.formatAmount(balance.pending * 100)}
                </p>
                <p className="text-sm text-secondary-600">Processing payments</p>
              </div>

              <div className="card p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center">
                    <DollarSign className="w-5 h-5 text-primary-600 mr-2" />
                    <h3 className="font-semibold text-secondary-900">Total Earnings</h3>
                  </div>
                </div>
                <p className="text-2xl font-bold text-primary-600 mb-2">
                  {paymentService.formatAmount(balance.total * 100)}
                </p>
                <p className="text-sm text-secondary-600">All time earnings</p>
              </div>
            </div>

            {/* Recent Transactions */}
            <div className="card p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold text-secondary-900">Recent Transactions</h3>
                <button
                  onClick={() => setActiveTab('transactions')}
                  className="text-primary-600 hover:text-primary-700 text-sm font-medium"
                >
                  View All
                </button>
              </div>
              <TransactionList transactions={transactions.slice(0, 5)} />
            </div>

            {/* Quick Actions */}
            <div className="grid md:grid-cols-2 gap-6">
              <div className="card p-6">
                <h3 className="text-lg font-semibold text-secondary-900 mb-4">Quick Actions</h3>
                <div className="space-y-3">
                  <button
                    onClick={() => setShowWithdrawalModal(true)}
                    className="w-full flex items-center justify-between p-3 bg-secondary-50 hover:bg-secondary-100 rounded-lg transition-colors duration-200"
                  >
                    <div className="flex items-center">
                      <ArrowDownLeft className="w-5 h-5 text-secondary-600 mr-3" />
                      <span className="font-medium text-secondary-900">Withdraw Funds</span>
                    </div>
                    <ArrowUpRight className="w-4 h-4 text-secondary-400" />
                  </button>
                  
                  <button
                    onClick={() => setActiveTab('methods')}
                    className="w-full flex items-center justify-between p-3 bg-secondary-50 hover:bg-secondary-100 rounded-lg transition-colors duration-200"
                  >
                    <div className="flex items-center">
                      <CreditCard className="w-5 h-5 text-secondary-600 mr-3" />
                      <span className="font-medium text-secondary-900">Manage Payment Methods</span>
                    </div>
                    <ArrowUpRight className="w-4 h-4 text-secondary-400" />
                  </button>
                  
                  <button className="w-full flex items-center justify-between p-3 bg-secondary-50 hover:bg-secondary-100 rounded-lg transition-colors duration-200">
                    <div className="flex items-center">
                      <Download className="w-5 h-5 text-secondary-600 mr-3" />
                      <span className="font-medium text-secondary-900">Download Tax Documents</span>
                    </div>
                    <ArrowUpRight className="w-4 h-4 text-secondary-400" />
                  </button>
                </div>
              </div>

              {/* Subscription Info */}
              {subscription && (
                <div className="card p-6">
                  <h3 className="text-lg font-semibold text-secondary-900 mb-4">Current Plan</h3>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-secondary-600">Plan</span>
                      <span className="font-medium text-secondary-900 capitalize">{subscription.plan}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-secondary-600">Status</span>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${paymentService.getStatusBadgeColor(subscription.status)}`}>
                        {subscription.status}
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-secondary-600">Next Billing</span>
                      <span className="font-medium text-secondary-900">
                        {new Date(subscription.currentPeriodEnd).toLocaleDateString()}
                      </span>
                    </div>
                    <button
                      onClick={() => setActiveTab('subscription')}
                      className="w-full btn-secondary mt-4"
                    >
                      Manage Subscription
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {activeTab === 'transactions' && (
          <div className="card p-6">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-secondary-900">All Transactions</h3>
              <button className="btn-secondary">
                <Download className="w-4 h-4 mr-2" />
                Export
              </button>
            </div>
            <TransactionList transactions={transactions} />
          </div>
        )}

        {activeTab === 'methods' && (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-secondary-900">Payment Methods</h3>
              <button
                onClick={() => setShowAddMethodModal(true)}
                className="btn-primary"
              >
                <Plus className="w-4 h-4 mr-2" />
                Add Method
              </button>
            </div>
            
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              {paymentMethods.map((method) => (
                <PaymentMethodCard
                  key={method.id}
                  method={method}
                  onRemove={handleRemovePaymentMethod}
                  onSetDefault={(id) => paymentService.setDefaultPaymentMethod(id)}
                />
              ))}
            </div>
          </div>
        )}

        {activeTab === 'subscription' && subscription && (
          <SubscriptionCard
            subscription={subscription}
            onUpdate={(plan) => paymentService.updateSubscription(plan)}
            onCancel={() => paymentService.cancelSubscription()}
          />
        )}

        {/* Modals */}
        <AddPaymentMethodModal
          isOpen={showAddMethodModal}
          onClose={() => setShowAddMethodModal(false)}
          onAdd={handleAddPaymentMethod}
        />

        <WithdrawalModal
          isOpen={showWithdrawalModal}
          onClose={() => setShowWithdrawalModal(false)}
          onWithdraw={handleWithdrawal}
          paymentMethods={paymentMethods.filter(pm => pm.type === 'bank')}
          availableBalance={balance.available}
        />
      </div>
    </div>
  );
}
