import React, { useState } from 'react';
import { CreditCard, Trash2, Star, Building, Mail, MoreVertical } from 'lucide-react';
import { PaymentMethod, paymentService } from '../../services/paymentService';

interface PaymentMethodCardProps {
  method: PaymentMethod;
  onRemove: (id: string) => void;
  onSetDefault: (id: string) => void;
}

export default function PaymentMethodCard({ method, onRemove, onSetDefault }: PaymentMethodCardProps) {
  const [showActions, setShowActions] = useState(false);
  const [loading, setLoading] = useState(false);

  const handleSetDefault = async () => {
    try {
      setLoading(true);
      await onSetDefault(method.id);
    } catch (error) {
      console.error('Failed to set default payment method:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleRemove = async () => {
    if (method.isDefault) {
      alert('Cannot remove default payment method. Please set another method as default first.');
      return;
    }

    if (confirm('Are you sure you want to remove this payment method?')) {
      try {
        setLoading(true);
        await onRemove(method.id);
      } catch (error) {
        console.error('Failed to remove payment method:', error);
      } finally {
        setLoading(false);
      }
    }
  };

  const getCardIcon = () => {
    if (method.type === 'card') {
      return <CreditCard className="w-6 h-6" />;
    } else if (method.type === 'paypal') {
      return <Mail className="w-6 h-6" />;
    } else if (method.type === 'bank') {
      return <Building className="w-6 h-6" />;
    }
    return <CreditCard className="w-6 h-6" />;
  };

  const getCardBrand = () => {
    if (method.type === 'card' && method.brand) {
      const brands: Record<string, string> = {
        visa: 'bg-blue-600',
        mastercard: 'bg-red-600',
        amex: 'bg-green-600',
        discover: 'bg-orange-600'
      };
      return brands[method.brand] || 'bg-secondary-600';
    }
    return 'bg-secondary-600';
  };

  const getDisplayText = () => {
    if (method.type === 'card') {
      return (
        <div>
          <p className="font-medium text-white">
            •••• •••• •••• {method.last4}
          </p>
          <p className="text-sm text-white/80">
            {method.expiryMonth?.toString().padStart(2, '0')}/{method.expiryYear?.toString().slice(-2)}
          </p>
        </div>
      );
    } else if (method.type === 'paypal') {
      return (
        <div>
          <p className="font-medium text-white">PayPal</p>
          <p className="text-sm text-white/80">{method.email}</p>
        </div>
      );
    } else if (method.type === 'bank') {
      return (
        <div>
          <p className="font-medium text-white">{method.bankName}</p>
          <p className="text-sm text-white/80">Bank Account</p>
        </div>
      );
    }
  };

  return (
    <div className="relative">
      <div className={`
        relative p-6 rounded-xl text-white min-h-[160px] flex flex-col justify-between
        ${getCardBrand()}
        ${method.isDefault ? 'ring-2 ring-yellow-400 ring-offset-2' : ''}
        transition-all duration-200 hover:shadow-lg
      `}>
        {/* Header */}
        <div className="flex items-start justify-between">
          <div className="flex items-center space-x-2">
            {getCardIcon()}
            {method.isDefault && (
              <div className="flex items-center space-x-1">
                <Star className="w-4 h-4 text-yellow-400 fill-current" />
                <span className="text-xs font-medium text-yellow-400">Default</span>
              </div>
            )}
          </div>
          
          <div className="relative">
            <button
              onClick={() => setShowActions(!showActions)}
              className="p-1 text-white/60 hover:text-white transition-colors duration-200"
              disabled={loading}
            >
              <MoreVertical className="w-4 h-4" />
            </button>

            {showActions && (
              <div className="absolute right-0 top-full mt-1 bg-white border border-secondary-200 rounded-lg shadow-lg z-10 min-w-32">
                {!method.isDefault && (
                  <button
                    onClick={handleSetDefault}
                    disabled={loading}
                    className="w-full text-left px-3 py-2 text-sm text-secondary-700 hover:bg-secondary-50 transition-colors duration-200 disabled:opacity-50"
                  >
                    Set as Default
                  </button>
                )}
                <button
                  onClick={handleRemove}
                  disabled={loading || method.isDefault}
                  className="w-full text-left px-3 py-2 text-sm text-error-600 hover:bg-error-50 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <Trash2 className="w-4 h-4 mr-2 inline" />
                  Remove
                </button>
              </div>
            )}
          </div>
        </div>

        {/* Card Details */}
        <div className="mt-4">
          {getDisplayText()}
        </div>

        {/* Card Type */}
        <div className="flex items-center justify-between mt-4">
          <span className="text-sm text-white/80 capitalize">
            {method.type === 'card' ? method.brand : method.type}
          </span>
          {method.type === 'card' && (
            <div className="text-right">
              <div className="text-xs text-white/60">Valid Thru</div>
              <div className="text-sm font-medium">
                {method.expiryMonth?.toString().padStart(2, '0')}/{method.expiryYear?.toString().slice(-2)}
              </div>
            </div>
          )}
        </div>

        {/* Loading Overlay */}
        {loading && (
          <div className="absolute inset-0 bg-black/20 rounded-xl flex items-center justify-center">
            <div className="w-6 h-6 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
          </div>
        )}
      </div>

      {/* Default Badge */}
      {method.isDefault && (
        <div className="absolute -top-2 -right-2 bg-yellow-400 text-yellow-900 text-xs font-bold px-2 py-1 rounded-full">
          DEFAULT
        </div>
      )}
    </div>
  );
}
