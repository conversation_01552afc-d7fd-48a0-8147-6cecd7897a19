import React, { useState, useEffect } from 'react';
import { X, CreditCard, Lock, AlertCircle, CheckCircle } from 'lucide-react';
import { PaymentMethod, paymentService } from '../../services/paymentService';

interface PaymentModalProps {
  isOpen: boolean;
  onClose: () => void;
  onPaymentSuccess: (transactionId: string) => void;
  amount: number;
  projectId: string;
  projectTitle: string;
}

export default function PaymentModal({ 
  isOpen, 
  onClose, 
  onPaymentSuccess, 
  amount, 
  projectId, 
  projectTitle 
}: PaymentModalProps) {
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([]);
  const [selectedMethodId, setSelectedMethodId] = useState('');
  const [loading, setLoading] = useState(false);
  const [processing, setProcessing] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);

  useEffect(() => {
    if (isOpen) {
      fetchPaymentMethods();
    }
  }, [isOpen]);

  const fetchPaymentMethods = async () => {
    try {
      setLoading(true);
      const methods = await paymentService.getPaymentMethods();
      setPaymentMethods(methods);
      
      // Auto-select default method
      const defaultMethod = methods.find(m => m.isDefault);
      if (defaultMethod) {
        setSelectedMethodId(defaultMethod.id);
      }
    } catch (error) {
      console.error('Failed to fetch payment methods:', error);
      setError('Failed to load payment methods');
    } finally {
      setLoading(false);
    }
  };

  const handlePayment = async () => {
    if (!selectedMethodId) {
      setError('Please select a payment method');
      return;
    }

    try {
      setProcessing(true);
      setError('');

      // Simulate payment processing
      const transaction = await paymentService.processProjectPayment(
        projectId, 
        amount * 100, // Convert to cents
        selectedMethodId
      );

      setSuccess(true);
      
      // Wait a moment to show success, then call success callback
      setTimeout(() => {
        onPaymentSuccess(transaction.id);
        handleClose();
      }, 2000);

    } catch (error: any) {
      setError(error.message || 'Payment failed. Please try again.');
    } finally {
      setProcessing(false);
    }
  };

  const handleClose = () => {
    setSelectedMethodId('');
    setError('');
    setSuccess(false);
    setProcessing(false);
    onClose();
  };

  const platformFee = Math.round(amount * 0.029 * 100) / 100; // 2.9% platform fee
  const totalAmount = amount + platformFee;

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-md w-full">
        {/* Header */}
        <div className="p-6 border-b border-secondary-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <CreditCard className="w-6 h-6 text-primary-600" />
              <h2 className="text-xl font-semibold text-secondary-900">Complete Payment</h2>
            </div>
            <button
              onClick={handleClose}
              className="text-secondary-400 hover:text-secondary-600"
              disabled={processing}
            >
              <X className="w-6 h-6" />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="p-6">
          {success ? (
            /* Success State */
            <div className="text-center py-8">
              <div className="w-16 h-16 bg-success-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <CheckCircle className="w-8 h-8 text-success-600" />
              </div>
              <h3 className="text-lg font-semibold text-secondary-900 mb-2">Payment Successful!</h3>
              <p className="text-secondary-600">
                Your payment has been processed successfully. You will receive a confirmation email shortly.
              </p>
            </div>
          ) : (
            <>
              {/* Project Info */}
              <div className="mb-6 p-4 bg-secondary-50 border border-secondary-200 rounded-lg">
                <h3 className="font-medium text-secondary-900 mb-1">{projectTitle}</h3>
                <p className="text-sm text-secondary-600">Project ID: {projectId}</p>
              </div>

              {/* Payment Breakdown */}
              <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <h4 className="font-medium text-blue-900 mb-3">Payment Summary</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-blue-700">Project Amount</span>
                    <span className="text-blue-900 font-medium">${amount.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-blue-700">Platform Fee (2.9%)</span>
                    <span className="text-blue-900 font-medium">${platformFee.toFixed(2)}</span>
                  </div>
                  <div className="border-t border-blue-300 pt-2 mt-2">
                    <div className="flex justify-between font-medium">
                      <span className="text-blue-900">Total Amount</span>
                      <span className="text-blue-900">${totalAmount.toFixed(2)}</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Payment Method Selection */}
              {loading ? (
                <div className="text-center py-8">
                  <div className="w-8 h-8 border-4 border-primary-200 border-t-primary-600 rounded-full animate-spin mx-auto mb-4"></div>
                  <p className="text-secondary-600">Loading payment methods...</p>
                </div>
              ) : paymentMethods.length === 0 ? (
                <div className="text-center py-8">
                  <div className="w-16 h-16 bg-warning-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <AlertCircle className="w-8 h-8 text-warning-600" />
                  </div>
                  <h3 className="text-lg font-semibold text-secondary-900 mb-2">No Payment Methods</h3>
                  <p className="text-secondary-600 mb-4">
                    You need to add a payment method before you can make a payment.
                  </p>
                  <button
                    onClick={handleClose}
                    className="btn-primary"
                  >
                    Add Payment Method
                  </button>
                </div>
              ) : (
                <>
                  <div className="mb-6">
                    <label className="block text-sm font-medium text-secondary-700 mb-3">
                      Select Payment Method
                    </label>
                    <div className="space-y-2">
                      {paymentMethods.map((method) => (
                        <label
                          key={method.id}
                          className={`flex items-center p-3 border rounded-lg cursor-pointer transition-colors duration-200 ${
                            selectedMethodId === method.id
                              ? 'border-primary-500 bg-primary-50'
                              : 'border-secondary-300 hover:border-secondary-400'
                          }`}
                        >
                          <input
                            type="radio"
                            name="paymentMethod"
                            value={method.id}
                            checked={selectedMethodId === method.id}
                            onChange={(e) => setSelectedMethodId(e.target.value)}
                            className="sr-only"
                          />
                          <div className="flex items-center space-x-3 flex-1">
                            <CreditCard className="w-5 h-5 text-secondary-600" />
                            <div className="flex-1">
                              <p className="font-medium text-secondary-900">
                                {paymentService.getPaymentMethodDisplay(method)}
                              </p>
                              <p className="text-sm text-secondary-600 capitalize">
                                {method.type === 'card' ? method.brand : method.type}
                                {method.isDefault && (
                                  <span className="ml-2 text-xs bg-primary-100 text-primary-700 px-2 py-0.5 rounded-full">
                                    Default
                                  </span>
                                )}
                              </p>
                            </div>
                          </div>
                        </label>
                      ))}
                    </div>
                  </div>

                  {/* Error Message */}
                  {error && (
                    <div className="mb-6 p-4 bg-error-50 border border-error-200 rounded-lg">
                      <div className="flex items-center">
                        <AlertCircle className="w-5 h-5 text-error-600 mr-3" />
                        <p className="text-error-700">{error}</p>
                      </div>
                    </div>
                  )}

                  {/* Security Notice */}
                  <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg">
                    <div className="flex items-start">
                      <Lock className="w-5 h-5 text-green-600 mr-3 mt-0.5" />
                      <div>
                        <h4 className="font-medium text-green-900 mb-1">Secure Payment</h4>
                        <p className="text-sm text-green-700">
                          Your payment is protected by bank-level security and encryption.
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex justify-end space-x-3">
                    <button
                      onClick={handleClose}
                      className="btn-secondary"
                      disabled={processing}
                    >
                      Cancel
                    </button>
                    <button
                      onClick={handlePayment}
                      disabled={!selectedMethodId || processing}
                      className="btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {processing ? (
                        <div className="flex items-center">
                          <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                          Processing...
                        </div>
                      ) : (
                        <>
                          <Lock className="w-4 h-4 mr-2" />
                          Pay ${totalAmount.toFixed(2)}
                        </>
                      )}
                    </button>
                  </div>
                </>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  );
}
