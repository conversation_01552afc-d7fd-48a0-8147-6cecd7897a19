import React, { useState } from 'react';
import { Check, X, Crown, Zap, Building, Calendar, CreditCard } from 'lucide-react';
import { Subscription, paymentService } from '../../services/paymentService';

interface SubscriptionCardProps {
  subscription: Subscription;
  onUpdate: (plan: 'basic' | 'pro' | 'enterprise') => Promise<Subscription>;
  onCancel: () => Promise<void>;
}

const plans = {
  basic: {
    name: 'Basic',
    price: 900, // $9.00
    icon: Zap,
    color: 'text-blue-600',
    bgColor: 'bg-blue-100',
    features: [
      'Up to 5 projects',
      'Basic analytics',
      'Email support',
      'Standard security',
      '1GB storage'
    ]
  },
  pro: {
    name: 'Pro',
    price: 2900, // $29.00
    icon: Crown,
    color: 'text-purple-600',
    bgColor: 'bg-purple-100',
    features: [
      'Unlimited projects',
      'Advanced analytics',
      'Priority support',
      'Enhanced security',
      '10GB storage',
      'Custom branding',
      'API access'
    ]
  },
  enterprise: {
    name: 'Enterprise',
    price: 9900, // $99.00
    icon: Building,
    color: 'text-green-600',
    bgColor: 'bg-green-100',
    features: [
      'Everything in Pro',
      'Dedicated account manager',
      '24/7 phone support',
      'Advanced integrations',
      'Unlimited storage',
      'Custom contracts',
      'SLA guarantee',
      'White-label solution'
    ]
  }
};

export default function SubscriptionCard({ subscription, onUpdate, onCancel }: SubscriptionCardProps) {
  const [loading, setLoading] = useState<string | null>(null);
  const [showCancelConfirm, setShowCancelConfirm] = useState(false);

  const handlePlanChange = async (newPlan: 'basic' | 'pro' | 'enterprise') => {
    if (newPlan === subscription.plan) return;

    try {
      setLoading(newPlan);
      await onUpdate(newPlan);
    } catch (error) {
      console.error('Failed to update subscription:', error);
    } finally {
      setLoading(null);
    }
  };

  const handleCancel = async () => {
    try {
      setLoading('cancel');
      await onCancel();
      setShowCancelConfirm(false);
    } catch (error) {
      console.error('Failed to cancel subscription:', error);
    } finally {
      setLoading(null);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <div className="space-y-8">
      {/* Current Subscription Info */}
      <div className="card p-6">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold text-secondary-900">Current Subscription</h3>
          <span className={`px-3 py-1 rounded-full text-sm font-medium ${paymentService.getStatusBadgeColor(subscription.status)}`}>
            {subscription.status.charAt(0).toUpperCase() + subscription.status.slice(1)}
          </span>
        </div>

        <div className="grid md:grid-cols-2 gap-6">
          <div className="space-y-4">
            <div className="flex items-center space-x-3">
              {React.createElement(plans[subscription.plan].icon, {
                className: `w-8 h-8 ${plans[subscription.plan].color}`
              })}
              <div>
                <h4 className="text-xl font-bold text-secondary-900">
                  {plans[subscription.plan].name} Plan
                </h4>
                <p className="text-secondary-600">
                  {paymentService.formatAmount(subscription.amount)}/month
                </p>
              </div>
            </div>

            <div className="space-y-2 text-sm">
              <div className="flex items-center space-x-2">
                <Calendar className="w-4 h-4 text-secondary-400" />
                <span className="text-secondary-600">
                  Next billing: {formatDate(subscription.currentPeriodEnd)}
                </span>
              </div>
              <div className="flex items-center space-x-2">
                <CreditCard className="w-4 h-4 text-secondary-400" />
                <span className="text-secondary-600">
                  Started: {formatDate(subscription.currentPeriodStart)}
                </span>
              </div>
            </div>
          </div>

          <div>
            <h5 className="font-medium text-secondary-900 mb-3">Plan Features</h5>
            <ul className="space-y-2">
              {plans[subscription.plan].features.slice(0, 5).map((feature, index) => (
                <li key={index} className="flex items-center space-x-2 text-sm">
                  <Check className="w-4 h-4 text-success-600" />
                  <span className="text-secondary-700">{feature}</span>
                </li>
              ))}
              {plans[subscription.plan].features.length > 5 && (
                <li className="text-sm text-secondary-500">
                  +{plans[subscription.plan].features.length - 5} more features
                </li>
              )}
            </ul>
          </div>
        </div>
      </div>

      {/* Plan Options */}
      <div>
        <h3 className="text-lg font-semibold text-secondary-900 mb-6">Change Plan</h3>
        <div className="grid md:grid-cols-3 gap-6">
          {Object.entries(plans).map(([planKey, plan]) => {
            const isCurrentPlan = planKey === subscription.plan;
            const isLoading = loading === planKey;

            return (
              <div
                key={planKey}
                className={`relative p-6 border rounded-lg transition-all duration-200 ${
                  isCurrentPlan
                    ? 'border-primary-500 bg-primary-50'
                    : 'border-secondary-300 hover:border-secondary-400'
                }`}
              >
                {isCurrentPlan && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <span className="bg-primary-600 text-white text-xs font-bold px-3 py-1 rounded-full">
                      CURRENT
                    </span>
                  </div>
                )}

                <div className="text-center mb-6">
                  <div className={`w-12 h-12 ${plan.bgColor} rounded-full flex items-center justify-center mx-auto mb-3`}>
                    <plan.icon className={`w-6 h-6 ${plan.color}`} />
                  </div>
                  <h4 className="text-xl font-bold text-secondary-900 mb-2">{plan.name}</h4>
                  <div className="text-3xl font-bold text-secondary-900">
                    {paymentService.formatAmount(plan.price)}
                    <span className="text-sm font-normal text-secondary-600">/month</span>
                  </div>
                </div>

                <ul className="space-y-2 mb-6">
                  {plan.features.map((feature, index) => (
                    <li key={index} className="flex items-center space-x-2 text-sm">
                      <Check className="w-4 h-4 text-success-600 flex-shrink-0" />
                      <span className="text-secondary-700">{feature}</span>
                    </li>
                  ))}
                </ul>

                <button
                  onClick={() => handlePlanChange(planKey as any)}
                  disabled={isCurrentPlan || isLoading}
                  className={`w-full py-2 px-4 rounded-lg font-medium transition-colors duration-200 ${
                    isCurrentPlan
                      ? 'bg-secondary-100 text-secondary-500 cursor-not-allowed'
                      : 'btn-primary'
                  } disabled:opacity-50 disabled:cursor-not-allowed`}
                >
                  {isLoading ? (
                    <div className="flex items-center justify-center">
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                      Updating...
                    </div>
                  ) : isCurrentPlan ? (
                    'Current Plan'
                  ) : (
                    `Upgrade to ${plan.name}`
                  )}
                </button>
              </div>
            );
          })}
        </div>
      </div>

      {/* Cancel Subscription */}
      <div className="card p-6 border-error-200">
        <h3 className="text-lg font-semibold text-secondary-900 mb-4">Cancel Subscription</h3>
        <p className="text-secondary-600 mb-4">
          If you cancel your subscription, you'll continue to have access to your current plan until the end of your billing period.
          After that, your account will be downgraded to the free tier.
        </p>

        {!showCancelConfirm ? (
          <button
            onClick={() => setShowCancelConfirm(true)}
            className="btn-secondary text-error-600 border-error-300 hover:bg-error-50"
          >
            Cancel Subscription
          </button>
        ) : (
          <div className="space-y-4">
            <div className="p-4 bg-error-50 border border-error-200 rounded-lg">
              <h4 className="font-medium text-error-900 mb-2">Are you sure?</h4>
              <p className="text-sm text-error-700">
                This action cannot be undone. Your subscription will be cancelled and you'll lose access to premium features
                at the end of your current billing period ({formatDate(subscription.currentPeriodEnd)}).
              </p>
            </div>

            <div className="flex space-x-3">
              <button
                onClick={() => setShowCancelConfirm(false)}
                className="btn-secondary"
                disabled={loading === 'cancel'}
              >
                Keep Subscription
              </button>
              <button
                onClick={handleCancel}
                disabled={loading === 'cancel'}
                className="bg-error-600 text-white px-4 py-2 rounded-lg hover:bg-error-700 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading === 'cancel' ? (
                  <div className="flex items-center">
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                    Cancelling...
                  </div>
                ) : (
                  <>
                    <X className="w-4 h-4 mr-2 inline" />
                    Yes, Cancel Subscription
                  </>
                )}
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
