import React from 'react';
import { 
  ArrowUpRight, 
  ArrowDownLeft, 
  RefreshCw, 
  ExternalLink,
  Calendar,
  CreditCard
} from 'lucide-react';
import { Transaction, paymentService } from '../../services/paymentService';

interface TransactionListProps {
  transactions: Transaction[];
}

export default function TransactionList({ transactions }: TransactionListProps) {
  const getTransactionIcon = (transaction: Transaction) => {
    switch (transaction.type) {
      case 'payment':
        return <ArrowUpRight className="w-5 h-5 text-success-600" />;
      case 'withdrawal':
        return <ArrowDownLeft className="w-5 h-5 text-blue-600" />;
      case 'refund':
        return <RefreshCw className="w-5 h-5 text-warning-600" />;
      default:
        return <CreditCard className="w-5 h-5 text-secondary-600" />;
    }
  };

  const getAmountDisplay = (transaction: Transaction) => {
    const sign = transaction.type === 'payment' ? '+' : '-';
    const color = transaction.type === 'payment' ? 'text-success-600' : 'text-secondary-900';
    
    return (
      <span className={`font-semibold ${color}`}>
        {sign}{paymentService.formatAmount(transaction.amount)}
      </span>
    );
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));

    if (diffInDays === 0) {
      return 'Today';
    } else if (diffInDays === 1) {
      return 'Yesterday';
    } else if (diffInDays < 7) {
      return `${diffInDays} days ago`;
    } else {
      return date.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: date.getFullYear() !== now.getFullYear() ? 'numeric' : undefined
      });
    }
  };

  if (transactions.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="w-16 h-16 bg-secondary-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <CreditCard className="w-8 h-8 text-secondary-400" />
        </div>
        <h3 className="text-lg font-semibold text-secondary-900 mb-2">No transactions yet</h3>
        <p className="text-secondary-600">Your transaction history will appear here.</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {transactions.map((transaction) => (
        <div
          key={transaction.id}
          className="flex items-center justify-between p-4 bg-white border border-secondary-200 rounded-lg hover:shadow-sm transition-shadow duration-200"
        >
          <div className="flex items-center space-x-4">
            {/* Icon */}
            <div className="w-12 h-12 bg-secondary-50 rounded-full flex items-center justify-center">
              {getTransactionIcon(transaction)}
            </div>

            {/* Transaction Details */}
            <div className="flex-1">
              <div className="flex items-center space-x-2 mb-1">
                <h4 className="font-medium text-secondary-900">{transaction.description}</h4>
                {transaction.projectId && (
                  <button className="text-primary-600 hover:text-primary-700">
                    <ExternalLink className="w-4 h-4" />
                  </button>
                )}
              </div>
              
              <div className="flex items-center space-x-4 text-sm text-secondary-600">
                <div className="flex items-center space-x-1">
                  <Calendar className="w-3 h-3" />
                  <span>{formatDate(transaction.date)}</span>
                </div>
                
                <div className="flex items-center space-x-1">
                  <CreditCard className="w-3 h-3" />
                  <span>{paymentService.getPaymentMethodDisplay(transaction.paymentMethod)}</span>
                </div>
                
                {transaction.fees > 0 && (
                  <span className="text-xs">
                    Fee: {paymentService.formatAmount(transaction.fees)}
                  </span>
                )}
              </div>

              {/* Project Title */}
              {transaction.projectTitle && (
                <p className="text-xs text-secondary-500 mt-1">
                  Project: {transaction.projectTitle}
                </p>
              )}
            </div>
          </div>

          {/* Amount and Status */}
          <div className="text-right">
            <div className="mb-1">
              {getAmountDisplay(transaction)}
            </div>
            
            <span className={`inline-block px-2 py-1 rounded-full text-xs font-medium ${paymentService.getStatusBadgeColor(transaction.status)}`}>
              {transaction.status.charAt(0).toUpperCase() + transaction.status.slice(1)}
            </span>
          </div>
        </div>
      ))}
    </div>
  );
}
