import React, { useState } from 'react';
import { X, ArrowDownLeft, AlertCircle, Building } from 'lucide-react';
import { PaymentMethod, paymentService } from '../../services/paymentService';

interface WithdrawalModalProps {
  isOpen: boolean;
  onClose: () => void;
  onWithdraw: (amount: number, methodId: string) => Promise<void>;
  paymentMethods: PaymentMethod[];
  availableBalance: number;
}

export default function WithdrawalModal({ 
  isOpen, 
  onClose, 
  onWithdraw, 
  paymentMethods, 
  availableBalance 
}: WithdrawalModalProps) {
  const [amount, setAmount] = useState('');
  const [selectedMethodId, setSelectedMethodId] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const withdrawalFee = 25; // $25 flat fee
  const minWithdrawal = 50; // $50 minimum
  const maxWithdrawal = availableBalance;

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    const withdrawalAmount = parseFloat(amount);
    
    // Validation
    if (!withdrawalAmount || withdrawalAmount < minWithdrawal) {
      setError(`Minimum withdrawal amount is $${minWithdrawal}`);
      return;
    }
    
    if (withdrawalAmount > maxWithdrawal) {
      setError(`Insufficient balance. Available: $${availableBalance.toFixed(2)}`);
      return;
    }
    
    if (!selectedMethodId) {
      setError('Please select a withdrawal method');
      return;
    }

    try {
      setLoading(true);
      setError('');
      await onWithdraw(withdrawalAmount * 100, selectedMethodId); // Convert to cents
      
      // Reset form
      setAmount('');
      setSelectedMethodId('');
    } catch (error: any) {
      setError(error.message || 'Failed to process withdrawal');
    } finally {
      setLoading(false);
    }
  };

  const handleAmountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    // Only allow numbers and decimal point
    if (/^\d*\.?\d*$/.test(value)) {
      setAmount(value);
      setError('');
    }
  };

  const setQuickAmount = (percentage: number) => {
    const quickAmount = Math.floor(availableBalance * percentage);
    setAmount(quickAmount.toString());
    setError('');
  };

  const netAmount = parseFloat(amount) ? parseFloat(amount) - withdrawalFee : 0;

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-md w-full">
        {/* Header */}
        <div className="p-6 border-b border-secondary-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <ArrowDownLeft className="w-6 h-6 text-blue-600" />
              <h2 className="text-xl font-semibold text-secondary-900">Withdraw Funds</h2>
            </div>
            <button
              onClick={onClose}
              className="text-secondary-400 hover:text-secondary-600"
            >
              <X className="w-6 h-6" />
            </button>
          </div>
        </div>

        {/* Content */}
        <form onSubmit={handleSubmit} className="p-6">
          {/* Available Balance */}
          <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-blue-900">Available Balance</span>
              <span className="text-lg font-bold text-blue-900">
                ${availableBalance.toFixed(2)}
              </span>
            </div>
          </div>

          {/* Amount Input */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-secondary-700 mb-2">
              Withdrawal Amount *
            </label>
            <div className="relative">
              <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-secondary-500">$</span>
              <input
                type="text"
                value={amount}
                onChange={handleAmountChange}
                placeholder="0.00"
                className="input-field w-full pl-8"
                required
              />
            </div>
            
            {/* Quick Amount Buttons */}
            <div className="flex space-x-2 mt-3">
              <button
                type="button"
                onClick={() => setQuickAmount(0.25)}
                className="btn-secondary text-xs px-3 py-1"
              >
                25%
              </button>
              <button
                type="button"
                onClick={() => setQuickAmount(0.5)}
                className="btn-secondary text-xs px-3 py-1"
              >
                50%
              </button>
              <button
                type="button"
                onClick={() => setQuickAmount(0.75)}
                className="btn-secondary text-xs px-3 py-1"
              >
                75%
              </button>
              <button
                type="button"
                onClick={() => setQuickAmount(1)}
                className="btn-secondary text-xs px-3 py-1"
              >
                Max
              </button>
            </div>
          </div>

          {/* Withdrawal Method */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-secondary-700 mb-2">
              Withdrawal Method *
            </label>
            {paymentMethods.length === 0 ? (
              <div className="p-4 bg-warning-50 border border-warning-200 rounded-lg">
                <div className="flex items-center">
                  <AlertCircle className="w-5 h-5 text-warning-600 mr-3" />
                  <p className="text-warning-700">
                    No bank accounts found. Please add a bank account to withdraw funds.
                  </p>
                </div>
              </div>
            ) : (
              <div className="space-y-2">
                {paymentMethods.map((method) => (
                  <label
                    key={method.id}
                    className={`flex items-center p-3 border rounded-lg cursor-pointer transition-colors duration-200 ${
                      selectedMethodId === method.id
                        ? 'border-primary-500 bg-primary-50'
                        : 'border-secondary-300 hover:border-secondary-400'
                    }`}
                  >
                    <input
                      type="radio"
                      name="withdrawalMethod"
                      value={method.id}
                      checked={selectedMethodId === method.id}
                      onChange={(e) => setSelectedMethodId(e.target.value)}
                      className="sr-only"
                    />
                    <div className="flex items-center space-x-3">
                      <Building className="w-5 h-5 text-secondary-600" />
                      <div>
                        <p className="font-medium text-secondary-900">{method.bankName}</p>
                        <p className="text-sm text-secondary-600">Bank Account</p>
                      </div>
                    </div>
                  </label>
                ))}
              </div>
            )}
          </div>

          {/* Fee Breakdown */}
          {parseFloat(amount) > 0 && (
            <div className="mb-6 p-4 bg-secondary-50 border border-secondary-200 rounded-lg">
              <h4 className="font-medium text-secondary-900 mb-3">Withdrawal Summary</h4>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-secondary-600">Withdrawal Amount</span>
                  <span className="text-secondary-900">${parseFloat(amount).toFixed(2)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-secondary-600">Processing Fee</span>
                  <span className="text-secondary-900">-${withdrawalFee.toFixed(2)}</span>
                </div>
                <div className="border-t border-secondary-300 pt-2 mt-2">
                  <div className="flex justify-between font-medium">
                    <span className="text-secondary-900">Net Amount</span>
                    <span className="text-secondary-900">${Math.max(0, netAmount).toFixed(2)}</span>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Error Message */}
          {error && (
            <div className="mb-6 p-4 bg-error-50 border border-error-200 rounded-lg">
              <div className="flex items-center">
                <AlertCircle className="w-5 h-5 text-error-600 mr-3" />
                <p className="text-error-700">{error}</p>
              </div>
            </div>
          )}

          {/* Processing Info */}
          <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <h4 className="font-medium text-blue-900 mb-2">Processing Information</h4>
            <ul className="text-sm text-blue-700 space-y-1">
              <li>• Withdrawals are processed within 1-3 business days</li>
              <li>• Minimum withdrawal amount: ${minWithdrawal}</li>
              <li>• Processing fee: ${withdrawalFee} per withdrawal</li>
              <li>• Funds will be deposited to your selected bank account</li>
            </ul>
          </div>

          {/* Actions */}
          <div className="flex justify-end space-x-3">
            <button
              type="button"
              onClick={onClose}
              className="btn-secondary"
              disabled={loading}
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading || !amount || !selectedMethodId || paymentMethods.length === 0}
              className="btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? (
                <div className="flex items-center">
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                  Processing...
                </div>
              ) : (
                <>
                  <ArrowDownLeft className="w-4 h-4 mr-2" />
                  Withdraw ${parseFloat(amount) ? parseFloat(amount).toFixed(2) : '0.00'}
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
