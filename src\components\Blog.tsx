import React from 'react';
import { ArrowRight, Calendar, Clock, User } from 'lucide-react';

const blogPosts = [
  {
    image: "https://images.unsplash.com/photo-1542744173-8e7e53415bb0?auto=format&fit=crop&w=800&q=80",
    title: "Mastering Global Project Management",
    excerpt: "Essential strategies for managing international projects with distributed teams and complex requirements.",
    date: "March 15, 2024",
    author: "<PERSON>",
    readTime: "5 min read",
    category: "Project Management"
  },
  {
    image: "https://images.unsplash.com/photo-1454165804606-c3d57bc86b40?auto=format&fit=crop&w=800&q=80",
    title: "The Future of Remote Collaboration",
    excerpt: "How modern tools and platforms are reshaping the way global teams work together on complex projects.",
    date: "March 12, 2024",
    author: "<PERSON>",
    readTime: "7 min read",
    category: "Technology"
  },
  {
    image: "https://images.unsplash.com/photo-1560472355-536de3962603?auto=format&fit=crop&w=800&q=80",
    title: "Building Trust in Digital Marketplaces",
    excerpt: "Best practices for establishing credibility and maintaining strong relationships in online business environments.",
    date: "March 10, 2024",
    author: "<PERSON>",
    readTime: "6 min read",
    category: "Business Strategy"
  }
];

export default function Blog() {
  return (
    <section className="section-padding gradient-bg">
      <div className="container-custom">
        {/* Header */}
        <div className="text-center mb-16 animate-fade-in-up">
          <span className="inline-block px-4 py-2 bg-primary-100 text-primary-700 rounded-full text-sm font-semibold mb-4">
            📚 Knowledge Hub
          </span>
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-display font-bold text-secondary-900 mb-6">
            Latest <span className="gradient-text">Insights</span> & Updates
          </h2>
          <p className="text-lg text-secondary-600 max-w-3xl mx-auto">
            Stay ahead with expert insights, industry trends, and practical tips to grow your business
            and succeed in the global marketplace.
          </p>
        </div>

        {/* Blog Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
          {blogPosts.map((post, index) => (
            <article
              key={index}
              className="card-hover overflow-hidden group animate-fade-in-up"
              style={{ animationDelay: `${index * 150}ms` }}
            >
              {/* Image */}
              <div className="relative overflow-hidden">
                <img
                  src={post.image}
                  alt={post.title}
                  className="w-full h-48 object-cover group-hover:scale-110 transition-transform duration-500"
                />
                <div className="absolute top-4 left-4">
                  <span className="px-3 py-1 bg-white/90 backdrop-blur-sm text-primary-600 rounded-full text-xs font-semibold">
                    {post.category}
                  </span>
                </div>
                <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              </div>

              {/* Content */}
              <div className="p-6">
                {/* Meta Info */}
                <div className="flex items-center space-x-4 text-sm text-secondary-500 mb-3">
                  <div className="flex items-center space-x-1">
                    <Calendar className="w-4 h-4" />
                    <span>{post.date}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Clock className="w-4 h-4" />
                    <span>{post.readTime}</span>
                  </div>
                </div>

                {/* Title */}
                <h3 className="text-xl font-display font-semibold text-secondary-900 mb-3 group-hover:text-primary-600 transition-colors duration-300">
                  {post.title}
                </h3>

                {/* Excerpt */}
                <p className="text-secondary-600 mb-4 leading-relaxed">
                  {post.excerpt}
                </p>

                {/* Author & Read More */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <div className="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">
                      <User className="w-4 h-4 text-primary-600" />
                    </div>
                    <span className="text-sm text-secondary-600">{post.author}</span>
                  </div>

                  <button className="text-primary-600 font-semibold inline-flex items-center group-hover:text-primary-700 transition-colors duration-200">
                    Read More
                    <ArrowRight className="ml-1 w-4 h-4 group-hover:translate-x-1 transition-transform duration-200" />
                  </button>
                </div>
              </div>
            </article>
          ))}
        </div>

        {/* CTA */}
        <div className="text-center animate-fade-in-up animation-delay-600">
          <button className="btn-outline">
            View All Articles
          </button>
        </div>
      </div>
    </section>
  );
}