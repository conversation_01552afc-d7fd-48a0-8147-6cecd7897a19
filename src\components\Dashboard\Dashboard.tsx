import React from 'react';
import { 
  TrendingUp, 
  Users, 
  DollarSign, 
  Clock, 
  Plus, 
  ArrowUpRight,
  Calendar,
  MessageCircle,
  FileText,
  Award
} from 'lucide-react';

interface DashboardProps {
  userRole: 'client' | 'vendor' | 'admin';
}

export default function Dashboard({ userRole }: DashboardProps) {
  // Mock data - would come from API
  const stats = {
    client: {
      totalProjects: 12,
      activeProjects: 5,
      completedProjects: 7,
      totalBids: 48,
      avgBidAmount: 8500,
      successRate: 92
    },
    vendor: {
      totalBids: 23,
      acceptedBids: 8,
      pendingBids: 3,
      activeProjects: 5,
      totalEarnings: 45000,
      rating: 4.8
    }
  };

  const recentActivity = [
    { id: 1, type: 'bid', message: 'New bid received on "E-commerce Platform"', time: '2 hours ago', status: 'new' },
    { id: 2, type: 'project', message: 'Project "Mobile App Design" completed', time: '5 hours ago', status: 'success' },
    { id: 3, type: 'message', message: 'New message from <PERSON>', time: '1 day ago', status: 'info' },
    { id: 4, type: 'payment', message: 'Payment received for "Website Redesign"', time: '2 days ago', status: 'success' }
  ];

  const upcomingDeadlines = [
    { id: 1, project: 'E-commerce Platform', deadline: '2024-03-20', daysLeft: 5 },
    { id: 2, project: 'Mobile App Design', deadline: '2024-03-25', daysLeft: 10 },
    { id: 3, project: 'Brand Identity', deadline: '2024-03-30', daysLeft: 15 }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'new': return 'bg-primary-100 text-primary-700';
      case 'success': return 'bg-success-100 text-success-700';
      case 'info': return 'bg-secondary-100 text-secondary-700';
      default: return 'bg-secondary-100 text-secondary-700';
    }
  };

  const renderClientStats = () => (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <div className="card p-6">
        <div className="flex items-center justify-between mb-4">
          <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center">
            <FileText className="w-6 h-6 text-primary-600" />
          </div>
          <span className="text-xs text-success-600 font-semibold">+12%</span>
        </div>
        <h3 className="text-2xl font-bold text-secondary-900 mb-1">{stats.client.totalProjects}</h3>
        <p className="text-secondary-600">Total Projects</p>
      </div>

      <div className="card p-6">
        <div className="flex items-center justify-between mb-4">
          <div className="w-12 h-12 bg-warning-100 rounded-lg flex items-center justify-center">
            <Clock className="w-6 h-6 text-warning-600" />
          </div>
          <span className="text-xs text-success-600 font-semibold">+8%</span>
        </div>
        <h3 className="text-2xl font-bold text-secondary-900 mb-1">{stats.client.activeProjects}</h3>
        <p className="text-secondary-600">Active Projects</p>
      </div>

      <div className="card p-6">
        <div className="flex items-center justify-between mb-4">
          <div className="w-12 h-12 bg-success-100 rounded-lg flex items-center justify-center">
            <Users className="w-6 h-6 text-success-600" />
          </div>
          <span className="text-xs text-primary-600 font-semibold">{stats.client.totalBids}</span>
        </div>
        <h3 className="text-2xl font-bold text-secondary-900 mb-1">${stats.client.avgBidAmount.toLocaleString()}</h3>
        <p className="text-secondary-600">Avg. Bid Amount</p>
      </div>

      <div className="card p-6">
        <div className="flex items-center justify-between mb-4">
          <div className="w-12 h-12 bg-error-100 rounded-lg flex items-center justify-center">
            <TrendingUp className="w-6 h-6 text-error-600" />
          </div>
          <span className="text-xs text-success-600 font-semibold">+5%</span>
        </div>
        <h3 className="text-2xl font-bold text-secondary-900 mb-1">{stats.client.successRate}%</h3>
        <p className="text-secondary-600">Success Rate</p>
      </div>
    </div>
  );

  const renderVendorStats = () => (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <div className="card p-6">
        <div className="flex items-center justify-between mb-4">
          <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center">
            <FileText className="w-6 h-6 text-primary-600" />
          </div>
          <span className="text-xs text-success-600 font-semibold">+15%</span>
        </div>
        <h3 className="text-2xl font-bold text-secondary-900 mb-1">{stats.vendor.totalBids}</h3>
        <p className="text-secondary-600">Total Bids</p>
      </div>

      <div className="card p-6">
        <div className="flex items-center justify-between mb-4">
          <div className="w-12 h-12 bg-success-100 rounded-lg flex items-center justify-center">
            <Award className="w-6 h-6 text-success-600" />
          </div>
          <span className="text-xs text-success-600 font-semibold">+3</span>
        </div>
        <h3 className="text-2xl font-bold text-secondary-900 mb-1">{stats.vendor.acceptedBids}</h3>
        <p className="text-secondary-600">Accepted Bids</p>
      </div>

      <div className="card p-6">
        <div className="flex items-center justify-between mb-4">
          <div className="w-12 h-12 bg-warning-100 rounded-lg flex items-center justify-center">
            <DollarSign className="w-6 h-6 text-warning-600" />
          </div>
          <span className="text-xs text-success-600 font-semibold">+22%</span>
        </div>
        <h3 className="text-2xl font-bold text-secondary-900 mb-1">${stats.vendor.totalEarnings.toLocaleString()}</h3>
        <p className="text-secondary-600">Total Earnings</p>
      </div>

      <div className="card p-6">
        <div className="flex items-center justify-between mb-4">
          <div className="w-12 h-12 bg-error-100 rounded-lg flex items-center justify-center">
            <TrendingUp className="w-6 h-6 text-error-600" />
          </div>
          <span className="text-xs text-warning-600 font-semibold">★ {stats.vendor.rating}</span>
        </div>
        <h3 className="text-2xl font-bold text-secondary-900 mb-1">{stats.vendor.activeProjects}</h3>
        <p className="text-secondary-600">Active Projects</p>
      </div>
    </div>
  );

  return (
    <div className="min-h-screen bg-secondary-50">
      <div className="container-custom py-8">
        {/* Header */}
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-8">
          <div>
            <h1 className="text-3xl font-display font-bold text-secondary-900 mb-2">
              Welcome back! 👋
            </h1>
            <p className="text-secondary-600">
              Here's what's happening with your {userRole === 'client' ? 'projects' : 'bids'} today.
            </p>
          </div>
          <button className="btn-primary mt-4 lg:mt-0">
            <Plus className="w-5 h-5 mr-2" />
            {userRole === 'client' ? 'New Project' : 'Browse Projects'}
          </button>
        </div>

        {/* Stats */}
        {userRole === 'client' ? renderClientStats() : renderVendorStats()}

        {/* Main Content Grid */}
        <div className="grid lg:grid-cols-3 gap-8">
          {/* Recent Activity */}
          <div className="lg:col-span-2">
            <div className="card p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-display font-semibold text-secondary-900">Recent Activity</h2>
                <button className="text-primary-600 hover:text-primary-700 text-sm font-semibold">
                  View All
                </button>
              </div>
              <div className="space-y-4">
                {recentActivity.map((activity) => (
                  <div key={activity.id} className="flex items-start space-x-4 p-4 rounded-lg hover:bg-secondary-50 transition-colors duration-200">
                    <div className={`w-2 h-2 rounded-full mt-2 ${getStatusColor(activity.status).replace('text-', 'bg-').replace('100', '500')}`}></div>
                    <div className="flex-1">
                      <p className="text-secondary-900 font-medium">{activity.message}</p>
                      <p className="text-secondary-500 text-sm">{activity.time}</p>
                    </div>
                    <span className={`px-2 py-1 rounded-full text-xs font-semibold ${getStatusColor(activity.status)}`}>
                      {activity.status}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Quick Actions */}
            <div className="card p-6">
              <h3 className="text-lg font-display font-semibold text-secondary-900 mb-4">Quick Actions</h3>
              <div className="space-y-3">
                <button className="w-full btn-secondary justify-start">
                  <MessageCircle className="w-4 h-4 mr-2" />
                  Messages
                </button>
                <button className="w-full btn-secondary justify-start">
                  <FileText className="w-4 h-4 mr-2" />
                  Documents
                </button>
                <button className="w-full btn-secondary justify-start">
                  <Calendar className="w-4 h-4 mr-2" />
                  Schedule
                </button>
              </div>
            </div>

            {/* Upcoming Deadlines */}
            <div className="card p-6">
              <h3 className="text-lg font-display font-semibold text-secondary-900 mb-4">Upcoming Deadlines</h3>
              <div className="space-y-3">
                {upcomingDeadlines.map((item) => (
                  <div key={item.id} className="flex items-center justify-between p-3 bg-secondary-50 rounded-lg">
                    <div>
                      <p className="font-medium text-secondary-900 text-sm">{item.project}</p>
                      <p className="text-secondary-500 text-xs">{item.deadline}</p>
                    </div>
                    <span className={`px-2 py-1 rounded-full text-xs font-semibold ${
                      item.daysLeft <= 5 ? 'bg-error-100 text-error-700' :
                      item.daysLeft <= 10 ? 'bg-warning-100 text-warning-700' :
                      'bg-success-100 text-success-700'
                    }`}>
                      {item.daysLeft}d
                    </span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
