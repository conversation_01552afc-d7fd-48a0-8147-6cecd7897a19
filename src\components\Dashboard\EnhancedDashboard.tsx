import React, { useState, useEffect } from 'react';
import { 
  TrendingUp, 
  Users, 
  DollarSign, 
  Clock, 
  Plus, 
  ArrowUpRight,
  Calendar,
  MessageCircle,
  FileText,
  Award,
  Briefcase,
  Target,
  CheckCircle,
  AlertCircle
} from 'lucide-react';
import { projectService } from '../../services/projectService';
import { User } from '../../services/authService';

interface EnhancedDashboardProps {
  currentUser: User;
}

interface DashboardStats {
  totalProjects: number;
  activeProjects: number;
  completedProjects: number;
  totalBids: number;
  totalEarnings: number;
  successRate: number;
  rating?: number;
}

interface RecentActivity {
  id: string;
  type: 'bid' | 'project' | 'message' | 'payment';
  title: string;
  description: string;
  time: string;
  status: 'new' | 'success' | 'info' | 'warning';
}

export default function EnhancedDashboard({ currentUser }: EnhancedDashboardProps) {
  const [stats, setStats] = useState<DashboardStats>({
    totalProjects: 0,
    activeProjects: 0,
    completedProjects: 0,
    totalBids: 0,
    totalEarnings: 0,
    successRate: 0,
    rating: 0
  });
  const [recentProjects, setRecentProjects] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchDashboardData();
  }, [currentUser]);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      
      // Fetch projects based on user role
      const projectsResponse = await projectService.getProjects({
        limit: 10,
        page: 1
      });
      
      setRecentProjects(projectsResponse.projects);
      
      // Calculate stats based on user role and projects
      const calculatedStats = calculateStats(projectsResponse.projects);
      setStats(calculatedStats);
      
    } catch (err) {
      console.error('Error fetching dashboard data:', err);
      setError('Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  };

  const calculateStats = (projects: any[]): DashboardStats => {
    if (currentUser.role === 'client') {
      const userProjects = projects.filter(p => p.client._id === currentUser._id);
      return {
        totalProjects: userProjects.length,
        activeProjects: userProjects.filter(p => ['open', 'in-progress'].includes(p.status)).length,
        completedProjects: userProjects.filter(p => p.status === 'completed').length,
        totalBids: userProjects.reduce((sum, p) => sum + (p.bids?.length || 0), 0),
        totalEarnings: userProjects.reduce((sum, p) => sum + (p.status === 'completed' ? p.budget : 0), 0),
        successRate: userProjects.length > 0 ? (userProjects.filter(p => p.status === 'completed').length / userProjects.length) * 100 : 0
      };
    } else if (currentUser.role === 'vendor') {
      const vendorProjects = projects.filter(p => p.assignedVendor?._id === currentUser._id);
      const vendorBids = projects.reduce((bids, p) => {
        const userBids = p.bids?.filter((b: any) => b.vendor._id === currentUser._id) || [];
        return [...bids, ...userBids];
      }, []);
      
      return {
        totalProjects: vendorProjects.length,
        activeProjects: vendorProjects.filter(p => p.status === 'in-progress').length,
        completedProjects: vendorProjects.filter(p => p.status === 'completed').length,
        totalBids: vendorBids.length,
        totalEarnings: vendorProjects.reduce((sum, p) => sum + (p.status === 'completed' ? p.budget : 0), 0),
        successRate: vendorBids.length > 0 ? (vendorBids.filter((b: any) => b.status === 'accepted').length / vendorBids.length) * 100 : 0,
        rating: currentUser.rating || 0
      };
    } else {
      // Admin stats
      return {
        totalProjects: projects.length,
        activeProjects: projects.filter(p => ['open', 'in-progress'].includes(p.status)).length,
        completedProjects: projects.filter(p => p.status === 'completed').length,
        totalBids: projects.reduce((sum, p) => sum + (p.bids?.length || 0), 0),
        totalEarnings: projects.reduce((sum, p) => sum + (p.status === 'completed' ? p.budget : 0), 0),
        successRate: projects.length > 0 ? (projects.filter(p => p.status === 'completed').length / projects.length) * 100 : 0
      };
    }
  };

  const getRecentActivity = (): RecentActivity[] => {
    const activities: RecentActivity[] = [];
    
    recentProjects.slice(0, 5).forEach(project => {
      if (project.status === 'open') {
        activities.push({
          id: `project-${project._id}`,
          type: 'project',
          title: 'New Project Posted',
          description: project.title,
          time: new Date(project.createdAt).toLocaleDateString(),
          status: 'new'
        });
      } else if (project.status === 'completed') {
        activities.push({
          id: `completed-${project._id}`,
          type: 'project',
          title: 'Project Completed',
          description: project.title,
          time: new Date(project.updatedAt).toLocaleDateString(),
          status: 'success'
        });
      }
      
      if (project.bids && project.bids.length > 0) {
        activities.push({
          id: `bid-${project._id}`,
          type: 'bid',
          title: 'New Bids Received',
          description: `${project.bids.length} bids on ${project.title}`,
          time: new Date(project.createdAt).toLocaleDateString(),
          status: 'info'
        });
      }
    });
    
    return activities.slice(0, 4);
  };

  const getStatCards = () => {
    if (currentUser.role === 'client') {
      return [
        { title: 'Total Projects', value: stats.totalProjects, icon: Briefcase, color: 'blue' },
        { title: 'Active Projects', value: stats.activeProjects, icon: Clock, color: 'orange' },
        { title: 'Total Bids', value: stats.totalBids, icon: Users, color: 'green' },
        { title: 'Success Rate', value: `${stats.successRate.toFixed(1)}%`, icon: Target, color: 'purple' }
      ];
    } else if (currentUser.role === 'vendor') {
      return [
        { title: 'Total Bids', value: stats.totalBids, icon: FileText, color: 'blue' },
        { title: 'Active Projects', value: stats.activeProjects, icon: Clock, color: 'orange' },
        { title: 'Total Earnings', value: `$${stats.totalEarnings.toLocaleString()}`, icon: DollarSign, color: 'green' },
        { title: 'Rating', value: `${stats.rating?.toFixed(1) || 0}/5`, icon: Award, color: 'purple' }
      ];
    } else {
      return [
        { title: 'Total Projects', value: stats.totalProjects, icon: Briefcase, color: 'blue' },
        { title: 'Active Projects', value: stats.activeProjects, icon: Clock, color: 'orange' },
        { title: 'Total Bids', value: stats.totalBids, icon: Users, color: 'green' },
        { title: 'Platform Revenue', value: `$${stats.totalEarnings.toLocaleString()}`, icon: DollarSign, color: 'purple' }
      ];
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-secondary-50 p-6">
        <div className="max-w-7xl mx-auto">
          <div className="animate-pulse">
            <div className="h-8 bg-secondary-200 rounded w-1/4 mb-6"></div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
              {[...Array(4)].map((_, i) => (
                <div key={i} className="h-32 bg-secondary-200 rounded-lg"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-secondary-50 p-6">
        <div className="max-w-7xl mx-auto">
          <div className="bg-red-50 border border-red-200 rounded-lg p-6 text-center">
            <AlertCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-red-900 mb-2">Error Loading Dashboard</h3>
            <p className="text-red-700">{error}</p>
            <button 
              onClick={fetchDashboardData}
              className="mt-4 btn-primary"
            >
              Try Again
            </button>
          </div>
        </div>
      </div>
    );
  }

  const statCards = getStatCards();
  const recentActivity = getRecentActivity();

  return (
    <div className="min-h-screen bg-secondary-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-secondary-900 mb-2">
            Welcome back, {currentUser.name}!
          </h1>
          <p className="text-secondary-600">
            Here's what's happening with your {currentUser.role === 'admin' ? 'platform' : 'projects'} today.
          </p>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {statCards.map((stat, index) => (
            <div key={index} className="card">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-secondary-600 mb-1">{stat.title}</p>
                  <p className="text-2xl font-bold text-secondary-900">{stat.value}</p>
                </div>
                <div className={`w-12 h-12 rounded-lg bg-${stat.color}-100 flex items-center justify-center`}>
                  <stat.icon className={`w-6 h-6 text-${stat.color}-600`} />
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Recent Projects */}
          <div className="lg:col-span-2">
            <div className="card">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-semibold text-secondary-900">Recent Projects</h2>
                <button className="btn-secondary text-sm">View All</button>
              </div>
              
              <div className="space-y-4">
                {recentProjects.slice(0, 5).map((project) => (
                  <div key={project._id} className="flex items-center justify-between p-4 bg-secondary-50 rounded-lg">
                    <div className="flex-1">
                      <h3 className="font-medium text-secondary-900 mb-1">{project.title}</h3>
                      <p className="text-sm text-secondary-600 mb-2">{project.description.substring(0, 100)}...</p>
                      <div className="flex items-center space-x-4 text-xs text-secondary-500">
                        <span>Budget: ${project.budget.toLocaleString()}</span>
                        <span>Bids: {project.bids?.length || 0}</span>
                        <span className={`px-2 py-1 rounded-full ${
                          project.status === 'open' ? 'bg-green-100 text-green-800' :
                          project.status === 'in-progress' ? 'bg-blue-100 text-blue-800' :
                          project.status === 'completed' ? 'bg-gray-100 text-gray-800' :
                          'bg-yellow-100 text-yellow-800'
                        }`}>
                          {project.status}
                        </span>
                      </div>
                    </div>
                    <ArrowUpRight className="w-5 h-5 text-secondary-400" />
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Recent Activity */}
          <div>
            <div className="card">
              <h2 className="text-xl font-semibold text-secondary-900 mb-6">Recent Activity</h2>
              
              <div className="space-y-4">
                {recentActivity.map((activity) => (
                  <div key={activity.id} className="flex items-start space-x-3">
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                      activity.status === 'new' ? 'bg-blue-100' :
                      activity.status === 'success' ? 'bg-green-100' :
                      activity.status === 'info' ? 'bg-purple-100' :
                      'bg-yellow-100'
                    }`}>
                      {activity.type === 'bid' && <Users className="w-4 h-4 text-blue-600" />}
                      {activity.type === 'project' && <Briefcase className="w-4 h-4 text-green-600" />}
                      {activity.type === 'message' && <MessageCircle className="w-4 h-4 text-purple-600" />}
                      {activity.type === 'payment' && <DollarSign className="w-4 h-4 text-yellow-600" />}
                    </div>
                    <div className="flex-1">
                      <p className="text-sm font-medium text-secondary-900">{activity.title}</p>
                      <p className="text-xs text-secondary-600">{activity.description}</p>
                      <p className="text-xs text-secondary-500 mt-1">{activity.time}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
