import React, { useState } from 'react';
import { 
  Download, 
  Eye, 
  Edit, 
  Trash2, 
  Shield, 
  Clock, 
  User, 
  MoreVertical,
  Share,
  History
} from 'lucide-react';
import { Document, documentService } from '../../services/documentService';
import { Project } from '../../services/projectService';
import { User as UserType } from '../../services/authService';

interface DocumentCardProps {
  document: Document;
  user: UserType;
  project: Project;
  viewMode: 'grid' | 'list';
  onView: () => void;
  onDelete: () => void;
  onUpdate: () => void;
}

export default function DocumentCard({ 
  document, 
  user, 
  project, 
  viewMode, 
  onView, 
  onDelete, 
  onUpdate 
}: DocumentCardProps) {
  const [showActions, setShowActions] = useState(false);
  const [downloading, setDownloading] = useState(false);

  const isOwner = document.uploadedBy._id === user._id;
  const isProjectOwner = user._id === (typeof project.client === 'string' ? project.client : project.client._id);
  const isAssignedVendor = project.assignedVendor && 
    user._id === (typeof project.assignedVendor === 'string' ? project.assignedVendor : project.assignedVendor._id);
  const canEdit = isOwner || isProjectOwner || user.role === 'admin';
  const canDelete = isOwner || isProjectOwner || user.role === 'admin';

  const handleDownload = async () => {
    try {
      setDownloading(true);
      documentService.downloadDocument(document);
    } catch (error: any) {
      alert(error.message);
    } finally {
      setDownloading(false);
    }
  };

  const handleShare = () => {
    // Implement sharing functionality
    console.log('Share document:', document._id);
  };

  const handleViewHistory = () => {
    // Implement version history functionality
    console.log('View history:', document._id);
  };

  if (viewMode === 'list') {
    return (
      <div className="card p-4 hover:shadow-medium transition-shadow duration-200">
        <div className="flex items-center space-x-4">
          {/* File Icon */}
          <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center flex-shrink-0">
            <span className="text-2xl">{documentService.getFileIcon(document.fileType)}</span>
          </div>

          {/* Document Info */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center space-x-2 mb-1">
              <h3 className="font-semibold text-secondary-900 truncate">{document.title}</h3>
              <span className={`px-2 py-1 rounded-full text-xs font-medium ${documentService.getSecurityLevelColor(document.securityLevel)}`}>
                <Shield className="w-3 h-3 mr-1 inline" />
                {documentService.getSecurityLevelLabel(document.securityLevel)}
              </span>
            </div>
            <p className="text-sm text-secondary-600 truncate">{document.fileName}</p>
            <div className="flex items-center space-x-4 text-xs text-secondary-500 mt-1">
              <span>{documentService.formatFileSize(document.fileSize)}</span>
              <span className="flex items-center">
                <User className="w-3 h-3 mr-1" />
                {document.uploadedBy.name}
              </span>
              <span className="flex items-center">
                <Clock className="w-3 h-3 mr-1" />
                {documentService.formatUploadDate(document.createdAt)}
              </span>
              {document.versions.length > 0 && (
                <span className="flex items-center">
                  <History className="w-3 h-3 mr-1" />
                  v{document.versions.length + 1}
                </span>
              )}
            </div>
          </div>

          {/* Actions */}
          <div className="flex items-center space-x-2">
            <button
              onClick={onView}
              className="btn-secondary text-sm"
            >
              <Eye className="w-4 h-4 mr-2" />
              View
            </button>
            
            <button
              onClick={handleDownload}
              disabled={downloading}
              className="btn-secondary text-sm"
            >
              {downloading ? (
                <div className="w-4 h-4 border-2 border-secondary-400 border-t-transparent rounded-full animate-spin mr-2"></div>
              ) : (
                <Download className="w-4 h-4 mr-2" />
              )}
              Download
            </button>

            {canEdit && (
              <div className="relative">
                <button
                  onClick={() => setShowActions(!showActions)}
                  className="p-2 text-secondary-400 hover:text-secondary-600 transition-colors duration-200"
                >
                  <MoreVertical className="w-4 h-4" />
                </button>

                {showActions && (
                  <div className="absolute right-0 top-full mt-1 bg-white border border-secondary-200 rounded-lg shadow-lg z-10 min-w-32">
                    <button
                      onClick={handleShare}
                      className="w-full text-left px-3 py-2 text-sm text-secondary-700 hover:bg-secondary-50 transition-colors duration-200"
                    >
                      <Share className="w-4 h-4 mr-2 inline" />
                      Share
                    </button>
                    <button
                      onClick={handleViewHistory}
                      className="w-full text-left px-3 py-2 text-sm text-secondary-700 hover:bg-secondary-50 transition-colors duration-200"
                    >
                      <History className="w-4 h-4 mr-2 inline" />
                      History
                    </button>
                    {canDelete && (
                      <button
                        onClick={onDelete}
                        className="w-full text-left px-3 py-2 text-sm text-error-600 hover:bg-error-50 transition-colors duration-200"
                      >
                        <Trash2 className="w-4 h-4 mr-2 inline" />
                        Delete
                      </button>
                    )}
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    );
  }

  // Grid view
  return (
    <div className="card p-6 hover:shadow-medium transition-shadow duration-200 group">
      {/* Header */}
      <div className="flex items-start justify-between mb-4">
        <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center">
          <span className="text-2xl">{documentService.getFileIcon(document.fileType)}</span>
        </div>
        
        {canEdit && (
          <div className="relative opacity-0 group-hover:opacity-100 transition-opacity duration-200">
            <button
              onClick={() => setShowActions(!showActions)}
              className="p-1 text-secondary-400 hover:text-secondary-600 transition-colors duration-200"
            >
              <MoreVertical className="w-4 h-4" />
            </button>

            {showActions && (
              <div className="absolute right-0 top-full mt-1 bg-white border border-secondary-200 rounded-lg shadow-lg z-10 min-w-32">
                <button
                  onClick={handleShare}
                  className="w-full text-left px-3 py-2 text-sm text-secondary-700 hover:bg-secondary-50 transition-colors duration-200"
                >
                  <Share className="w-4 h-4 mr-2 inline" />
                  Share
                </button>
                <button
                  onClick={handleViewHistory}
                  className="w-full text-left px-3 py-2 text-sm text-secondary-700 hover:bg-secondary-50 transition-colors duration-200"
                >
                  <History className="w-4 h-4 mr-2 inline" />
                  History
                </button>
                {canDelete && (
                  <button
                    onClick={onDelete}
                    className="w-full text-left px-3 py-2 text-sm text-error-600 hover:bg-error-50 transition-colors duration-200"
                  >
                    <Trash2 className="w-4 h-4 mr-2 inline" />
                    Delete
                  </button>
                )}
              </div>
            )}
          </div>
        )}
      </div>

      {/* Document Info */}
      <div className="mb-4">
        <h3 className="font-semibold text-secondary-900 mb-2 line-clamp-2">{document.title}</h3>
        <p className="text-sm text-secondary-600 mb-2 truncate">{document.fileName}</p>
        
        {/* Security Level */}
        <div className="flex items-center justify-between mb-2">
          <span className={`px-2 py-1 rounded-full text-xs font-medium ${documentService.getSecurityLevelColor(document.securityLevel)}`}>
            <Shield className="w-3 h-3 mr-1 inline" />
            {documentService.getSecurityLevelLabel(document.securityLevel)}
          </span>
          {document.versions.length > 0 && (
            <span className="text-xs text-secondary-500 flex items-center">
              <History className="w-3 h-3 mr-1" />
              v{document.versions.length + 1}
            </span>
          )}
        </div>

        {/* Description */}
        {document.description && (
          <p className="text-sm text-secondary-600 line-clamp-2 mb-2">{document.description}</p>
        )}
      </div>

      {/* Metadata */}
      <div className="space-y-2 mb-4 text-xs text-secondary-500">
        <div className="flex items-center justify-between">
          <span>Size:</span>
          <span>{documentService.formatFileSize(document.fileSize)}</span>
        </div>
        <div className="flex items-center justify-between">
          <span>Uploaded by:</span>
          <span className="truncate ml-2">{document.uploadedBy.name}</span>
        </div>
        <div className="flex items-center justify-between">
          <span>Date:</span>
          <span>{documentService.formatUploadDate(document.createdAt)}</span>
        </div>
      </div>

      {/* Actions */}
      <div className="flex space-x-2">
        <button
          onClick={onView}
          className="btn-secondary text-sm flex-1"
        >
          <Eye className="w-4 h-4 mr-2" />
          View
        </button>
        
        <button
          onClick={handleDownload}
          disabled={downloading}
          className="btn-primary text-sm flex-1"
        >
          {downloading ? (
            <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
          ) : (
            <Download className="w-4 h-4 mr-2" />
          )}
          Download
        </button>
      </div>
    </div>
  );
}
