import React, { useState, useRef } from 'react';
import { 
  X, 
  Upload, 
  FileText, 
  Shield, 
  AlertCircle,
  CheckCircle,
  Loader
} from 'lucide-react';
import { Document, documentService, UploadDocumentData } from '../../services/documentService';
import { Project } from '../../services/projectService';
import { User } from '../../services/authService';

interface DocumentUploadProps {
  project: Project;
  user: User;
  onClose: () => void;
  onUpload: (document: Document) => void;
}

export default function DocumentUpload({ project, user, onClose, onUpload }: DocumentUploadProps) {
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    securityLevel: 1,
    metadata: {}
  });
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [uploading, setUploading] = useState(false);
  const [error, setError] = useState('');
  const [dragActive, setDragActive] = useState(false);
  
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: name === 'securityLevel' ? parseInt(value) : value
    }));
    if (error) setError('');
  };

  const handleFileSelect = (file: File) => {
    const validation = documentService.validateFile(file);
    if (!validation.isValid) {
      setError(validation.error || 'Invalid file');
      return;
    }

    setSelectedFile(file);
    if (!formData.title) {
      setFormData(prev => ({
        ...prev,
        title: file.name.replace(/\.[^/.]+$/, '') // Remove file extension
      }));
    }
    setError('');
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      handleFileSelect(files[0]);
    }
  };

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      handleFileSelect(files[0]);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!selectedFile) {
      setError('Please select a file to upload');
      return;
    }

    if (!formData.title.trim()) {
      setError('Please enter a document title');
      return;
    }

    try {
      setUploading(true);
      
      const uploadData: UploadDocumentData = {
        projectId: project._id,
        title: formData.title.trim(),
        description: formData.description.trim() || undefined,
        securityLevel: formData.securityLevel,
        metadata: Object.keys(formData.metadata).length > 0 ? formData.metadata : undefined,
        file: selectedFile
      };

      const result = await documentService.uploadDocument(uploadData);
      onUpload(result.document);
    } catch (error: any) {
      setError(error.message);
    } finally {
      setUploading(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="p-6 border-b border-secondary-200">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold text-secondary-900">Upload Document</h2>
            <button
              onClick={onClose}
              className="text-secondary-400 hover:text-secondary-600"
            >
              <X className="w-6 h-6" />
            </button>
          </div>
          <p className="text-secondary-600 mt-1">
            Upload a document to {project.title}
          </p>
        </div>

        {/* Content */}
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Error Message */}
          {error && (
            <div className="p-4 bg-error-50 border border-error-200 rounded-lg flex items-center">
              <AlertCircle className="w-5 h-5 text-error-600 mr-3" />
              <p className="text-error-700">{error}</p>
            </div>
          )}

          {/* File Upload Area */}
          <div>
            <label className="block text-sm font-medium text-secondary-700 mb-2">
              Select File *
            </label>
            <div
              className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors duration-200 ${
                dragActive
                  ? 'border-primary-500 bg-primary-50'
                  : selectedFile
                  ? 'border-success-300 bg-success-50'
                  : 'border-secondary-300 hover:border-secondary-400'
              }`}
              onDrop={handleDrop}
              onDragEnter={handleDrag}
              onDragLeave={handleDrag}
              onDragOver={handleDrag}
            >
              {selectedFile ? (
                <div className="space-y-3">
                  <div className="w-16 h-16 bg-success-100 rounded-full flex items-center justify-center mx-auto">
                    <CheckCircle className="w-8 h-8 text-success-600" />
                  </div>
                  <div>
                    <p className="font-medium text-secondary-900">{selectedFile.name}</p>
                    <p className="text-sm text-secondary-600">
                      {documentService.formatFileSize(selectedFile.size)} • {selectedFile.type}
                    </p>
                  </div>
                  <button
                    type="button"
                    onClick={() => {
                      setSelectedFile(null);
                      if (fileInputRef.current) {
                        fileInputRef.current.value = '';
                      }
                    }}
                    className="text-sm text-error-600 hover:text-error-700"
                  >
                    Remove file
                  </button>
                </div>
              ) : (
                <div className="space-y-3">
                  <div className="w-16 h-16 bg-secondary-100 rounded-full flex items-center justify-center mx-auto">
                    <Upload className="w-8 h-8 text-secondary-400" />
                  </div>
                  <div>
                    <p className="text-secondary-600 mb-2">
                      Drag and drop your file here, or click to browse
                    </p>
                    <button
                      type="button"
                      onClick={() => fileInputRef.current?.click()}
                      className="btn-secondary"
                    >
                      Choose File
                    </button>
                  </div>
                  <p className="text-xs text-secondary-500">
                    Supported: PDF, Word, Excel, PowerPoint, Images, Text files (Max 10MB)
                  </p>
                </div>
              )}
            </div>
            <input
              ref={fileInputRef}
              type="file"
              onChange={handleFileInputChange}
              accept=".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.jpg,.jpeg,.png,.txt,.zip,.rar"
              className="hidden"
            />
          </div>

          {/* Document Details */}
          <div className="grid md:grid-cols-2 gap-6">
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-secondary-700 mb-2">
                Document Title *
              </label>
              <input
                type="text"
                name="title"
                value={formData.title}
                onChange={handleInputChange}
                placeholder="Enter document title"
                className="input-field w-full"
                required
              />
            </div>

            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-secondary-700 mb-2">
                Description (Optional)
              </label>
              <textarea
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                rows={3}
                placeholder="Describe the document content and purpose..."
                className="input-field w-full resize-none"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-secondary-700 mb-2">
                Security Level
              </label>
              <select
                name="securityLevel"
                value={formData.securityLevel}
                onChange={handleInputChange}
                className="input-field w-full"
              >
                <option value={1}>Basic - Standard protection</option>
                <option value={2}>Standard - Enhanced security</option>
                <option value={3}>High - Advanced encryption</option>
                <option value={4}>Maximum - Military-grade security</option>
              </select>
              <p className="text-xs text-secondary-500 mt-1">
                Higher security levels provide better protection but may increase processing time
              </p>
            </div>

            <div className="flex items-center">
              <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mr-3">
                <Shield className="w-6 h-6 text-primary-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-secondary-900">
                  {documentService.getSecurityLevelLabel(formData.securityLevel)} Security
                </p>
                <p className="text-xs text-secondary-600">
                  Document will be protected with steganographic embedding
                </p>
              </div>
            </div>
          </div>

          {/* Security Information */}
          <div className="p-4 bg-primary-50 border border-primary-200 rounded-lg">
            <div className="flex items-start">
              <Shield className="w-5 h-5 text-primary-600 mr-3 mt-0.5" />
              <div>
                <h4 className="font-medium text-primary-900 mb-1">Document Security</h4>
                <p className="text-sm text-primary-700">
                  Your document will be protected using advanced steganographic techniques that embed 
                  metadata invisibly within the file. This ensures document authenticity and provides 
                  tamper detection capabilities.
                </p>
              </div>
            </div>
          </div>

          {/* Actions */}
          <div className="flex justify-end space-x-3 pt-4 border-t border-secondary-200">
            <button
              type="button"
              onClick={onClose}
              className="btn-secondary"
              disabled={uploading}
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={uploading || !selectedFile}
              className="btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {uploading ? (
                <div className="flex items-center">
                  <Loader className="w-5 h-5 mr-2 animate-spin" />
                  Uploading...
                </div>
              ) : (
                <>
                  <Upload className="w-5 h-5 mr-2" />
                  Upload Document
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
