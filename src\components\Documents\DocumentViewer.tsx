import React, { useState } from 'react';
import { 
  X, 
  Download, 
  Share, 
  Edit, 
  Shield, 
  User, 
  Clock, 
  FileText,
  Eye,
  ExternalLink,
  Lock
} from 'lucide-react';
import { Document, documentService } from '../../services/documentService';
import { User as UserType } from '../../services/authService';

interface DocumentViewerProps {
  document: Document;
  user: UserType;
  onClose: () => void;
  onUpdate: () => void;
}

export default function DocumentViewer({ document, user, onClose, onUpdate }: DocumentViewerProps) {
  const [downloading, setDownloading] = useState(false);
  const [extracting, setExtracting] = useState(false);
  const [extractedData, setExtractedData] = useState<any>(null);

  const isOwner = document.uploadedBy._id === user._id;
  const canExtract = isOwner || user.role === 'admin';

  const handleDownload = async () => {
    try {
      setDownloading(true);
      documentService.downloadDocument(document);
    } catch (error: any) {
      alert(error.message);
    } finally {
      setDownloading(false);
    }
  };

  const handleExtractData = async () => {
    try {
      setExtracting(true);
      const result = await documentService.extractDocumentData(document._id);
      setExtractedData(result.data);
    } catch (error: any) {
      alert(error.message);
    } finally {
      setExtracting(false);
    }
  };

  const handleOpenInNewTab = () => {
    if (document.url) {
      window.open(document.url, '_blank');
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-hidden flex flex-col">
        {/* Header */}
        <div className="p-6 border-b border-secondary-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center">
                <span className="text-2xl">{documentService.getFileIcon(document.fileType)}</span>
              </div>
              <div>
                <h2 className="text-xl font-semibold text-secondary-900">{document.title}</h2>
                <p className="text-secondary-600">{document.fileName}</p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="text-secondary-400 hover:text-secondary-600"
            >
              <X className="w-6 h-6" />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-hidden flex">
          {/* Document Preview */}
          <div className="flex-1 p-6">
            {documentService.canPreview(document.fileType) ? (
              <div className="h-full border border-secondary-200 rounded-lg overflow-hidden">
                {documentService.isImageFile(document.fileType) ? (
                  <img
                    src={document.url}
                    alt={document.title}
                    className="w-full h-full object-contain"
                  />
                ) : documentService.isPdfFile(document.fileType) ? (
                  <iframe
                    src={document.url}
                    className="w-full h-full"
                    title={document.title}
                  />
                ) : null}
              </div>
            ) : (
              <div className="h-full border-2 border-dashed border-secondary-300 rounded-lg flex items-center justify-center">
                <div className="text-center">
                  <div className="w-24 h-24 bg-secondary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <FileText className="w-12 h-12 text-secondary-400" />
                  </div>
                  <h3 className="text-lg font-semibold text-secondary-900 mb-2">Preview Not Available</h3>
                  <p className="text-secondary-600 mb-4">
                    This file type cannot be previewed in the browser.
                  </p>
                  <div className="space-x-2">
                    <button
                      onClick={handleDownload}
                      disabled={downloading}
                      className="btn-primary"
                    >
                      {downloading ? (
                        <div className="flex items-center">
                          <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                          Downloading...
                        </div>
                      ) : (
                        <>
                          <Download className="w-4 h-4 mr-2" />
                          Download
                        </>
                      )}
                    </button>
                    <button
                      onClick={handleOpenInNewTab}
                      className="btn-secondary"
                    >
                      <ExternalLink className="w-4 h-4 mr-2" />
                      Open in New Tab
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Sidebar */}
          <div className="w-80 border-l border-secondary-200 p-6 overflow-y-auto">
            {/* Document Info */}
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold text-secondary-900 mb-4">Document Details</h3>
                
                <div className="space-y-3">
                  <div>
                    <label className="text-sm font-medium text-secondary-700">Title</label>
                    <p className="text-secondary-900">{document.title}</p>
                  </div>
                  
                  {document.description && (
                    <div>
                      <label className="text-sm font-medium text-secondary-700">Description</label>
                      <p className="text-secondary-900">{document.description}</p>
                    </div>
                  )}
                  
                  <div>
                    <label className="text-sm font-medium text-secondary-700">File Name</label>
                    <p className="text-secondary-900">{document.fileName}</p>
                  </div>
                  
                  <div>
                    <label className="text-sm font-medium text-secondary-700">File Size</label>
                    <p className="text-secondary-900">{documentService.formatFileSize(document.fileSize)}</p>
                  </div>
                  
                  <div>
                    <label className="text-sm font-medium text-secondary-700">Security Level</label>
                    <div className="flex items-center space-x-2">
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${documentService.getSecurityLevelColor(document.securityLevel)}`}>
                        <Shield className="w-3 h-3 mr-1 inline" />
                        {documentService.getSecurityLevelLabel(document.securityLevel)}
                      </span>
                    </div>
                  </div>
                  
                  <div>
                    <label className="text-sm font-medium text-secondary-700">Uploaded By</label>
                    <div className="flex items-center space-x-2">
                      <User className="w-4 h-4 text-secondary-400" />
                      <span className="text-secondary-900">{document.uploadedBy.name}</span>
                    </div>
                  </div>
                  
                  <div>
                    <label className="text-sm font-medium text-secondary-700">Upload Date</label>
                    <div className="flex items-center space-x-2">
                      <Clock className="w-4 h-4 text-secondary-400" />
                      <span className="text-secondary-900">
                        {new Date(document.createdAt).toLocaleDateString('en-US', {
                          year: 'numeric',
                          month: 'long',
                          day: 'numeric',
                          hour: '2-digit',
                          minute: '2-digit'
                        })}
                      </span>
                    </div>
                  </div>
                  
                  {document.versions.length > 0 && (
                    <div>
                      <label className="text-sm font-medium text-secondary-700">Version</label>
                      <p className="text-secondary-900">v{document.versions.length + 1}</p>
                    </div>
                  )}
                </div>
              </div>

              {/* Actions */}
              <div>
                <h3 className="text-lg font-semibold text-secondary-900 mb-4">Actions</h3>
                <div className="space-y-2">
                  <button
                    onClick={handleDownload}
                    disabled={downloading}
                    className="btn-primary w-full"
                  >
                    {downloading ? (
                      <div className="flex items-center justify-center">
                        <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                        Downloading...
                      </div>
                    ) : (
                      <>
                        <Download className="w-4 h-4 mr-2" />
                        Download
                      </>
                    )}
                  </button>
                  
                  <button
                    onClick={handleOpenInNewTab}
                    className="btn-secondary w-full"
                  >
                    <ExternalLink className="w-4 h-4 mr-2" />
                    Open in New Tab
                  </button>
                  
                  {canExtract && (
                    <button
                      onClick={handleExtractData}
                      disabled={extracting}
                      className="btn-secondary w-full"
                    >
                      {extracting ? (
                        <div className="flex items-center justify-center">
                          <div className="w-4 h-4 border-2 border-secondary-400 border-t-transparent rounded-full animate-spin mr-2"></div>
                          Extracting...
                        </div>
                      ) : (
                        <>
                          <Lock className="w-4 h-4 mr-2" />
                          Extract Metadata
                        </>
                      )}
                    </button>
                  )}
                </div>
              </div>

              {/* Extracted Data */}
              {extractedData && (
                <div>
                  <h3 className="text-lg font-semibold text-secondary-900 mb-4">Extracted Metadata</h3>
                  <div className="p-3 bg-secondary-50 rounded-lg">
                    <pre className="text-xs text-secondary-700 whitespace-pre-wrap">
                      {JSON.stringify(extractedData, null, 2)}
                    </pre>
                  </div>
                </div>
              )}

              {/* Security Info */}
              <div className="p-4 bg-primary-50 border border-primary-200 rounded-lg">
                <div className="flex items-start">
                  <Shield className="w-5 h-5 text-primary-600 mr-3 mt-0.5" />
                  <div>
                    <h4 className="font-medium text-primary-900 mb-1">Security Features</h4>
                    <p className="text-sm text-primary-700">
                      This document is protected with {documentService.getSecurityLevelLabel(document.securityLevel).toLowerCase()} 
                      security steganographic embedding for authenticity verification.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
