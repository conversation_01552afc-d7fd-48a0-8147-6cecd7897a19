import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { 
  Upload, 
  Search, 
  Filter, 
  Download, 
  Eye, 
  Edit, 
  Trash2, 
  Shield, 
  Clock,
  User,
  FileText,
  Plus,
  Grid,
  List
} from 'lucide-react';
import { documentService, Document } from '../../services/documentService';
import { projectService, Project } from '../../services/projectService';
import { User as UserType } from '../../services/authService';
import DocumentCard from './DocumentCard';
import DocumentUpload from './DocumentUpload';
import DocumentViewer from './DocumentViewer';

interface DocumentsProps {
  user: UserType;
}

export default function Documents({ user }: DocumentsProps) {
  const { projectId } = useParams<{ projectId?: string }>();
  const [documents, setDocuments] = useState<Document[]>([]);
  const [project, setProject] = useState<Project | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState('all');
  const [filterSecurity, setFilterSecurity] = useState('all');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [showUploadModal, setShowUploadModal] = useState(false);
  const [selectedDocument, setSelectedDocument] = useState<Document | null>(null);
  const [showViewer, setShowViewer] = useState(false);

  useEffect(() => {
    if (projectId) {
      fetchProjectAndDocuments();
    }
  }, [projectId]);

  const fetchProjectAndDocuments = async () => {
    if (!projectId) return;

    try {
      setLoading(true);
      const [projectData, documentsData] = await Promise.all([
        projectService.getProjectById(projectId),
        documentService.getProjectDocuments(projectId)
      ]);
      
      setProject(projectData);
      setDocuments(documentsData);
    } catch (error: any) {
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleDocumentUpload = (newDocument: Document) => {
    setDocuments(prev => [newDocument, ...prev]);
    setShowUploadModal(false);
  };

  const handleDocumentDelete = async (documentId: string) => {
    if (!confirm('Are you sure you want to delete this document?')) return;

    try {
      await documentService.deleteDocument(documentId);
      setDocuments(prev => prev.filter(doc => doc._id !== documentId));
    } catch (error: any) {
      alert(error.message);
    }
  };

  const handleDocumentView = (document: Document) => {
    setSelectedDocument(document);
    setShowViewer(true);
  };

  const filteredDocuments = documents.filter(document => {
    const matchesSearch = document.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         document.fileName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         document.description?.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesType = filterType === 'all' || 
                       (filterType === 'pdf' && documentService.isPdfFile(document.fileType)) ||
                       (filterType === 'image' && documentService.isImageFile(document.fileType)) ||
                       (filterType === 'document' && documentService.isDocumentFile(document.fileType));

    const matchesSecurity = filterSecurity === 'all' || 
                           document.securityLevel.toString() === filterSecurity;

    return matchesSearch && matchesType && matchesSecurity;
  });

  const canUpload = project && (
    user._id === (typeof project.client === 'string' ? project.client : project.client._id) ||
    (project.assignedVendor && user._id === (typeof project.assignedVendor === 'string' ? project.assignedVendor : project.assignedVendor._id)) ||
    user.role === 'admin'
  );

  if (loading) {
    return (
      <div className="min-h-screen bg-secondary-50 flex items-center justify-center">
        <div className="text-center">
          <div className="w-12 h-12 border-4 border-primary-200 border-t-primary-600 rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-secondary-600">Loading documents...</p>
        </div>
      </div>
    );
  }

  if (!project) {
    return (
      <div className="min-h-screen bg-secondary-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-secondary-900 mb-4">Project Not Found</h1>
          <p className="text-secondary-600">The requested project could not be found.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-secondary-50">
      <div className="container-custom py-8">
        {/* Header */}
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-8">
          <div>
            <h1 className="text-3xl font-display font-bold text-secondary-900 mb-2">
              Project Documents
            </h1>
            <p className="text-secondary-600">
              {project.title} • {documents.length} document{documents.length !== 1 ? 's' : ''}
            </p>
          </div>
          
          {canUpload && (
            <button
              onClick={() => setShowUploadModal(true)}
              className="btn-primary mt-4 lg:mt-0"
            >
              <Upload className="w-5 h-5 mr-2" />
              Upload Document
            </button>
          )}
        </div>

        {/* Error Message */}
        {error && (
          <div className="mb-6 p-4 bg-error-50 border border-error-200 rounded-lg">
            <p className="text-error-700">{error}</p>
          </div>
        )}

        {/* Search and Filters */}
        <div className="card p-6 mb-8">
          <div className="flex flex-col lg:flex-row gap-4">
            {/* Search */}
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-secondary-400" />
                <input
                  type="text"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  placeholder="Search documents..."
                  className="input-field pl-10 w-full"
                />
              </div>
            </div>

            {/* Filters */}
            <div className="flex gap-4">
              <select
                value={filterType}
                onChange={(e) => setFilterType(e.target.value)}
                className="input-field"
              >
                <option value="all">All Types</option>
                <option value="pdf">PDF</option>
                <option value="image">Images</option>
                <option value="document">Documents</option>
              </select>

              <select
                value={filterSecurity}
                onChange={(e) => setFilterSecurity(e.target.value)}
                className="input-field"
              >
                <option value="all">All Security Levels</option>
                <option value="1">Basic</option>
                <option value="2">Standard</option>
                <option value="3">High</option>
                <option value="4">Maximum</option>
              </select>

              {/* View Mode Toggle */}
              <div className="flex border border-secondary-300 rounded-lg overflow-hidden">
                <button
                  onClick={() => setViewMode('grid')}
                  className={`p-2 ${viewMode === 'grid' ? 'bg-primary-600 text-white' : 'bg-white text-secondary-600 hover:bg-secondary-50'}`}
                >
                  <Grid className="w-5 h-5" />
                </button>
                <button
                  onClick={() => setViewMode('list')}
                  className={`p-2 ${viewMode === 'list' ? 'bg-primary-600 text-white' : 'bg-white text-secondary-600 hover:bg-secondary-50'}`}
                >
                  <List className="w-5 h-5" />
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Documents */}
        {filteredDocuments.length === 0 ? (
          <div className="text-center py-12">
            <div className="w-24 h-24 bg-secondary-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <FileText className="w-12 h-12 text-secondary-400" />
            </div>
            <h3 className="text-xl font-semibold text-secondary-900 mb-2">
              {documents.length === 0 ? 'No documents yet' : 'No documents match your filters'}
            </h3>
            <p className="text-secondary-600 mb-6">
              {documents.length === 0 
                ? 'Upload your first document to get started.'
                : 'Try adjusting your search or filter criteria.'
              }
            </p>
            {canUpload && documents.length === 0 && (
              <button
                onClick={() => setShowUploadModal(true)}
                className="btn-primary"
              >
                <Upload className="w-5 h-5 mr-2" />
                Upload First Document
              </button>
            )}
          </div>
        ) : (
          <div className={viewMode === 'grid' 
            ? 'grid md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6' 
            : 'space-y-4'
          }>
            {filteredDocuments.map((document) => (
              <DocumentCard
                key={document._id}
                document={document}
                user={user}
                project={project}
                viewMode={viewMode}
                onView={() => handleDocumentView(document)}
                onDelete={() => handleDocumentDelete(document._id)}
                onUpdate={fetchProjectAndDocuments}
              />
            ))}
          </div>
        )}

        {/* Upload Modal */}
        {showUploadModal && project && (
          <DocumentUpload
            project={project}
            user={user}
            onClose={() => setShowUploadModal(false)}
            onUpload={handleDocumentUpload}
          />
        )}

        {/* Document Viewer */}
        {showViewer && selectedDocument && (
          <DocumentViewer
            document={selectedDocument}
            user={user}
            onClose={() => {
              setShowViewer(false);
              setSelectedDocument(null);
            }}
            onUpdate={fetchProjectAndDocuments}
          />
        )}
      </div>
    </div>
  );
}
