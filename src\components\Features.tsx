import React from 'react';
import { Globe, TrendingUp, FileText, MessageCircle, Shield, Users, Zap, Target, Clock } from 'lucide-react';

const features = [
  {
    icon: Globe,
    title: "Global Project Marketplace",
    description: "Connect with clients and vendors worldwide. Access a diverse pool of talent and opportunities across all industries.",
    color: "primary"
  },
  {
    icon: TrendingUp,
    title: "Smart Bidding System",
    description: "AI-powered bid recommendations and real-time market insights to help you win more projects at optimal prices.",
    color: "success"
  },
  {
    icon: FileText,
    title: "Secure Document Management",
    description: "Advanced encryption and steganography protect your sensitive project documents and intellectual property.",
    color: "warning"
  },
  {
    icon: MessageCircle,
    title: "Real-time Collaboration",
    description: "Instant messaging, video calls, and project updates keep all stakeholders connected and informed.",
    color: "primary"
  },
  {
    icon: Shield,
    title: "Enterprise Security",
    description: "Bank-level security with multi-factor authentication, encrypted communications, and secure file storage.",
    color: "error"
  },
  {
    icon: Users,
    title: "Verified Network",
    description: "All users are verified with ratings and reviews. Build trust with a community of reliable professionals.",
    color: "success"
  },
  {
    icon: Zap,
    title: "Lightning Fast",
    description: "Optimized performance ensures quick project posting, bid submission, and seamless user experience.",
    color: "warning"
  },
  {
    icon: Target,
    title: "Precision Matching",
    description: "Advanced algorithms match projects with the most suitable vendors based on skills, experience, and budget.",
    color: "primary"
  },
  {
    icon: Clock,
    title: "24/7 Support",
    description: "Round-the-clock customer support and automated project monitoring ensure smooth operations.",
    color: "success"
  }
];

const getColorClasses = (color: string) => {
  const colors = {
    primary: {
      bg: 'bg-primary-100',
      text: 'text-primary-600',
      border: 'border-primary-200',
      hover: 'hover:border-primary-300'
    },
    success: {
      bg: 'bg-success-100',
      text: 'text-success-600',
      border: 'border-success-200',
      hover: 'hover:border-success-300'
    },
    warning: {
      bg: 'bg-warning-100',
      text: 'text-warning-600',
      border: 'border-warning-200',
      hover: 'hover:border-warning-300'
    },
    error: {
      bg: 'bg-error-100',
      text: 'text-error-600',
      border: 'border-error-200',
      hover: 'hover:border-error-300'
    }
  };
  return colors[color as keyof typeof colors] || colors.primary;
};

export default function Features() {
  return (
    <section className="section-padding bg-white relative">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%2322c55e' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
        }}></div>
      </div>

      <div className="container-custom relative z-10">
        {/* Header */}
        <div className="text-center mb-16 animate-fade-in-up">
          <span className="inline-block px-4 py-2 bg-primary-100 text-primary-700 rounded-full text-sm font-semibold mb-4">
            ✨ Platform Features
          </span>
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-display font-bold text-secondary-900 mb-6">
            Everything You Need to <span className="gradient-text">Succeed</span>
          </h2>
          <p className="text-lg text-secondary-600 max-w-3xl mx-auto">
            Our comprehensive platform provides all the tools and features you need to manage projects,
            connect with global talent, and grow your business efficiently.
          </p>
        </div>

        {/* Features Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => {
            const Icon = feature.icon;
            const colorClasses = getColorClasses(feature.color);

            return (
              <div
                key={index}
                className={`card-hover p-8 border-2 ${colorClasses.border} ${colorClasses.hover} group animate-fade-in-up`}
                style={{ animationDelay: `${index * 100}ms` }}
              >
                <div className={`w-16 h-16 ${colorClasses.bg} rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300`}>
                  <Icon className={`w-8 h-8 ${colorClasses.text}`} />
                </div>

                <h3 className="text-xl font-display font-semibold text-secondary-900 mb-4 group-hover:text-primary-600 transition-colors duration-300">
                  {feature.title}
                </h3>

                <p className="text-secondary-600 leading-relaxed">
                  {feature.description}
                </p>

                {/* Hover Effect Arrow */}
                <div className="mt-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <div className={`w-8 h-0.5 ${colorClasses.bg} rounded-full`}></div>
                </div>
              </div>
            );
          })}
        </div>

        {/* Bottom CTA */}
        <div className="text-center mt-16 animate-fade-in-up animation-delay-800">
          <div className="inline-flex items-center space-x-4">
            <button className="btn-primary">
              Explore All Features
            </button>
            <button className="btn-outline">
              Schedule Demo
            </button>
          </div>
        </div>
      </div>
    </section>
  );
}