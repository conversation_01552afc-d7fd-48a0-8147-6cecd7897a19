import React from 'react';
import { <PERSON><PERSON>ight, Globe, Users, TrendingUp, Shield } from 'lucide-react';

interface HeroProps {
  onGetStarted?: () => void;
}

export default function Hero({ onGetStarted }: HeroProps) {
  return (
    <section className="relative min-h-screen flex items-center gradient-bg overflow-hidden">
      {/* Background decorative elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-primary-100 rounded-full opacity-50 animate-float"></div>
        <div className="absolute -bottom-40 -left-40 w-96 h-96 bg-primary-50 rounded-full opacity-30 animation-delay-400 animate-float"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-primary-200 rounded-full opacity-20 animation-delay-800 animate-float"></div>
      </div>

      <div className="container-custom relative z-10">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Left Column - Content */}
          <div className="text-center lg:text-left">
            <div className="animate-fade-in-up">
              <span className="inline-block px-4 py-2 bg-primary-100 text-primary-700 rounded-full text-sm font-semibold mb-6">
                🚀 Connecting Global Markets
              </span>
            </div>

            <h1 className="text-4xl sm:text-5xl lg:text-6xl font-display font-bold text-secondary-900 mb-6 animate-fade-in-up animation-delay-200">
              Empowering <span className="gradient-text">Global Trade</span> for Modern Businesses
            </h1>

            <p className="text-lg sm:text-xl text-secondary-600 mb-8 max-w-2xl animate-fade-in-up animation-delay-400">
              Connect clients with top vendors worldwide. Streamline project management, secure bidding, and real-time collaboration in one powerful platform.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start animate-fade-in-up animation-delay-600">
              <button
                onClick={onGetStarted}
                className="btn-primary group relative overflow-hidden"
              >
                <span className="relative z-10 flex items-center">
                  Get Started Today
                  <ArrowRight className="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform duration-200" />
                </span>
                <div className="absolute inset-0 bg-gradient-to-r from-primary-700 to-primary-600 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"></div>
              </button>
              <button className="btn-secondary group relative overflow-hidden">
                <span className="relative z-10 flex items-center">
                  Watch Demo
                  <div className="ml-2 w-2 h-2 bg-primary-600 rounded-full group-hover:animate-pulse"></div>
                </span>
                <div className="absolute inset-0 bg-gradient-to-r from-primary-50 to-primary-100 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"></div>
              </button>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-3 gap-8 mt-12 animate-fade-in-up animation-delay-800">
              <div className="text-center lg:text-left">
                <div className="text-2xl lg:text-3xl font-bold text-primary-600">10K+</div>
                <div className="text-sm text-secondary-500">Active Projects</div>
              </div>
              <div className="text-center lg:text-left">
                <div className="text-2xl lg:text-3xl font-bold text-primary-600">50+</div>
                <div className="text-sm text-secondary-500">Countries</div>
              </div>
              <div className="text-center lg:text-left">
                <div className="text-2xl lg:text-3xl font-bold text-primary-600">99%</div>
                <div className="text-sm text-secondary-500">Success Rate</div>
              </div>
            </div>
          </div>

          {/* Right Column - Visual Elements */}
          <div className="relative animate-fade-in-right">
            <div className="relative">
              {/* Main Card */}
              <div className="card p-8 transform rotate-3 hover:rotate-0 transition-transform duration-500">
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center space-x-3">
                    <div className="w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center">
                      <Globe className="w-6 h-6 text-primary-600" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-secondary-900">Global Project</h3>
                      <p className="text-sm text-secondary-500">Web Development</p>
                    </div>
                  </div>
                  <span className="px-3 py-1 bg-success-100 text-success-700 rounded-full text-xs font-semibold">
                    Active
                  </span>
                </div>

                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-secondary-600">Budget</span>
                    <span className="font-semibold text-secondary-900">$15,000</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-secondary-600">Bids</span>
                    <span className="font-semibold text-primary-600">12 received</span>
                  </div>
                  <div className="w-full bg-secondary-100 rounded-full h-2">
                    <div className="bg-primary-500 h-2 rounded-full w-3/4 animate-pulse"></div>
                  </div>
                </div>
              </div>

              {/* Floating Cards */}
              <div className="absolute -top-4 -right-4 card p-4 w-48 animate-bounce-in animation-delay-400">
                <div className="flex items-center space-x-2">
                  <Users className="w-5 h-5 text-primary-600" />
                  <span className="text-sm font-semibold">5 Vendors Bidding</span>
                </div>
              </div>

              <div className="absolute -bottom-4 -left-4 card p-4 w-48 animate-bounce-in animation-delay-600">
                <div className="flex items-center space-x-2">
                  <TrendingUp className="w-5 h-5 text-success-600" />
                  <span className="text-sm font-semibold">98% Success Rate</span>
                </div>
              </div>

              <div className="absolute top-1/2 -right-8 card p-3 animate-bounce-in animation-delay-800">
                <Shield className="w-6 h-6 text-primary-600" />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom wave */}
      <div className="absolute bottom-0 left-0 right-0">
        <svg viewBox="0 0 1440 120" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path
            d="M0 120L60 110C120 100 240 80 360 70C480 60 600 60 720 65C840 70 960 80 1080 85C1200 90 1320 90 1380 90L1440 90V120H1380C1320 120 1200 120 1080 120C960 120 840 120 720 120C600 120 480 120 360 120C240 120 120 120 60 120H0Z"
            fill="white"
          />
        </svg>
      </div>
    </section>
  );
}