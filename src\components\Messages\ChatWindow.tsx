import React, { useState, useEffect, useRef } from 'react';
import { 
  Send, 
  Paperclip, 
  Smile, 
  Phone, 
  Video, 
  MoreVertical,
  User,
  Download,
  X
} from 'lucide-react';
import { Conversation, Message, messageService } from '../../services/messageService';
import { User as UserType } from '../../services/authService';
import MessageBubble from './MessageBubble';

interface ChatWindowProps {
  conversation: Conversation;
  currentUser: UserType;
  onConversationUpdate: () => void;
}

export default function ChatWindow({ conversation, currentUser, onConversationUpdate }: ChatWindowProps) {
  const [messages, setMessages] = useState<Message[]>([]);
  const [loading, setLoading] = useState(true);
  const [sending, setSending] = useState(false);
  const [messageText, setMessageText] = useState('');
  const [attachments, setAttachments] = useState<File[]>([]);
  const [isTyping, setIsTyping] = useState(false);
  const [typingUsers, setTypingUsers] = useState<string[]>([]);
  const [page, setPage] = useState(1);
  const [hasMoreMessages, setHasMoreMessages] = useState(true);

  const messagesEndRef = useRef<HTMLDivElement>(null);
  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    fetchMessages();
    
    // Set up event listeners
    messageService.on('new_message', handleNewMessage);
    messageService.on('user_typing', handleUserTyping);
    messageService.on('user_stopped_typing', handleUserStoppedTyping);

    return () => {
      messageService.off('new_message', handleNewMessage);
      messageService.off('user_typing', handleUserTyping);
      messageService.off('user_stopped_typing', handleUserStoppedTyping);
    };
  }, [conversation._id]);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const fetchMessages = async (pageNum: number = 1) => {
    try {
      setLoading(pageNum === 1);
      const data = await messageService.getMessages(conversation._id, pageNum);
      
      if (pageNum === 1) {
        setMessages(data.messages.reverse());
      } else {
        setMessages(prev => [...data.messages.reverse(), ...prev]);
      }
      
      setHasMoreMessages(data.currentPage < data.totalPages);
      setPage(pageNum);
    } catch (error: any) {
      console.error('Failed to fetch messages:', error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleNewMessage = (data: { message: Message; conversation: string }) => {
    if (data.conversation === conversation._id) {
      setMessages(prev => [...prev, data.message]);
      
      // Mark message as read if it's not from the current user
      if (data.message.sender._id !== currentUser._id) {
        messageService.markMessageAsRead(data.message._id);
      }
    }
  };

  const handleUserTyping = (data: { userId: string; conversationId: string; userName: string }) => {
    if (data.conversationId === conversation._id && data.userId !== currentUser._id) {
      setTypingUsers(prev => [...prev.filter(id => id !== data.userId), data.userId]);
    }
  };

  const handleUserStoppedTyping = (data: { userId: string; conversationId: string }) => {
    if (data.conversationId === conversation._id) {
      setTypingUsers(prev => prev.filter(id => id !== data.userId));
    }
  };

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!messageText.trim() && attachments.length === 0) return;

    try {
      setSending(true);
      
      await messageService.sendMessage(conversation._id, {
        content: messageText.trim() || undefined,
        attachments: attachments.length > 0 ? attachments : undefined
      });

      setMessageText('');
      setAttachments([]);
      onConversationUpdate();
    } catch (error: any) {
      console.error('Failed to send message:', error.message);
    } finally {
      setSending(false);
    }
  };

  const handleTyping = () => {
    if (!isTyping) {
      setIsTyping(true);
      messageService.startTyping(conversation._id);
    }

    // Clear existing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    // Set new timeout
    typingTimeoutRef.current = setTimeout(() => {
      setIsTyping(false);
      messageService.stopTyping(conversation._id);
    }, 1000);
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    setAttachments(prev => [...prev, ...files]);
  };

  const removeAttachment = (index: number) => {
    setAttachments(prev => prev.filter((_, i) => i !== index));
  };

  const loadMoreMessages = () => {
    if (hasMoreMessages && !loading) {
      fetchMessages(page + 1);
    }
  };

  const conversationTitle = messageService.getConversationTitle(conversation, currentUser._id);
  const conversationAvatar = messageService.getConversationAvatar(conversation, currentUser._id);

  if (loading && messages.length === 0) {
    return (
      <div className="card h-full flex items-center justify-center">
        <div className="text-center">
          <div className="w-8 h-8 border-4 border-primary-200 border-t-primary-600 rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-secondary-600">Loading messages...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="card h-full flex flex-col">
      {/* Header */}
      <div className="p-4 border-b border-secondary-200 flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center overflow-hidden">
            {conversationAvatar ? (
              <img 
                src={conversationAvatar} 
                alt={conversationTitle}
                className="w-10 h-10 object-cover"
              />
            ) : (
              <User className="w-5 h-5 text-primary-600" />
            )}
          </div>
          <div>
            <h3 className="font-semibold text-secondary-900">{conversationTitle}</h3>
            {conversation.project && (
              <p className="text-sm text-secondary-600">Project: {conversation.project.title}</p>
            )}
            {typingUsers.length > 0 && (
              <p className="text-sm text-primary-600">
                {typingUsers.length === 1 ? 'Someone is typing...' : 'Multiple people are typing...'}
              </p>
            )}
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <button className="p-2 text-secondary-400 hover:text-secondary-600 transition-colors duration-200">
            <Phone className="w-5 h-5" />
          </button>
          <button className="p-2 text-secondary-400 hover:text-secondary-600 transition-colors duration-200">
            <Video className="w-5 h-5" />
          </button>
          <button className="p-2 text-secondary-400 hover:text-secondary-600 transition-colors duration-200">
            <MoreVertical className="w-5 h-5" />
          </button>
        </div>
      </div>

      {/* Messages */}
      <div 
        ref={messagesContainerRef}
        className="flex-1 overflow-y-auto p-4 space-y-4"
      >
        {hasMoreMessages && (
          <div className="text-center">
            <button
              onClick={loadMoreMessages}
              disabled={loading}
              className="text-primary-600 hover:text-primary-700 text-sm font-medium"
            >
              {loading ? 'Loading...' : 'Load more messages'}
            </button>
          </div>
        )}

        {messages.map((message, index) => (
          <MessageBubble
            key={message._id}
            message={message}
            isFromCurrentUser={messageService.isMessageFromCurrentUser(message, currentUser._id)}
            showAvatar={
              index === 0 || 
              messages[index - 1].sender._id !== message.sender._id ||
              new Date(message.createdAt).getTime() - new Date(messages[index - 1].createdAt).getTime() > 300000 // 5 minutes
            }
            showTimestamp={
              index === messages.length - 1 ||
              messages[index + 1].sender._id !== message.sender._id ||
              new Date(messages[index + 1].createdAt).getTime() - new Date(message.createdAt).getTime() > 300000 // 5 minutes
            }
          />
        ))}
        
        <div ref={messagesEndRef} />
      </div>

      {/* Attachments Preview */}
      {attachments.length > 0 && (
        <div className="px-4 py-2 border-t border-secondary-200 bg-secondary-50">
          <div className="flex flex-wrap gap-2">
            {attachments.map((file, index) => (
              <div key={index} className="flex items-center space-x-2 bg-white rounded-lg p-2 border">
                <span className="text-sm">{messageService.getFileIcon(file.type)}</span>
                <span className="text-sm text-secondary-700 truncate max-w-32">{file.name}</span>
                <button
                  onClick={() => removeAttachment(index)}
                  className="text-error-600 hover:text-error-700"
                >
                  <X className="w-4 h-4" />
                </button>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Message Input */}
      <div className="p-4 border-t border-secondary-200">
        <form onSubmit={handleSendMessage} className="flex items-end space-x-2">
          <div className="flex-1">
            <div className="flex items-center space-x-2 mb-2">
              <button
                type="button"
                onClick={() => fileInputRef.current?.click()}
                className="p-2 text-secondary-400 hover:text-secondary-600 transition-colors duration-200"
              >
                <Paperclip className="w-5 h-5" />
              </button>
              <button
                type="button"
                className="p-2 text-secondary-400 hover:text-secondary-600 transition-colors duration-200"
              >
                <Smile className="w-5 h-5" />
              </button>
            </div>
            
            <textarea
              value={messageText}
              onChange={(e) => {
                setMessageText(e.target.value);
                handleTyping();
              }}
              onKeyPress={(e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                  e.preventDefault();
                  handleSendMessage(e);
                }
              }}
              placeholder="Type a message..."
              rows={1}
              className="input-field resize-none w-full"
              disabled={sending}
            />
          </div>
          
          <button
            type="submit"
            disabled={sending || (!messageText.trim() && attachments.length === 0)}
            className="btn-primary p-3 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {sending ? (
              <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
            ) : (
              <Send className="w-5 h-5" />
            )}
          </button>
        </form>

        <input
          ref={fileInputRef}
          type="file"
          multiple
          onChange={handleFileSelect}
          className="hidden"
          accept="image/*,video/*,audio/*,.pdf,.doc,.docx,.txt,.zip,.rar"
        />
      </div>
    </div>
  );
}
