import React from 'react';
import { User, MessageSquare, Users, Briefcase } from 'lucide-react';
import { Conversation, messageService } from '../../services/messageService';
import { User as UserType } from '../../services/authService';

interface ConversationsListProps {
  conversations: Conversation[];
  selectedConversation: Conversation | null;
  currentUser: UserType;
  onConversationSelect: (conversation: Conversation) => void;
}

export default function ConversationsList({
  conversations,
  selectedConversation,
  currentUser,
  onConversationSelect
}: ConversationsListProps) {
  if (conversations.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center h-full p-8 text-center">
        <div className="w-16 h-16 bg-secondary-100 rounded-full flex items-center justify-center mb-4">
          <MessageSquare className="w-8 h-8 text-secondary-400" />
        </div>
        <h3 className="text-lg font-semibold text-secondary-900 mb-2">No conversations yet</h3>
        <p className="text-secondary-600 text-sm">
          Start a new conversation to begin messaging
        </p>
      </div>
    );
  }

  return (
    <div className="divide-y divide-secondary-100">
      {conversations.map((conversation) => (
        <ConversationItem
          key={conversation._id}
          conversation={conversation}
          isSelected={selectedConversation?._id === conversation._id}
          currentUser={currentUser}
          onClick={() => onConversationSelect(conversation)}
        />
      ))}
    </div>
  );
}

interface ConversationItemProps {
  conversation: Conversation;
  isSelected: boolean;
  currentUser: UserType;
  onClick: () => void;
}

function ConversationItem({ conversation, isSelected, currentUser, onClick }: ConversationItemProps) {
  const title = messageService.getConversationTitle(conversation, currentUser._id);
  const avatar = messageService.getConversationAvatar(conversation, currentUser._id);
  const unreadCount = messageService.getUnreadCount(conversation, currentUser._id);
  const lastMessageTime = conversation.lastMessage 
    ? messageService.formatMessageTime(conversation.lastMessage.createdAt)
    : messageService.formatMessageTime(conversation.createdAt);

  const getConversationIcon = () => {
    switch (conversation.type) {
      case 'direct':
        return User;
      case 'group':
        return Users;
      case 'project':
        return Briefcase;
      default:
        return MessageSquare;
    }
  };

  const Icon = getConversationIcon();

  const getLastMessagePreview = () => {
    if (!conversation.lastMessage) {
      return 'No messages yet';
    }

    const message = conversation.lastMessage;
    const isFromCurrentUser = messageService.isMessageFromCurrentUser(message, currentUser._id);
    const prefix = isFromCurrentUser ? 'You: ' : '';

    if (message.content) {
      return `${prefix}${message.content}`;
    } else if (message.attachments.length > 0) {
      const attachment = message.attachments[0];
      const icon = messageService.getFileIcon(attachment.fileType);
      return `${prefix}${icon} ${attachment.fileName}`;
    }

    return `${prefix}Sent a message`;
  };

  return (
    <div
      onClick={onClick}
      className={`p-4 cursor-pointer transition-colors duration-200 hover:bg-secondary-50 ${
        isSelected ? 'bg-primary-50 border-r-2 border-primary-500' : ''
      }`}
    >
      <div className="flex items-start space-x-3">
        {/* Avatar */}
        <div className="relative flex-shrink-0">
          <div className="w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center overflow-hidden">
            {avatar ? (
              <img 
                src={avatar} 
                alt={title}
                className="w-12 h-12 object-cover"
              />
            ) : (
              <Icon className="w-6 h-6 text-primary-600" />
            )}
          </div>
          
          {/* Online status indicator (placeholder) */}
          <div className="absolute bottom-0 right-0 w-3 h-3 bg-success-500 border-2 border-white rounded-full"></div>
        </div>

        {/* Content */}
        <div className="flex-1 min-w-0">
          <div className="flex items-center justify-between mb-1">
            <h3 className={`font-medium truncate ${
              isSelected ? 'text-primary-700' : 'text-secondary-900'
            }`}>
              {title}
            </h3>
            <span className="text-xs text-secondary-500 flex-shrink-0 ml-2">
              {lastMessageTime}
            </span>
          </div>

          {/* Project info */}
          {conversation.project && (
            <div className="flex items-center mb-1">
              <Briefcase className="w-3 h-3 text-secondary-400 mr-1" />
              <span className="text-xs text-secondary-500 truncate">
                {conversation.project.title}
              </span>
            </div>
          )}

          {/* Last message preview */}
          <div className="flex items-center justify-between">
            <p className={`text-sm truncate ${
              unreadCount > 0 ? 'font-medium text-secondary-900' : 'text-secondary-600'
            }`}>
              {getLastMessagePreview()}
            </p>
            
            {/* Unread count badge */}
            {unreadCount > 0 && (
              <span className="bg-primary-600 text-white text-xs font-medium px-2 py-1 rounded-full ml-2 flex-shrink-0">
                {unreadCount > 99 ? '99+' : unreadCount}
              </span>
            )}
          </div>

          {/* Participants preview for group conversations */}
          {conversation.type === 'group' && conversation.participants.length > 2 && (
            <div className="flex items-center mt-2">
              <Users className="w-3 h-3 text-secondary-400 mr-1" />
              <span className="text-xs text-secondary-500">
                {conversation.participants.length} members
              </span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
