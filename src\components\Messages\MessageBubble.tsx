import React, { useState } from 'react';
import {
  User,
  Download,
  Eye,
  MoreVertical,
  Trash2,
  <PERSON><PERSON>,
  Check,
  Check<PERSON>he<PERSON>,
  X
} from 'lucide-react';
import { Message, messageService } from '../../services/messageService';

interface MessageBubbleProps {
  message: Message;
  isFromCurrentUser: boolean;
  showAvatar: boolean;
  showTimestamp: boolean;
}

export default function MessageBubble({ 
  message, 
  isFromCurrentUser, 
  showAvatar, 
  showTimestamp 
}: MessageBubbleProps) {
  const [showActions, setShowActions] = useState(false);
  const [imagePreview, setImagePreview] = useState<string | null>(null);

  const handleCopyMessage = () => {
    if (message.content) {
      navigator.clipboard.writeText(message.content);
    }
  };

  const handleDeleteMessage = async () => {
    if (confirm('Are you sure you want to delete this message?')) {
      try {
        await messageService.deleteMessage(message._id);
      } catch (error: any) {
        console.error('Failed to delete message:', error.message);
      }
    }
  };

  const handleDownloadAttachment = (attachment: any) => {
    const link = document.createElement('a');
    link.href = attachment.url;
    link.download = attachment.fileName;
    link.target = '_blank';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const isImageFile = (fileType: string) => {
    return fileType.startsWith('image/');
  };

  const isVideoFile = (fileType: string) => {
    return fileType.startsWith('video/');
  };

  const isAudioFile = (fileType: string) => {
    return fileType.startsWith('audio/');
  };

  return (
    <div className={`flex ${isFromCurrentUser ? 'justify-end' : 'justify-start'} group`}>
      <div className={`flex max-w-xs lg:max-w-md ${isFromCurrentUser ? 'flex-row-reverse' : 'flex-row'} items-end space-x-2`}>
        {/* Avatar */}
        {showAvatar && !isFromCurrentUser && (
          <div className="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center overflow-hidden flex-shrink-0">
            {message.sender.profileImage ? (
              <img 
                src={message.sender.profileImage} 
                alt={message.sender.name}
                className="w-8 h-8 object-cover"
              />
            ) : (
              <User className="w-4 h-4 text-primary-600" />
            )}
          </div>
        )}

        {/* Message Content */}
        <div className={`relative ${showAvatar && !isFromCurrentUser ? 'ml-2' : ''} ${showAvatar && isFromCurrentUser ? 'mr-2' : ''}`}>
          {/* Sender Name (for group conversations) */}
          {!isFromCurrentUser && showAvatar && (
            <p className="text-xs text-secondary-600 mb-1 px-3">
              {message.sender.name}
            </p>
          )}

          {/* Message Bubble */}
          <div
            className={`relative px-4 py-2 rounded-2xl ${
              isFromCurrentUser
                ? 'bg-primary-600 text-white'
                : 'bg-white border border-secondary-200 text-secondary-900'
            } ${showAvatar ? (isFromCurrentUser ? 'rounded-br-md' : 'rounded-bl-md') : ''}`}
            onMouseEnter={() => setShowActions(true)}
            onMouseLeave={() => setShowActions(false)}
          >
            {/* Text Content */}
            {message.content && (
              <p className="text-sm whitespace-pre-wrap break-words">
                {message.content}
              </p>
            )}

            {/* Attachments */}
            {message.attachments.length > 0 && (
              <div className={`space-y-2 ${message.content ? 'mt-2' : ''}`}>
                {message.attachments.map((attachment, index) => (
                  <div key={index} className="attachment">
                    {isImageFile(attachment.fileType) ? (
                      <div className="relative">
                        <img
                          src={attachment.url}
                          alt={attachment.fileName}
                          className="max-w-full h-auto rounded-lg cursor-pointer hover:opacity-90 transition-opacity duration-200"
                          onClick={() => setImagePreview(attachment.url)}
                          style={{ maxHeight: '200px' }}
                        />
                        <button
                          onClick={() => handleDownloadAttachment(attachment)}
                          className="absolute top-2 right-2 p-1 bg-black bg-opacity-50 text-white rounded-full hover:bg-opacity-70 transition-all duration-200"
                        >
                          <Download className="w-4 h-4" />
                        </button>
                      </div>
                    ) : isVideoFile(attachment.fileType) ? (
                      <div className="relative">
                        <video
                          controls
                          className="max-w-full h-auto rounded-lg"
                          style={{ maxHeight: '200px' }}
                        >
                          <source src={attachment.url} type={attachment.fileType} />
                          Your browser does not support the video tag.
                        </video>
                      </div>
                    ) : isAudioFile(attachment.fileType) ? (
                      <div className="bg-secondary-50 rounded-lg p-3">
                        <audio controls className="w-full">
                          <source src={attachment.url} type={attachment.fileType} />
                          Your browser does not support the audio tag.
                        </audio>
                        <p className="text-xs text-secondary-600 mt-1">{attachment.fileName}</p>
                      </div>
                    ) : (
                      <div className={`flex items-center space-x-3 p-3 rounded-lg border ${
                        isFromCurrentUser ? 'bg-primary-500 border-primary-400' : 'bg-secondary-50 border-secondary-200'
                      }`}>
                        <div className="text-2xl">
                          {messageService.getFileIcon(attachment.fileType)}
                        </div>
                        <div className="flex-1 min-w-0">
                          <p className={`text-sm font-medium truncate ${
                            isFromCurrentUser ? 'text-white' : 'text-secondary-900'
                          }`}>
                            {attachment.fileName}
                          </p>
                          <p className={`text-xs ${
                            isFromCurrentUser ? 'text-primary-100' : 'text-secondary-500'
                          }`}>
                            {messageService.formatFileSize(attachment.fileSize)}
                          </p>
                        </div>
                        <button
                          onClick={() => handleDownloadAttachment(attachment)}
                          className={`p-2 rounded-full transition-colors duration-200 ${
                            isFromCurrentUser 
                              ? 'hover:bg-primary-400 text-white' 
                              : 'hover:bg-secondary-200 text-secondary-600'
                          }`}
                        >
                          <Download className="w-4 h-4" />
                        </button>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}

            {/* Message Actions */}
            {showActions && (
              <div className={`absolute top-0 ${
                isFromCurrentUser ? 'left-0 -translate-x-full' : 'right-0 translate-x-full'
              } flex items-center space-x-1 bg-white border border-secondary-200 rounded-lg shadow-lg p-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200`}>
                {message.content && (
                  <button
                    onClick={handleCopyMessage}
                    className="p-1 text-secondary-400 hover:text-secondary-600 transition-colors duration-200"
                    title="Copy message"
                  >
                    <Copy className="w-4 h-4" />
                  </button>
                )}
                {isFromCurrentUser && (
                  <button
                    onClick={handleDeleteMessage}
                    className="p-1 text-secondary-400 hover:text-error-600 transition-colors duration-200"
                    title="Delete message"
                  >
                    <Trash2 className="w-4 h-4" />
                  </button>
                )}
                <button
                  className="p-1 text-secondary-400 hover:text-secondary-600 transition-colors duration-200"
                  title="More options"
                >
                  <MoreVertical className="w-4 h-4" />
                </button>
              </div>
            )}
          </div>

          {/* Timestamp and Read Status */}
          {showTimestamp && (
            <div className={`flex items-center space-x-1 mt-1 ${
              isFromCurrentUser ? 'justify-end' : 'justify-start'
            }`}>
              <span className="text-xs text-secondary-500">
                {messageService.formatMessageTime(message.createdAt)}
              </span>
              {isFromCurrentUser && (
                <div className="text-secondary-500">
                  {message.readBy.length > 1 ? (
                    <CheckCheck className="w-3 h-3 text-primary-600" />
                  ) : (
                    <Check className="w-3 h-3" />
                  )}
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Image Preview Modal */}
      {imagePreview && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4"
          onClick={() => setImagePreview(null)}
        >
          <div className="relative max-w-4xl max-h-full">
            <img
              src={imagePreview}
              alt="Preview"
              className="max-w-full max-h-full object-contain"
            />
            <button
              onClick={() => setImagePreview(null)}
              className="absolute top-4 right-4 p-2 bg-black bg-opacity-50 text-white rounded-full hover:bg-opacity-70 transition-all duration-200"
            >
              <X className="w-6 h-6" />
            </button>
          </div>
        </div>
      )}
    </div>
  );
}


