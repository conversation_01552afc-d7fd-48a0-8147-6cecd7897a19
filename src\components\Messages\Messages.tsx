import React, { useState, useEffect } from 'react';
import { 
  MessageSquare, 
  Search, 
  Plus, 
  Users, 
  Phone, 
  Video,
  MoreVertical,
  ArrowLeft
} from 'lucide-react';
import { messageService, Conversation, Message } from '../../services/messageService';
import { User } from '../../services/authService';
import ConversationsList from './ConversationsList';
import ChatWindow from './ChatWindow';
import NewConversationModal from './NewConversationModal';

interface MessagesProps {
  user: User;
}

export default function Messages({ user }: MessagesProps) {
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [selectedConversation, setSelectedConversation] = useState<Conversation | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [showNewConversationModal, setShowNewConversationModal] = useState(false);
  const [totalUnreadCount, setTotalUnreadCount] = useState(0);
  const [isMobileView, setIsMobileView] = useState(false);

  useEffect(() => {
    // Initialize socket connection
    messageService.initializeSocket(user._id);
    
    // Fetch conversations
    fetchConversations();

    // Set up event listeners
    messageService.on('new_message', handleNewMessage);
    messageService.on('new_conversation', handleNewConversation);
    messageService.on('message_read', handleMessageRead);

    // Check for mobile view
    const checkMobileView = () => {
      setIsMobileView(window.innerWidth < 1024);
    };
    
    checkMobileView();
    window.addEventListener('resize', checkMobileView);

    return () => {
      messageService.off('new_message', handleNewMessage);
      messageService.off('new_conversation', handleNewConversation);
      messageService.off('message_read', handleMessageRead);
      window.removeEventListener('resize', checkMobileView);
      messageService.disconnect();
    };
  }, [user._id]);

  const fetchConversations = async () => {
    try {
      setLoading(true);
      const data = await messageService.getConversations();
      setConversations(data.conversations);
      setTotalUnreadCount(data.totalUnreadCount);
    } catch (error: any) {
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleNewMessage = (data: { message: Message; conversation: string }) => {
    setConversations(prev => 
      prev.map(conv => {
        if (conv._id === data.conversation) {
          return {
            ...conv,
            lastMessage: data.message,
            updatedAt: data.message.createdAt
          };
        }
        return conv;
      })
    );
    
    // Update total unread count
    setTotalUnreadCount(prev => prev + 1);
  };

  const handleNewConversation = (conversation: Conversation) => {
    setConversations(prev => [conversation, ...prev]);
  };

  const handleMessageRead = (data: { messageId: string; userId: string; conversationId: string }) => {
    // Update conversation unread count if the current user read the message
    if (data.userId === user._id) {
      setConversations(prev =>
        prev.map(conv => {
          if (conv._id === data.conversationId) {
            const newUnreadCount = new Map(conv.unreadCount);
            newUnreadCount.set(user._id, 0);
            return { ...conv, unreadCount: newUnreadCount };
          }
          return conv;
        })
      );
    }
  };

  const handleConversationSelect = (conversation: Conversation) => {
    setSelectedConversation(conversation);
    
    // Join the conversation room
    messageService.joinConversation(conversation._id);
    
    // Mark messages as read (this will be handled by the ChatWindow component)
  };

  const handleBackToList = () => {
    if (selectedConversation) {
      messageService.leaveConversation(selectedConversation._id);
    }
    setSelectedConversation(null);
  };

  const filteredConversations = conversations.filter(conversation => {
    const title = messageService.getConversationTitle(conversation, user._id).toLowerCase();
    return title.includes(searchTerm.toLowerCase());
  });

  if (loading) {
    return (
      <div className="min-h-screen bg-secondary-50 flex items-center justify-center">
        <div className="text-center">
          <div className="w-12 h-12 border-4 border-primary-200 border-t-primary-600 rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-secondary-600">Loading messages...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-secondary-50">
      <div className="container-custom py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center">
            {isMobileView && selectedConversation && (
              <button
                onClick={handleBackToList}
                className="btn-secondary mr-4 lg:hidden"
              >
                <ArrowLeft className="w-5 h-5" />
              </button>
            )}
            <div>
              <h1 className="text-3xl font-display font-bold text-secondary-900">Messages</h1>
              <p className="text-secondary-600">
                {totalUnreadCount > 0 && (
                  <span className="text-primary-600 font-medium">
                    {totalUnreadCount} unread message{totalUnreadCount !== 1 ? 's' : ''}
                  </span>
                )}
              </p>
            </div>
          </div>
          
          <button
            onClick={() => setShowNewConversationModal(true)}
            className="btn-primary"
          >
            <Plus className="w-5 h-5 mr-2" />
            New Message
          </button>
        </div>

        {/* Error Message */}
        {error && (
          <div className="mb-6 p-4 bg-error-50 border border-error-200 rounded-lg">
            <p className="text-error-700">{error}</p>
          </div>
        )}

        {/* Main Content */}
        <div className="grid lg:grid-cols-3 gap-6 h-[calc(100vh-200px)]">
          {/* Conversations List */}
          <div className={`lg:col-span-1 ${isMobileView && selectedConversation ? 'hidden' : ''}`}>
            <div className="card h-full flex flex-col">
              {/* Search */}
              <div className="p-4 border-b border-secondary-200">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-secondary-400" />
                  <input
                    type="text"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    placeholder="Search conversations..."
                    className="input-field pl-10 w-full"
                  />
                </div>
              </div>

              {/* Conversations */}
              <div className="flex-1 overflow-y-auto">
                <ConversationsList
                  conversations={filteredConversations}
                  selectedConversation={selectedConversation}
                  currentUser={user}
                  onConversationSelect={handleConversationSelect}
                />
              </div>
            </div>
          </div>

          {/* Chat Window */}
          <div className={`lg:col-span-2 ${isMobileView && !selectedConversation ? 'hidden' : ''}`}>
            {selectedConversation ? (
              <ChatWindow
                conversation={selectedConversation}
                currentUser={user}
                onConversationUpdate={fetchConversations}
              />
            ) : (
              <div className="card h-full flex items-center justify-center">
                <div className="text-center">
                  <div className="w-24 h-24 bg-secondary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <MessageSquare className="w-12 h-12 text-secondary-400" />
                  </div>
                  <h3 className="text-xl font-semibold text-secondary-900 mb-2">
                    Select a conversation
                  </h3>
                  <p className="text-secondary-600 mb-6">
                    Choose a conversation from the list to start messaging
                  </p>
                  <button
                    onClick={() => setShowNewConversationModal(true)}
                    className="btn-primary"
                  >
                    <Plus className="w-5 h-5 mr-2" />
                    Start New Conversation
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* New Conversation Modal */}
        {showNewConversationModal && (
          <NewConversationModal
            currentUser={user}
            onClose={() => setShowNewConversationModal(false)}
            onConversationCreated={(conversation) => {
              setConversations(prev => [conversation, ...prev]);
              setSelectedConversation(conversation);
              setShowNewConversationModal(false);
            }}
          />
        )}
      </div>
    </div>
  );
}
