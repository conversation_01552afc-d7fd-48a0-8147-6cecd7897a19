import React, { useState, useEffect } from 'react';
import { X, Search, User, Users, Briefcase, Plus } from 'lucide-react';
import { messageService, Conversation, CreateConversationData } from '../../services/messageService';
import { User as UserType } from '../../services/authService';
import axios from 'axios';

interface NewConversationModalProps {
  currentUser: UserType;
  onClose: () => void;
  onConversationCreated: (conversation: Conversation) => void;
}

interface SearchUser {
  _id: string;
  name: string;
  email: string;
  profileImage?: string;
  role: string;
  company?: string;
}

interface Project {
  _id: string;
  title: string;
  status: string;
  client: {
    _id: string;
    name: string;
  };
  assignedVendor?: {
    _id: string;
    name: string;
  };
}

export default function NewConversationModal({ 
  currentUser, 
  onClose, 
  onConversationCreated 
}: NewConversationModalProps) {
  const [activeTab, setActiveTab] = useState<'users' | 'projects'>('users');
  const [searchTerm, setSearchTerm] = useState('');
  const [searchResults, setSearchResults] = useState<SearchUser[]>([]);
  const [projects, setProjects] = useState<Project[]>([]);
  const [selectedUsers, setSelectedUsers] = useState<SearchUser[]>([]);
  const [selectedProject, setSelectedProject] = useState<Project | null>(null);
  const [loading, setLoading] = useState(false);
  const [creating, setCreating] = useState(false);
  const [conversationType, setConversationType] = useState<'direct' | 'group' | 'project'>('direct');

  useEffect(() => {
    if (activeTab === 'projects') {
      fetchProjects();
    }
  }, [activeTab]);

  useEffect(() => {
    if (searchTerm.trim() && activeTab === 'users') {
      searchUsers();
    } else {
      setSearchResults([]);
    }
  }, [searchTerm, activeTab]);

  const searchUsers = async () => {
    try {
      setLoading(true);
      const response = await axios.get(`http://localhost:5000/api/users/search`, {
        params: { q: searchTerm, limit: 10 }
      });
      setSearchResults(response.data.filter((user: SearchUser) => user._id !== currentUser._id));
    } catch (error) {
      console.error('Failed to search users:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchProjects = async () => {
    try {
      setLoading(true);
      const response = await axios.get(`http://localhost:5000/api/projects`, {
        params: { 
          status: 'in-progress,open',
          limit: 20
        }
      });
      
      // Filter projects where user is involved
      const userProjects = response.data.projects.filter((project: Project) => 
        project.client._id === currentUser._id || 
        (project.assignedVendor && project.assignedVendor._id === currentUser._id)
      );
      
      setProjects(userProjects);
    } catch (error) {
      console.error('Failed to fetch projects:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleUserSelect = (user: SearchUser) => {
    if (!selectedUsers.find(u => u._id === user._id)) {
      setSelectedUsers(prev => [...prev, user]);
      
      // Auto-set conversation type based on selection
      if (selectedUsers.length === 0) {
        setConversationType('direct');
      } else {
        setConversationType('group');
      }
    }
  };

  const handleUserRemove = (userId: string) => {
    setSelectedUsers(prev => prev.filter(u => u._id !== userId));
    
    // Update conversation type
    if (selectedUsers.length <= 2) {
      setConversationType('direct');
    }
  };

  const handleProjectSelect = (project: Project) => {
    setSelectedProject(project);
    setConversationType('project');
    
    // Auto-select project participants
    const participants: SearchUser[] = [];
    
    if (project.client._id !== currentUser._id) {
      participants.push({
        _id: project.client._id,
        name: project.client.name,
        email: '',
        role: 'client'
      });
    }
    
    if (project.assignedVendor && project.assignedVendor._id !== currentUser._id) {
      participants.push({
        _id: project.assignedVendor._id,
        name: project.assignedVendor.name,
        email: '',
        role: 'vendor'
      });
    }
    
    setSelectedUsers(participants);
  };

  const handleCreateConversation = async () => {
    if (selectedUsers.length === 0) {
      alert('Please select at least one participant');
      return;
    }

    try {
      setCreating(true);
      
      const data: CreateConversationData = {
        participantIds: selectedUsers.map(user => user._id),
        type: conversationType
      };

      if (selectedProject) {
        data.projectId = selectedProject._id;
        data.title = `Project: ${selectedProject.title}`;
      }

      const conversation = await messageService.createConversation(data);
      onConversationCreated(conversation);
    } catch (error: any) {
      alert(error.message || 'Failed to create conversation');
    } finally {
      setCreating(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-2xl w-full max-h-[80vh] overflow-hidden">
        {/* Header */}
        <div className="p-6 border-b border-secondary-200">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold text-secondary-900">New Conversation</h2>
            <button
              onClick={onClose}
              className="text-secondary-400 hover:text-secondary-600"
            >
              <X className="w-6 h-6" />
            </button>
          </div>
        </div>

        {/* Tabs */}
        <div className="flex border-b border-secondary-200">
          <button
            onClick={() => setActiveTab('users')}
            className={`flex-1 py-3 px-4 text-sm font-medium transition-colors duration-200 ${
              activeTab === 'users'
                ? 'text-primary-600 border-b-2 border-primary-600'
                : 'text-secondary-600 hover:text-secondary-900'
            }`}
          >
            <Users className="w-4 h-4 mr-2 inline" />
            Find Users
          </button>
          <button
            onClick={() => setActiveTab('projects')}
            className={`flex-1 py-3 px-4 text-sm font-medium transition-colors duration-200 ${
              activeTab === 'projects'
                ? 'text-primary-600 border-b-2 border-primary-600'
                : 'text-secondary-600 hover:text-secondary-900'
            }`}
          >
            <Briefcase className="w-4 h-4 mr-2 inline" />
            Project Chat
          </button>
        </div>

        {/* Content */}
        <div className="p-6 max-h-96 overflow-y-auto">
          {activeTab === 'users' ? (
            <div className="space-y-4">
              {/* Search */}
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-secondary-400" />
                <input
                  type="text"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  placeholder="Search users by name or email..."
                  className="input-field pl-10 w-full"
                />
              </div>

              {/* Search Results */}
              {loading ? (
                <div className="text-center py-4">
                  <div className="w-6 h-6 border-2 border-primary-200 border-t-primary-600 rounded-full animate-spin mx-auto"></div>
                </div>
              ) : searchResults.length > 0 ? (
                <div className="space-y-2">
                  <p className="text-sm font-medium text-secondary-700">Search Results:</p>
                  {searchResults.map((user) => (
                    <div
                      key={user._id}
                      onClick={() => handleUserSelect(user)}
                      className="flex items-center space-x-3 p-3 rounded-lg border border-secondary-200 hover:bg-secondary-50 cursor-pointer transition-colors duration-200"
                    >
                      <div className="w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center">
                        {user.profileImage ? (
                          <img 
                            src={user.profileImage} 
                            alt={user.name}
                            className="w-10 h-10 rounded-full object-cover"
                          />
                        ) : (
                          <User className="w-5 h-5 text-primary-600" />
                        )}
                      </div>
                      <div className="flex-1">
                        <p className="font-medium text-secondary-900">{user.name}</p>
                        <p className="text-sm text-secondary-600">{user.email}</p>
                        {user.company && (
                          <p className="text-xs text-secondary-500">{user.company}</p>
                        )}
                      </div>
                      <span className="text-xs text-primary-600 font-medium capitalize">
                        {user.role}
                      </span>
                    </div>
                  ))}
                </div>
              ) : searchTerm.trim() ? (
                <div className="text-center py-8 text-secondary-600">
                  No users found matching "{searchTerm}"
                </div>
              ) : (
                <div className="text-center py-8 text-secondary-600">
                  Start typing to search for users
                </div>
              )}
            </div>
          ) : (
            <div className="space-y-4">
              {loading ? (
                <div className="text-center py-4">
                  <div className="w-6 h-6 border-2 border-primary-200 border-t-primary-600 rounded-full animate-spin mx-auto"></div>
                </div>
              ) : projects.length > 0 ? (
                <div className="space-y-2">
                  <p className="text-sm font-medium text-secondary-700">Your Projects:</p>
                  {projects.map((project) => (
                    <div
                      key={project._id}
                      onClick={() => handleProjectSelect(project)}
                      className={`p-3 rounded-lg border cursor-pointer transition-colors duration-200 ${
                        selectedProject?._id === project._id
                          ? 'border-primary-500 bg-primary-50'
                          : 'border-secondary-200 hover:bg-secondary-50'
                      }`}
                    >
                      <div className="flex items-center space-x-3">
                        <Briefcase className="w-5 h-5 text-primary-600" />
                        <div className="flex-1">
                          <p className="font-medium text-secondary-900">{project.title}</p>
                          <p className="text-sm text-secondary-600">
                            Client: {project.client.name}
                            {project.assignedVendor && (
                              <span> • Vendor: {project.assignedVendor.name}</span>
                            )}
                          </p>
                        </div>
                        <span className={`text-xs px-2 py-1 rounded-full font-medium ${
                          project.status === 'open' 
                            ? 'bg-success-100 text-success-700'
                            : 'bg-warning-100 text-warning-700'
                        }`}>
                          {project.status}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-secondary-600">
                  No active projects found
                </div>
              )}
            </div>
          )}
        </div>

        {/* Selected Users */}
        {selectedUsers.length > 0 && (
          <div className="px-6 py-4 border-t border-secondary-200 bg-secondary-50">
            <p className="text-sm font-medium text-secondary-700 mb-2">
              Selected Participants ({selectedUsers.length}):
            </p>
            <div className="flex flex-wrap gap-2">
              {selectedUsers.map((user) => (
                <span
                  key={user._id}
                  className="inline-flex items-center px-3 py-1 bg-primary-100 text-primary-700 rounded-full text-sm"
                >
                  {user.name}
                  <button
                    onClick={() => handleUserRemove(user._id)}
                    className="ml-2 text-primary-500 hover:text-primary-700"
                  >
                    <X className="w-3 h-3" />
                  </button>
                </span>
              ))}
            </div>
          </div>
        )}

        {/* Footer */}
        <div className="p-6 border-t border-secondary-200 flex justify-end space-x-3">
          <button
            onClick={onClose}
            className="btn-secondary"
          >
            Cancel
          </button>
          <button
            onClick={handleCreateConversation}
            disabled={creating || selectedUsers.length === 0}
            className="btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {creating ? (
              <div className="flex items-center">
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                Creating...
              </div>
            ) : (
              <>
                <Plus className="w-4 h-4 mr-2" />
                Create Conversation
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );
}
