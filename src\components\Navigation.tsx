import React, { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { Globe, Menu, X, User as UserIcon, Bell, Search, ChevronDown } from 'lucide-react';
import NotificationBell from './Notifications/NotificationBell';
import { User } from '../services/authService';

interface NavigationProps {
  isAuthenticated?: boolean;
  user?: User;
  onSignIn?: () => void;
  onSignUp?: () => void;
  onSignOut?: () => void;
}

export default function Navigation({ isAuthenticated = false, user, onSignIn, onSignUp, onSignOut }: NavigationProps) {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isProfileOpen, setIsProfileOpen] = useState(false);
  const location = useLocation();

  const toggleMenu = () => setIsMenuOpen(!isMenuOpen);
  const toggleProfile = () => setIsProfileOpen(!isProfileOpen);

  return (
    <nav className="bg-white/95 backdrop-blur-sm border-b border-secondary-100 sticky top-0 z-50">
      <div className="container-custom">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <Link to="/" className="flex items-center space-x-2">
            <div className="w-10 h-10 bg-primary-600 rounded-lg flex items-center justify-center">
              <Globe className="w-6 h-6 text-white" />
            </div>
            <span className="text-xl font-display font-bold text-secondary-900">GlobalConnect</span>
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-8">
            {!isAuthenticated ? (
              <>
                <Link to="/services" className="text-secondary-600 hover:text-primary-600 transition-colors duration-200">Services</Link>
                <Link to="/pricing" className="text-secondary-600 hover:text-primary-600 transition-colors duration-200">Pricing</Link>
                <Link to="/about" className="text-secondary-600 hover:text-primary-600 transition-colors duration-200">About</Link>
                <Link to="/contact" className="text-secondary-600 hover:text-primary-600 transition-colors duration-200">Contact</Link>
              </>
            ) : (
              <>
                <Link
                  to="/dashboard"
                  className={`transition-colors duration-200 ${
                    location.pathname === '/dashboard'
                      ? 'text-primary-600'
                      : 'text-secondary-600 hover:text-primary-600'
                  }`}
                >
                  Dashboard
                </Link>
                <Link
                  to="/projects"
                  className={`transition-colors duration-200 ${
                    location.pathname === '/projects'
                      ? 'text-primary-600'
                      : 'text-secondary-600 hover:text-primary-600'
                  }`}
                >
                  Projects
                </Link>
                <Link
                  to="/messages"
                  className={`transition-colors duration-200 ${
                    location.pathname === '/messages'
                      ? 'text-primary-600'
                      : 'text-secondary-600 hover:text-primary-600'
                  }`}
                >
                  Messages
                </Link>
                <Link
                  to="/analytics"
                  className={`transition-colors duration-200 ${
                    location.pathname === '/analytics'
                      ? 'text-primary-600'
                      : 'text-secondary-600 hover:text-primary-600'
                  }`}
                >
                  Analytics
                </Link>
                <Link
                  to="/billing"
                  className={`transition-colors duration-200 ${
                    location.pathname === '/billing'
                      ? 'text-primary-600'
                      : 'text-secondary-600 hover:text-primary-600'
                  }`}
                >
                  Billing
                </Link>
                {user?.role === 'admin' && (
                  <Link
                    to="/admin"
                    className={`transition-colors duration-200 ${
                      location.pathname === '/admin'
                        ? 'text-primary-600'
                        : 'text-secondary-600 hover:text-primary-600'
                    }`}
                  >
                    Admin
                  </Link>
                )}
                {user?.role === 'vendor' && (
                  <Link
                    to="/bids"
                    className={`transition-colors duration-200 ${
                      location.pathname === '/bids'
                        ? 'text-primary-600'
                        : 'text-secondary-600 hover:text-primary-600'
                    }`}
                  >
                    My Bids
                  </Link>
                )}
                {user?.role === 'admin' && (
                  <Link
                    to="/admin"
                    className={`transition-colors duration-200 ${
                      location.pathname === '/admin'
                        ? 'text-primary-600'
                        : 'text-secondary-600 hover:text-primary-600'
                    }`}
                  >
                    Admin
                  </Link>
                )}
              </>
            )}
          </div>

          {/* Right Side */}
          <div className="flex items-center space-x-4">
            {!isAuthenticated ? (
              <>
                <button
                  onClick={onSignIn}
                  className="btn-secondary text-sm py-2 px-4"
                >
                  Sign In
                </button>
                <button
                  onClick={onSignUp}
                  className="btn-primary text-sm py-2 px-4"
                >
                  Get Started
                </button>
              </>
            ) : (
              <>
                {/* Search */}
                <button className="p-2 text-secondary-600 hover:text-primary-600 transition-colors duration-200">
                  <Search className="w-5 h-5" />
                </button>

                {/* Notifications */}
                {user && <NotificationBell user={user} />}

                {/* Profile Dropdown */}
                <div className="relative">
                  <button 
                    onClick={toggleProfile}
                    className="flex items-center space-x-2 p-2 rounded-lg hover:bg-secondary-50 transition-colors duration-200"
                  >
                    {user?.avatar ? (
                      <img src={user.avatar} alt={user.name} className="w-8 h-8 rounded-full" />
                    ) : (
                      <div className="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">
                        <UserIcon className="w-4 h-4 text-primary-600" />
                      </div>
                    )}
                    <ChevronDown className="w-4 h-4 text-secondary-600" />
                  </button>

                  {/* Profile Dropdown Menu */}
                  {isProfileOpen && (
                    <div className="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-medium border border-secondary-100 py-2">
                      <div className="px-4 py-2 border-b border-secondary-100">
                        <p className="font-semibold text-secondary-900">{user?.name}</p>
                        <p className="text-sm text-secondary-500 capitalize">{user?.role}</p>
                      </div>
                      <Link to="/profile" className="block px-4 py-2 text-secondary-700 hover:bg-secondary-50 transition-colors duration-200">Profile</Link>
                      <Link to="/settings" className="block px-4 py-2 text-secondary-700 hover:bg-secondary-50 transition-colors duration-200">Settings</Link>
                      <Link to="/billing" className="block px-4 py-2 text-secondary-700 hover:bg-secondary-50 transition-colors duration-200">Billing</Link>
                      <hr className="my-2 border-secondary-100" />
                      <button
                        onClick={onSignOut}
                        className="block w-full text-left px-4 py-2 text-error-600 hover:bg-error-50 transition-colors duration-200"
                      >
                        Sign Out
                      </button>
                    </div>
                  )}
                </div>
              </>
            )}

            {/* Mobile Menu Button */}
            <button 
              onClick={toggleMenu}
              className="md:hidden p-2 text-secondary-600 hover:text-primary-600 transition-colors duration-200"
            >
              {isMenuOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
            </button>
          </div>
        </div>

        {/* Mobile Menu */}
        {isMenuOpen && (
          <div className="md:hidden border-t border-secondary-100 py-4">
            <div className="space-y-4">
              {!isAuthenticated ? (
                <>
                  <Link to="/services" className="block text-secondary-600 hover:text-primary-600 transition-colors duration-200">Services</Link>
                  <Link to="/pricing" className="block text-secondary-600 hover:text-primary-600 transition-colors duration-200">Pricing</Link>
                  <Link to="/about" className="block text-secondary-600 hover:text-primary-600 transition-colors duration-200">About</Link>
                  <Link to="/contact" className="block text-secondary-600 hover:text-primary-600 transition-colors duration-200">Contact</Link>
                  <div className="pt-4 space-y-2">
                    <button
                      onClick={onSignIn}
                      className="btn-secondary w-full"
                    >
                      Sign In
                    </button>
                    <button
                      onClick={onSignUp}
                      className="btn-primary w-full"
                    >
                      Get Started
                    </button>
                  </div>
                </>
              ) : (
                <>
                  <Link to="/dashboard" className="block text-secondary-600 hover:text-primary-600 transition-colors duration-200">Dashboard</Link>
                  <Link to="/projects" className="block text-secondary-600 hover:text-primary-600 transition-colors duration-200">Projects</Link>
                  <Link to="/messages" className="block text-secondary-600 hover:text-primary-600 transition-colors duration-200">Messages</Link>
                  <Link to="/analytics" className="block text-secondary-600 hover:text-primary-600 transition-colors duration-200">Analytics</Link>
                  <Link to="/billing" className="block text-secondary-600 hover:text-primary-600 transition-colors duration-200">Billing</Link>
                  {user?.role === 'admin' && (
                    <Link to="/admin" className="block text-secondary-600 hover:text-primary-600 transition-colors duration-200">Admin</Link>
                  )}
                  {user?.role === 'vendor' && (
                    <Link to="/bids" className="block text-secondary-600 hover:text-primary-600 transition-colors duration-200">My Bids</Link>
                  )}
                  {user?.role === 'admin' && (
                    <Link to="/admin" className="block text-secondary-600 hover:text-primary-600 transition-colors duration-200">Admin</Link>
                  )}
                  <hr className="border-secondary-100" />
                  <Link to="/profile" className="block text-secondary-600 hover:text-primary-600 transition-colors duration-200">Profile</Link>
                  <Link to="/settings" className="block text-secondary-600 hover:text-primary-600 transition-colors duration-200">Settings</Link>
                  <button
                    onClick={onSignOut}
                    className="block w-full text-left text-error-600 hover:text-error-700 transition-colors duration-200"
                  >
                    Sign Out
                  </button>
                </>
              )}
            </div>
          </div>
        )}
      </div>
    </nav>
  );
}
