import React, { useState } from 'react';
import { Globe, Menu, X, User, Bell, Search, ChevronDown } from 'lucide-react';

interface NavigationProps {
  isAuthenticated?: boolean;
  user?: {
    name: string;
    avatar?: string;
    role: 'client' | 'vendor' | 'admin';
  };
  onSignIn?: () => void;
  onSignUp?: () => void;
  onSignOut?: () => void;
}

export default function Navigation({ isAuthenticated = false, user, onSignIn, onSignUp, onSignOut }: NavigationProps) {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isProfileOpen, setIsProfileOpen] = useState(false);

  const toggleMenu = () => setIsMenuOpen(!isMenuOpen);
  const toggleProfile = () => setIsProfileOpen(!isProfileOpen);

  return (
    <nav className="bg-white/95 backdrop-blur-sm border-b border-secondary-100 sticky top-0 z-50">
      <div className="container-custom">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <div className="flex items-center space-x-2">
            <div className="w-10 h-10 bg-primary-600 rounded-lg flex items-center justify-center">
              <Globe className="w-6 h-6 text-white" />
            </div>
            <span className="text-xl font-display font-bold text-secondary-900">GlobalConnect</span>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-8">
            {!isAuthenticated ? (
              <>
                <a href="#features" className="text-secondary-600 hover:text-primary-600 transition-colors duration-200">Features</a>
                <a href="#pricing" className="text-secondary-600 hover:text-primary-600 transition-colors duration-200">Pricing</a>
                <a href="#about" className="text-secondary-600 hover:text-primary-600 transition-colors duration-200">About</a>
                <a href="#contact" className="text-secondary-600 hover:text-primary-600 transition-colors duration-200">Contact</a>
              </>
            ) : (
              <>
                <a href="/dashboard" className="text-secondary-600 hover:text-primary-600 transition-colors duration-200">Dashboard</a>
                <a href="/projects" className="text-secondary-600 hover:text-primary-600 transition-colors duration-200">Projects</a>
                <a href="/messages" className="text-secondary-600 hover:text-primary-600 transition-colors duration-200">Messages</a>
                {user?.role === 'vendor' && (
                  <a href="/bids" className="text-secondary-600 hover:text-primary-600 transition-colors duration-200">My Bids</a>
                )}
                {user?.role === 'admin' && (
                  <a href="/admin" className="text-secondary-600 hover:text-primary-600 transition-colors duration-200">Admin</a>
                )}
              </>
            )}
          </div>

          {/* Right Side */}
          <div className="flex items-center space-x-4">
            {!isAuthenticated ? (
              <>
                <button
                  onClick={onSignIn}
                  className="btn-secondary text-sm py-2 px-4"
                >
                  Sign In
                </button>
                <button
                  onClick={onSignUp}
                  className="btn-primary text-sm py-2 px-4"
                >
                  Get Started
                </button>
              </>
            ) : (
              <>
                {/* Search */}
                <button className="p-2 text-secondary-600 hover:text-primary-600 transition-colors duration-200">
                  <Search className="w-5 h-5" />
                </button>

                {/* Notifications */}
                <button className="p-2 text-secondary-600 hover:text-primary-600 transition-colors duration-200 relative">
                  <Bell className="w-5 h-5" />
                  <span className="absolute -top-1 -right-1 w-3 h-3 bg-error-500 rounded-full"></span>
                </button>

                {/* Profile Dropdown */}
                <div className="relative">
                  <button 
                    onClick={toggleProfile}
                    className="flex items-center space-x-2 p-2 rounded-lg hover:bg-secondary-50 transition-colors duration-200"
                  >
                    {user?.avatar ? (
                      <img src={user.avatar} alt={user.name} className="w-8 h-8 rounded-full" />
                    ) : (
                      <div className="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">
                        <User className="w-4 h-4 text-primary-600" />
                      </div>
                    )}
                    <ChevronDown className="w-4 h-4 text-secondary-600" />
                  </button>

                  {/* Profile Dropdown Menu */}
                  {isProfileOpen && (
                    <div className="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-medium border border-secondary-100 py-2">
                      <div className="px-4 py-2 border-b border-secondary-100">
                        <p className="font-semibold text-secondary-900">{user?.name}</p>
                        <p className="text-sm text-secondary-500 capitalize">{user?.role}</p>
                      </div>
                      <a href="/profile" className="block px-4 py-2 text-secondary-700 hover:bg-secondary-50 transition-colors duration-200">Profile</a>
                      <a href="/settings" className="block px-4 py-2 text-secondary-700 hover:bg-secondary-50 transition-colors duration-200">Settings</a>
                      <a href="/billing" className="block px-4 py-2 text-secondary-700 hover:bg-secondary-50 transition-colors duration-200">Billing</a>
                      <hr className="my-2 border-secondary-100" />
                      <button
                        onClick={onSignOut}
                        className="block w-full text-left px-4 py-2 text-error-600 hover:bg-error-50 transition-colors duration-200"
                      >
                        Sign Out
                      </button>
                    </div>
                  )}
                </div>
              </>
            )}

            {/* Mobile Menu Button */}
            <button 
              onClick={toggleMenu}
              className="md:hidden p-2 text-secondary-600 hover:text-primary-600 transition-colors duration-200"
            >
              {isMenuOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
            </button>
          </div>
        </div>

        {/* Mobile Menu */}
        {isMenuOpen && (
          <div className="md:hidden border-t border-secondary-100 py-4">
            <div className="space-y-4">
              {!isAuthenticated ? (
                <>
                  <a href="#features" className="block text-secondary-600 hover:text-primary-600 transition-colors duration-200">Features</a>
                  <a href="#pricing" className="block text-secondary-600 hover:text-primary-600 transition-colors duration-200">Pricing</a>
                  <a href="#about" className="block text-secondary-600 hover:text-primary-600 transition-colors duration-200">About</a>
                  <a href="#contact" className="block text-secondary-600 hover:text-primary-600 transition-colors duration-200">Contact</a>
                  <div className="pt-4 space-y-2">
                    <button
                      onClick={onSignIn}
                      className="btn-secondary w-full"
                    >
                      Sign In
                    </button>
                    <button
                      onClick={onSignUp}
                      className="btn-primary w-full"
                    >
                      Get Started
                    </button>
                  </div>
                </>
              ) : (
                <>
                  <a href="/dashboard" className="block text-secondary-600 hover:text-primary-600 transition-colors duration-200">Dashboard</a>
                  <a href="/projects" className="block text-secondary-600 hover:text-primary-600 transition-colors duration-200">Projects</a>
                  <a href="/messages" className="block text-secondary-600 hover:text-primary-600 transition-colors duration-200">Messages</a>
                  {user?.role === 'vendor' && (
                    <a href="/bids" className="block text-secondary-600 hover:text-primary-600 transition-colors duration-200">My Bids</a>
                  )}
                  {user?.role === 'admin' && (
                    <a href="/admin" className="block text-secondary-600 hover:text-primary-600 transition-colors duration-200">Admin</a>
                  )}
                  <hr className="border-secondary-100" />
                  <a href="/profile" className="block text-secondary-600 hover:text-primary-600 transition-colors duration-200">Profile</a>
                  <a href="/settings" className="block text-secondary-600 hover:text-primary-600 transition-colors duration-200">Settings</a>
                  <button
                    onClick={onSignOut}
                    className="block w-full text-left text-error-600 hover:text-error-700 transition-colors duration-200"
                  >
                    Sign Out
                  </button>
                </>
              )}
            </div>
          </div>
        )}
      </div>
    </nav>
  );
}
