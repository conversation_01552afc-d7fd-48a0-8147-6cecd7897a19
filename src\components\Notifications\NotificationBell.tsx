import React, { useState, useEffect } from 'react';
import { Bell } from 'lucide-react';
import { notificationService } from '../../services/notificationService';
import { User } from '../../services/authService';
import NotificationCenter from './NotificationCenter';

interface NotificationBellProps {
  user: User;
}

export default function NotificationBell({ user }: NotificationBellProps) {
  const [unreadCount, setUnreadCount] = useState(0);
  const [isOpen, setIsOpen] = useState(false);
  const [hasPermission, setHasPermission] = useState(false);

  useEffect(() => {
    // Initialize notification service
    notificationService.initializeSocket(user._id);
    
    // Request browser notification permission
    requestNotificationPermission();
    
    // Fetch initial unread count
    fetchUnreadCount();

    // Set up event listeners
    notificationService.on('new_notification', handleNewNotification);
    notificationService.on('notification_read', handleNotificationRead);
    notificationService.on('notifications_read_all', handleNotificationsReadAll);

    return () => {
      notificationService.off('new_notification', handleNewNotification);
      notificationService.off('notification_read', handleNotificationRead);
      notificationService.off('notifications_read_all', handleNotificationsReadAll);
      notificationService.disconnect();
    };
  }, [user._id]);

  const requestNotificationPermission = async () => {
    const permission = await notificationService.requestPermission();
    setHasPermission(permission);
  };

  const fetchUnreadCount = async () => {
    try {
      const response = await notificationService.getNotifications(1, 1, true);
      setUnreadCount(response.unreadCount);
    } catch (error) {
      console.error('Failed to fetch unread count:', error);
    }
  };

  const handleNewNotification = (data: { notification: any; unreadCount: number }) => {
    setUnreadCount(data.unreadCount);
    
    // Show browser notification if permission granted and page is not visible
    if (hasPermission && document.hidden) {
      notificationService.showBrowserNotification(data.notification);
    }
  };

  const handleNotificationRead = () => {
    setUnreadCount(prev => Math.max(0, prev - 1));
  };

  const handleNotificationsReadAll = () => {
    setUnreadCount(0);
  };

  const handleToggle = () => {
    setIsOpen(!isOpen);
  };

  return (
    <>
      <button
        onClick={handleToggle}
        className="relative p-2 text-secondary-600 hover:text-primary-600 transition-colors duration-200"
        title="Notifications"
      >
        <Bell className="w-6 h-6" />
        {unreadCount > 0 && (
          <span className="absolute -top-1 -right-1 bg-primary-600 text-white text-xs font-medium px-1.5 py-0.5 rounded-full min-w-[1.25rem] h-5 flex items-center justify-center">
            {unreadCount > 99 ? '99+' : unreadCount}
          </span>
        )}
      </button>

      <NotificationCenter
        user={user}
        isOpen={isOpen}
        onClose={() => setIsOpen(false)}
      />
    </>
  );
}
