import React, { useState, useEffect } from 'react';
import { 
  <PERSON>, 
  <PERSON>, 
  <PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  Trash2, 
  Fi<PERSON>,
  Setting<PERSON>,
  MoreVertical
} from 'lucide-react';
import { notificationService, Notification } from '../../services/notificationService';
import { User } from '../../services/authService';
import NotificationItem from './NotificationItem';

interface NotificationCenterProps {
  user: User;
  isOpen: boolean;
  onClose: () => void;
}

export default function NotificationCenter({ user, isOpen, onClose }: NotificationCenterProps) {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [filter, setFilter] = useState<'all' | 'unread'>('all');
  const [typeFilter, setTypeFilter] = useState<string>('all');
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);

  useEffect(() => {
    if (isOpen) {
      fetchNotifications();
      
      // Set up real-time listeners
      notificationService.on('new_notification', handleNewNotification);
      notificationService.on('notification_read', handleNotificationRead);
      notificationService.on('notifications_read_all', handleNotificationsReadAll);
      notificationService.on('notification_deleted', handleNotificationDeleted);

      return () => {
        notificationService.off('new_notification', handleNewNotification);
        notificationService.off('notification_read', handleNotificationRead);
        notificationService.off('notifications_read_all', handleNotificationsReadAll);
        notificationService.off('notification_deleted', handleNotificationDeleted);
      };
    }
  }, [isOpen, filter, typeFilter]);

  const fetchNotifications = async (pageNum: number = 1) => {
    try {
      setLoading(pageNum === 1);
      const response = await notificationService.getNotifications(
        pageNum, 
        20, 
        filter === 'unread'
      );
      
      if (pageNum === 1) {
        setNotifications(response.notifications);
      } else {
        setNotifications(prev => [...prev, ...response.notifications]);
      }
      
      setUnreadCount(response.unreadCount);
      setHasMore(pageNum < response.totalPages);
      setPage(pageNum);
    } catch (error: any) {
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleNewNotification = (data: { notification: Notification; unreadCount: number }) => {
    setNotifications(prev => [data.notification, ...prev]);
    setUnreadCount(data.unreadCount);
    
    // Show browser notification if permission granted
    notificationService.showBrowserNotification(data.notification);
  };

  const handleNotificationRead = (data: { notificationId: string }) => {
    setNotifications(prev => 
      prev.map(notif => 
        notif._id === data.notificationId 
          ? { ...notif, isRead: true, readAt: new Date().toISOString() }
          : notif
      )
    );
    setUnreadCount(prev => Math.max(0, prev - 1));
  };

  const handleNotificationsReadAll = () => {
    setNotifications(prev => 
      prev.map(notif => ({ ...notif, isRead: true, readAt: new Date().toISOString() }))
    );
    setUnreadCount(0);
  };

  const handleNotificationDeleted = (data: { notificationId: string }) => {
    setNotifications(prev => prev.filter(notif => notif._id !== data.notificationId));
  };

  const handleMarkAsRead = async (notificationId: string) => {
    try {
      await notificationService.markAsRead(notificationId);
    } catch (error: any) {
      console.error('Failed to mark notification as read:', error.message);
    }
  };

  const handleMarkAllAsRead = async () => {
    try {
      await notificationService.markAllAsRead(typeFilter !== 'all' ? typeFilter : undefined);
    } catch (error: any) {
      console.error('Failed to mark all notifications as read:', error.message);
    }
  };

  const handleDeleteNotification = async (notificationId: string) => {
    try {
      await notificationService.deleteNotification(notificationId);
    } catch (error: any) {
      console.error('Failed to delete notification:', error.message);
    }
  };

  const handleLoadMore = () => {
    if (hasMore && !loading) {
      fetchNotifications(page + 1);
    }
  };

  const filteredNotifications = notifications.filter(notification => {
    if (typeFilter !== 'all' && notification.type !== typeFilter) {
      return false;
    }
    return true;
  });

  const groupedNotifications = notificationService.groupNotifications(filteredNotifications);

  if (!isOpen) return null;

  return (
    <>
      {/* Backdrop */}
      <div 
        className="fixed inset-0 bg-black bg-opacity-50 z-40"
        onClick={onClose}
      />
      
      {/* Notification Panel */}
      <div className="fixed right-0 top-0 h-full w-96 bg-white shadow-xl z-50 flex flex-col">
        {/* Header */}
        <div className="p-4 border-b border-secondary-200">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-2">
              <Bell className="w-5 h-5 text-primary-600" />
              <h2 className="text-lg font-semibold text-secondary-900">Notifications</h2>
              {unreadCount > 0 && (
                <span className="bg-primary-600 text-white text-xs font-medium px-2 py-1 rounded-full">
                  {unreadCount}
                </span>
              )}
            </div>
            <button
              onClick={onClose}
              className="text-secondary-400 hover:text-secondary-600"
            >
              <X className="w-5 h-5" />
            </button>
          </div>

          {/* Filters */}
          <div className="flex items-center space-x-2 mb-3">
            <select
              value={filter}
              onChange={(e) => setFilter(e.target.value as 'all' | 'unread')}
              className="text-sm border border-secondary-300 rounded px-2 py-1"
            >
              <option value="all">All</option>
              <option value="unread">Unread</option>
            </select>

            <select
              value={typeFilter}
              onChange={(e) => setTypeFilter(e.target.value)}
              className="text-sm border border-secondary-300 rounded px-2 py-1"
            >
              <option value="all">All Types</option>
              <option value="message">Messages</option>
              <option value="bid">Bids</option>
              <option value="bid_update">Bid Updates</option>
              <option value="project_update">Project Updates</option>
              <option value="document">Documents</option>
              <option value="system">System</option>
            </select>
          </div>

          {/* Actions */}
          <div className="flex items-center justify-between">
            <button
              onClick={handleMarkAllAsRead}
              disabled={unreadCount === 0}
              className="text-sm text-primary-600 hover:text-primary-700 disabled:text-secondary-400 disabled:cursor-not-allowed"
            >
              <CheckCheck className="w-4 h-4 mr-1 inline" />
              Mark all read
            </button>
            
            <button className="text-sm text-secondary-600 hover:text-secondary-700">
              <Settings className="w-4 h-4 mr-1 inline" />
              Settings
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto">
          {loading && notifications.length === 0 ? (
            <div className="flex items-center justify-center h-32">
              <div className="w-6 h-6 border-2 border-primary-200 border-t-primary-600 rounded-full animate-spin"></div>
            </div>
          ) : error ? (
            <div className="p-4 text-center text-error-600">
              <p>{error}</p>
              <button
                onClick={() => fetchNotifications()}
                className="mt-2 text-sm text-primary-600 hover:text-primary-700"
              >
                Try again
              </button>
            </div>
          ) : filteredNotifications.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-32 text-secondary-500">
              <Bell className="w-8 h-8 mb-2" />
              <p className="text-sm">No notifications</p>
            </div>
          ) : (
            <div className="divide-y divide-secondary-100">
              {Object.entries(groupedNotifications).map(([group, groupNotifications]) => (
                <div key={group}>
                  <div className="px-4 py-2 bg-secondary-50 border-b border-secondary-100">
                    <h3 className="text-xs font-medium text-secondary-600 uppercase tracking-wide">
                      {group}
                    </h3>
                  </div>
                  {groupNotifications.map((notification) => (
                    <NotificationItem
                      key={notification._id}
                      notification={notification}
                      onMarkAsRead={handleMarkAsRead}
                      onDelete={handleDeleteNotification}
                    />
                  ))}
                </div>
              ))}
              
              {/* Load More */}
              {hasMore && (
                <div className="p-4 text-center">
                  <button
                    onClick={handleLoadMore}
                    disabled={loading}
                    className="text-sm text-primary-600 hover:text-primary-700 disabled:text-secondary-400"
                  >
                    {loading ? 'Loading...' : 'Load more'}
                  </button>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </>
  );
}
