import React, { useState } from 'react';
import { Check, Trash2, MoreVertical, ExternalLink } from 'lucide-react';
import { Notification, notificationService } from '../../services/notificationService';

interface NotificationItemProps {
  notification: Notification;
  onMarkAsRead: (id: string) => void;
  onDelete: (id: string) => void;
}

export default function NotificationItem({ notification, onMarkAsRead, onDelete }: NotificationItemProps) {
  const [showActions, setShowActions] = useState(false);

  const handleClick = () => {
    if (!notification.isRead) {
      onMarkAsRead(notification._id);
    }
    
    // Navigate based on notification type
    handleNotificationNavigation();
  };

  const handleNotificationNavigation = () => {
    const { type, reference, metadata } = notification;
    
    switch (type) {
      case 'message':
        if (metadata.conversationId) {
          window.location.href = `/messages?conversation=${metadata.conversationId}`;
        } else {
          window.location.href = '/messages';
        }
        break;
      case 'bid':
      case 'bid_update':
        if (metadata.projectId) {
          window.location.href = `/projects/${metadata.projectId}`;
        } else {
          window.location.href = '/projects';
        }
        break;
      case 'project_update':
        if (reference.id) {
          window.location.href = `/projects/${reference.id}`;
        } else {
          window.location.href = '/projects';
        }
        break;
      case 'document':
        if (metadata.projectId) {
          window.location.href = `/projects/${metadata.projectId}/documents`;
        } else {
          window.location.href = '/projects';
        }
        break;
      default:
        // For system notifications, just mark as read
        break;
    }
  };

  const handleMarkAsRead = (e: React.MouseEvent) => {
    e.stopPropagation();
    onMarkAsRead(notification._id);
  };

  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation();
    onDelete(notification._id);
    setShowActions(false);
  };

  const getNotificationIcon = () => {
    return notificationService.getNotificationIcon(notification.type);
  };

  const getNotificationColors = () => {
    return {
      text: notificationService.getNotificationColor(notification.type),
      bg: notificationService.getNotificationBgColor(notification.type)
    };
  };

  const colors = getNotificationColors();

  return (
    <div
      className={`relative p-4 hover:bg-secondary-50 cursor-pointer transition-colors duration-200 ${
        !notification.isRead ? 'bg-primary-50 border-l-4 border-primary-500' : ''
      }`}
      onClick={handleClick}
      onMouseEnter={() => setShowActions(true)}
      onMouseLeave={() => setShowActions(false)}
    >
      <div className="flex items-start space-x-3">
        {/* Icon */}
        <div className={`w-10 h-10 rounded-full flex items-center justify-center text-lg ${colors.bg}`}>
          <span>{getNotificationIcon()}</span>
        </div>

        {/* Content */}
        <div className="flex-1 min-w-0">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <h4 className={`text-sm font-medium ${!notification.isRead ? 'text-secondary-900' : 'text-secondary-700'}`}>
                {notification.title}
              </h4>
              <p className={`text-sm mt-1 ${!notification.isRead ? 'text-secondary-700' : 'text-secondary-600'}`}>
                {notification.content}
              </p>
              
              {/* Metadata */}
              {notification.metadata && Object.keys(notification.metadata).length > 0 && (
                <div className="mt-2 space-y-1">
                  {notification.metadata.amount && (
                    <span className="inline-block text-xs bg-green-100 text-green-700 px-2 py-1 rounded">
                      ${notification.metadata.amount.toLocaleString()}
                    </span>
                  )}
                  {notification.metadata.fileName && (
                    <span className="inline-block text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded ml-1">
                      📄 {notification.metadata.fileName}
                    </span>
                  )}
                  {notification.metadata.status && (
                    <span className={`inline-block text-xs px-2 py-1 rounded ml-1 ${
                      notification.metadata.status === 'accepted' 
                        ? 'bg-green-100 text-green-700'
                        : notification.metadata.status === 'rejected'
                        ? 'bg-red-100 text-red-700'
                        : 'bg-yellow-100 text-yellow-700'
                    }`}>
                      {notification.metadata.status}
                    </span>
                  )}
                </div>
              )}
            </div>

            {/* Unread indicator */}
            {!notification.isRead && (
              <div className="w-2 h-2 bg-primary-600 rounded-full ml-2 mt-1"></div>
            )}
          </div>

          {/* Time */}
          <p className="text-xs text-secondary-500 mt-2">
            {notificationService.formatNotificationTime(notification.createdAt)}
          </p>
        </div>

        {/* Actions */}
        {showActions && (
          <div className="flex items-center space-x-1">
            {!notification.isRead && (
              <button
                onClick={handleMarkAsRead}
                className="p-1 text-secondary-400 hover:text-primary-600 transition-colors duration-200"
                title="Mark as read"
              >
                <Check className="w-4 h-4" />
              </button>
            )}
            
            <button
              onClick={handleDelete}
              className="p-1 text-secondary-400 hover:text-error-600 transition-colors duration-200"
              title="Delete notification"
            >
              <Trash2 className="w-4 h-4" />
            </button>

            {(notification.type === 'message' || notification.type === 'bid' || notification.type === 'project_update') && (
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  handleNotificationNavigation();
                }}
                className="p-1 text-secondary-400 hover:text-primary-600 transition-colors duration-200"
                title="Open"
              >
                <ExternalLink className="w-4 h-4" />
              </button>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
