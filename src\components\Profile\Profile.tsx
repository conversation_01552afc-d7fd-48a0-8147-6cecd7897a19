import React, { useState, useEffect } from 'react';
import { 
  User, 
  Mail, 
  MapPin, 
  Globe, 
  Building, 
  Star, 
  Edit3, 
  Save, 
  X,
  Camera,
  Plus,
  Trash2
} from 'lucide-react';
import { authService, User as UserType } from '../../services/authService';

interface ProfileProps {
  user: UserType;
  onUserUpdate: (user: UserType) => void;
}

export default function Profile({ user, onUserUpdate }: ProfileProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [formData, setFormData] = useState({
    name: user.name || '',
    email: user.email || '',
    company: user.company || '',
    bio: user.bio || '',
    location: user.location || '',
    website: user.website || '',
    skills: user.skills || []
  });
  const [newSkill, setNewSkill] = useState('');

  useEffect(() => {
    setFormData({
      name: user.name || '',
      email: user.email || '',
      company: user.company || '',
      bio: user.bio || '',
      location: user.location || '',
      website: user.website || '',
      skills: user.skills || []
    });
  }, [user]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setFormData(prev => ({
      ...prev,
      [e.target.name]: e.target.value
    }));
    if (error) setError('');
  };

  const handleAddSkill = () => {
    if (newSkill.trim() && !formData.skills.includes(newSkill.trim())) {
      setFormData(prev => ({
        ...prev,
        skills: [...prev.skills, newSkill.trim()]
      }));
      setNewSkill('');
    }
  };

  const handleRemoveSkill = (skillToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      skills: prev.skills.filter(skill => skill !== skillToRemove)
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    try {
      const updatedUser = await authService.updateProfile(formData);
      onUserUpdate(updatedUser);
      setIsEditing(false);
    } catch (error: any) {
      setError(error.message || 'Failed to update profile');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    setFormData({
      name: user.name || '',
      email: user.email || '',
      company: user.company || '',
      bio: user.bio || '',
      location: user.location || '',
      website: user.website || '',
      skills: user.skills || []
    });
    setIsEditing(false);
    setError('');
  };

  return (
    <div className="min-h-screen bg-secondary-50">
      <div className="container-custom py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-display font-bold text-secondary-900">Profile</h1>
            <p className="text-secondary-600">Manage your account information and preferences</p>
          </div>
          {!isEditing && (
            <button
              onClick={() => setIsEditing(true)}
              className="btn-primary"
            >
              <Edit3 className="w-5 h-5 mr-2" />
              Edit Profile
            </button>
          )}
        </div>

        {/* Error Message */}
        {error && (
          <div className="mb-6 p-4 bg-error-50 border border-error-200 rounded-lg">
            <p className="text-error-700">{error}</p>
          </div>
        )}

        <div className="grid lg:grid-cols-3 gap-8">
          {/* Profile Card */}
          <div className="lg:col-span-1">
            <div className="card p-6 text-center">
              {/* Avatar */}
              <div className="relative inline-block mb-6">
                <div className="w-32 h-32 bg-primary-100 rounded-full flex items-center justify-center mx-auto">
                  {user.companyLogo ? (
                    <img 
                      src={user.companyLogo} 
                      alt={user.name}
                      className="w-32 h-32 rounded-full object-cover"
                    />
                  ) : (
                    <User className="w-16 h-16 text-primary-600" />
                  )}
                </div>
                {isEditing && (
                  <button className="absolute bottom-0 right-0 w-10 h-10 bg-primary-600 text-white rounded-full flex items-center justify-center hover:bg-primary-700 transition-colors duration-200">
                    <Camera className="w-5 h-5" />
                  </button>
                )}
              </div>

              <h2 className="text-xl font-display font-semibold text-secondary-900 mb-2">
                {user.name}
              </h2>
              <p className="text-secondary-600 mb-4 capitalize">{user.role}</p>
              
              {user.rating && (
                <div className="flex items-center justify-center mb-4">
                  <Star className="w-5 h-5 text-warning-500 fill-current" />
                  <span className="ml-1 font-semibold text-secondary-900">{user.rating}</span>
                  <span className="ml-1 text-secondary-600">/5.0</span>
                </div>
              )}

              {/* Quick Stats */}
              <div className="grid grid-cols-2 gap-4 pt-4 border-t border-secondary-100">
                <div>
                  <div className="text-lg font-bold text-primary-600">12</div>
                  <div className="text-xs text-secondary-500">
                    {user.role === 'client' ? 'Projects' : 'Completed'}
                  </div>
                </div>
                <div>
                  <div className="text-lg font-bold text-primary-600">98%</div>
                  <div className="text-xs text-secondary-500">Success Rate</div>
                </div>
              </div>
            </div>
          </div>

          {/* Profile Details */}
          <div className="lg:col-span-2">
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Basic Information */}
              <div className="card p-6">
                <h3 className="text-lg font-display font-semibold text-secondary-900 mb-6">
                  Basic Information
                </h3>
                
                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-semibold text-secondary-700 mb-2">
                      Full Name
                    </label>
                    {isEditing ? (
                      <input
                        type="text"
                        name="name"
                        value={formData.name}
                        onChange={handleInputChange}
                        className="input-field"
                        required
                      />
                    ) : (
                      <div className="flex items-center p-3 bg-secondary-50 rounded-lg">
                        <User className="w-5 h-5 text-secondary-400 mr-3" />
                        <span className="text-secondary-900">{user.name}</span>
                      </div>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-semibold text-secondary-700 mb-2">
                      Email Address
                    </label>
                    {isEditing ? (
                      <input
                        type="email"
                        name="email"
                        value={formData.email}
                        onChange={handleInputChange}
                        className="input-field"
                        required
                      />
                    ) : (
                      <div className="flex items-center p-3 bg-secondary-50 rounded-lg">
                        <Mail className="w-5 h-5 text-secondary-400 mr-3" />
                        <span className="text-secondary-900">{user.email}</span>
                      </div>
                    )}
                  </div>

                  {user.role === 'vendor' && (
                    <div>
                      <label className="block text-sm font-semibold text-secondary-700 mb-2">
                        Company Name
                      </label>
                      {isEditing ? (
                        <input
                          type="text"
                          name="company"
                          value={formData.company}
                          onChange={handleInputChange}
                          className="input-field"
                        />
                      ) : (
                        <div className="flex items-center p-3 bg-secondary-50 rounded-lg">
                          <Building className="w-5 h-5 text-secondary-400 mr-3" />
                          <span className="text-secondary-900">{user.company || 'Not specified'}</span>
                        </div>
                      )}
                    </div>
                  )}

                  <div>
                    <label className="block text-sm font-semibold text-secondary-700 mb-2">
                      Location
                    </label>
                    {isEditing ? (
                      <input
                        type="text"
                        name="location"
                        value={formData.location}
                        onChange={handleInputChange}
                        className="input-field"
                        placeholder="City, Country"
                      />
                    ) : (
                      <div className="flex items-center p-3 bg-secondary-50 rounded-lg">
                        <MapPin className="w-5 h-5 text-secondary-400 mr-3" />
                        <span className="text-secondary-900">{user.location || 'Not specified'}</span>
                      </div>
                    )}
                  </div>

                  <div className="md:col-span-2">
                    <label className="block text-sm font-semibold text-secondary-700 mb-2">
                      Website
                    </label>
                    {isEditing ? (
                      <input
                        type="url"
                        name="website"
                        value={formData.website}
                        onChange={handleInputChange}
                        className="input-field"
                        placeholder="https://yourwebsite.com"
                      />
                    ) : (
                      <div className="flex items-center p-3 bg-secondary-50 rounded-lg">
                        <Globe className="w-5 h-5 text-secondary-400 mr-3" />
                        {user.website ? (
                          <a 
                            href={user.website} 
                            target="_blank" 
                            rel="noopener noreferrer"
                            className="text-primary-600 hover:text-primary-700"
                          >
                            {user.website}
                          </a>
                        ) : (
                          <span className="text-secondary-900">Not specified</span>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Bio Section */}
              <div className="card p-6">
                <h3 className="text-lg font-display font-semibold text-secondary-900 mb-6">
                  About
                </h3>
                
                {isEditing ? (
                  <textarea
                    name="bio"
                    value={formData.bio}
                    onChange={handleInputChange}
                    rows={4}
                    className="input-field resize-none"
                    placeholder="Tell us about yourself, your experience, and what makes you unique..."
                  />
                ) : (
                  <div className="p-3 bg-secondary-50 rounded-lg">
                    <p className="text-secondary-900">
                      {user.bio || 'No bio provided yet.'}
                    </p>
                  </div>
                )}
              </div>

              {/* Skills Section */}
              <div className="card p-6">
                <h3 className="text-lg font-display font-semibold text-secondary-900 mb-6">
                  Skills & Expertise
                </h3>
                
                {isEditing && (
                  <div className="mb-4">
                    <div className="flex gap-2">
                      <input
                        type="text"
                        value={newSkill}
                        onChange={(e) => setNewSkill(e.target.value)}
                        onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), handleAddSkill())}
                        className="input-field flex-1"
                        placeholder="Add a skill..."
                      />
                      <button
                        type="button"
                        onClick={handleAddSkill}
                        className="btn-primary px-4"
                      >
                        <Plus className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                )}

                <div className="flex flex-wrap gap-2">
                  {formData.skills.map((skill, index) => (
                    <span
                      key={index}
                      className="inline-flex items-center px-3 py-1 bg-primary-100 text-primary-700 rounded-full text-sm"
                    >
                      {skill}
                      {isEditing && (
                        <button
                          type="button"
                          onClick={() => handleRemoveSkill(skill)}
                          className="ml-2 text-primary-500 hover:text-primary-700"
                        >
                          <X className="w-3 h-3" />
                        </button>
                      )}
                    </span>
                  ))}
                  {formData.skills.length === 0 && (
                    <p className="text-secondary-500">No skills added yet.</p>
                  )}
                </div>
              </div>

              {/* Action Buttons */}
              {isEditing && (
                <div className="flex justify-end space-x-4">
                  <button
                    type="button"
                    onClick={handleCancel}
                    className="btn-secondary"
                  >
                    <X className="w-5 h-5 mr-2" />
                    Cancel
                  </button>
                  <button
                    type="submit"
                    disabled={isLoading}
                    className="btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isLoading ? (
                      <div className="flex items-center">
                        <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                        Saving...
                      </div>
                    ) : (
                      <>
                        <Save className="w-5 h-5 mr-2" />
                        Save Changes
                      </>
                    )}
                  </button>
                </div>
              )}
            </form>
          </div>
        </div>
      </div>
    </div>
  );
}
