import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  ArrowLeft, 
  Calendar, 
  DollarSign, 
  FileText, 
  Tag, 
  Users, 
  Eye,
  Plus,
  X,
  Upload,
  Save
} from 'lucide-react';
import { projectService, CreateProjectData } from '../../services/projectService';

export default function CreateProject() {
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [formData, setFormData] = useState<CreateProjectData>({
    title: '',
    description: '',
    budget: 0,
    deadline: '',
    category: '',
    skills: [],
    attachments: [],
    visibility: 'public'
  });
  const [newSkill, setNewSkill] = useState('');

  const categories = projectService.getCategories();
  const commonSkills = projectService.getCommonSkills();

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: name === 'budget' ? parseFloat(value) || 0 : value
    }));
    if (error) setError('');
  };

  const handleAddSkill = (skill: string) => {
    if (skill && !formData.skills.includes(skill)) {
      setFormData(prev => ({
        ...prev,
        skills: [...prev.skills, skill]
      }));
    }
    setNewSkill('');
  };

  const handleRemoveSkill = (skillToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      skills: prev.skills.filter(skill => skill !== skillToRemove)
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validation
    if (!formData.title.trim()) {
      setError('Project title is required');
      return;
    }
    if (!formData.description.trim()) {
      setError('Project description is required');
      return;
    }
    if (formData.budget <= 0) {
      setError('Budget must be greater than 0');
      return;
    }
    if (!formData.deadline) {
      setError('Deadline is required');
      return;
    }
    if (!formData.category) {
      setError('Category is required');
      return;
    }
    if (formData.skills.length === 0) {
      setError('At least one skill is required');
      return;
    }

    // Check if deadline is in the future
    const deadlineDate = new Date(formData.deadline);
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    if (deadlineDate <= today) {
      setError('Deadline must be in the future');
      return;
    }

    try {
      setIsLoading(true);
      await projectService.createProject(formData);
      navigate('/projects');
    } catch (error: any) {
      setError(error.message);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    navigate('/projects');
  };

  return (
    <div className="min-h-screen bg-secondary-50">
      <div className="container-custom py-8">
        {/* Header */}
        <div className="flex items-center mb-8">
          <button
            onClick={handleCancel}
            className="btn-secondary mr-4"
          >
            <ArrowLeft className="w-5 h-5 mr-2" />
            Back
          </button>
          <div>
            <h1 className="text-3xl font-display font-bold text-secondary-900">Create New Project</h1>
            <p className="text-secondary-600">Post your project and connect with talented vendors</p>
          </div>
        </div>

        {/* Error Message */}
        {error && (
          <div className="mb-6 p-4 bg-error-50 border border-error-200 rounded-lg">
            <p className="text-error-700">{error}</p>
          </div>
        )}

        <form onSubmit={handleSubmit} className="space-y-8">
          <div className="grid lg:grid-cols-3 gap-8">
            {/* Main Form */}
            <div className="lg:col-span-2 space-y-6">
              {/* Basic Information */}
              <div className="card p-6">
                <h2 className="text-xl font-semibold text-secondary-900 mb-6">Basic Information</h2>
                
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-secondary-700 mb-2">
                      Project Title *
                    </label>
                    <input
                      type="text"
                      name="title"
                      value={formData.title}
                      onChange={handleInputChange}
                      placeholder="Enter a clear, descriptive title for your project"
                      className="input-field"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-secondary-700 mb-2">
                      Project Description *
                    </label>
                    <textarea
                      name="description"
                      value={formData.description}
                      onChange={handleInputChange}
                      rows={6}
                      placeholder="Describe your project in detail. Include requirements, expectations, and any specific instructions..."
                      className="input-field resize-none"
                      required
                    />
                  </div>

                  <div className="grid md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-secondary-700 mb-2">
                        Budget (USD) *
                      </label>
                      <div className="relative">
                        <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-secondary-400" />
                        <input
                          type="number"
                          name="budget"
                          value={formData.budget || ''}
                          onChange={handleInputChange}
                          placeholder="5000"
                          min="1"
                          className="input-field pl-10"
                          required
                        />
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-secondary-700 mb-2">
                        Deadline *
                      </label>
                      <div className="relative">
                        <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-secondary-400" />
                        <input
                          type="date"
                          name="deadline"
                          value={formData.deadline}
                          onChange={handleInputChange}
                          min={new Date().toISOString().split('T')[0]}
                          className="input-field pl-10"
                          required
                        />
                      </div>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-secondary-700 mb-2">
                      Category *
                    </label>
                    <div className="relative">
                      <Tag className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-secondary-400" />
                      <select
                        name="category"
                        value={formData.category}
                        onChange={handleInputChange}
                        className="input-field pl-10"
                        required
                      >
                        <option value="">Select a category</option>
                        {categories.map(category => (
                          <option key={category.value} value={category.value}>
                            {category.label}
                          </option>
                        ))}
                      </select>
                    </div>
                  </div>
                </div>
              </div>

              {/* Skills Required */}
              <div className="card p-6">
                <h2 className="text-xl font-semibold text-secondary-900 mb-6">Required Skills</h2>
                
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-secondary-700 mb-2">
                      Add Skills *
                    </label>
                    <div className="flex gap-2">
                      <input
                        type="text"
                        value={newSkill}
                        onChange={(e) => setNewSkill(e.target.value)}
                        onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), handleAddSkill(newSkill))}
                        placeholder="Type a skill and press Enter"
                        className="input-field flex-1"
                      />
                      <button
                        type="button"
                        onClick={() => handleAddSkill(newSkill)}
                        className="btn-primary px-4"
                      >
                        <Plus className="w-4 h-4" />
                      </button>
                    </div>
                  </div>

                  {/* Common Skills */}
                  <div>
                    <p className="text-sm text-secondary-600 mb-2">Popular skills:</p>
                    <div className="flex flex-wrap gap-2">
                      {commonSkills.slice(0, 12).map((skill) => (
                        <button
                          key={skill}
                          type="button"
                          onClick={() => handleAddSkill(skill)}
                          disabled={formData.skills.includes(skill)}
                          className="px-3 py-1 text-sm border border-secondary-300 rounded-full hover:bg-primary-50 hover:border-primary-300 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                          {skill}
                        </button>
                      ))}
                    </div>
                  </div>

                  {/* Selected Skills */}
                  {formData.skills.length > 0 && (
                    <div>
                      <p className="text-sm font-medium text-secondary-700 mb-2">Selected skills:</p>
                      <div className="flex flex-wrap gap-2">
                        {formData.skills.map((skill, index) => (
                          <span
                            key={index}
                            className="inline-flex items-center px-3 py-1 bg-primary-100 text-primary-700 rounded-full text-sm"
                          >
                            {skill}
                            <button
                              type="button"
                              onClick={() => handleRemoveSkill(skill)}
                              className="ml-2 text-primary-500 hover:text-primary-700"
                            >
                              <X className="w-3 h-3" />
                            </button>
                          </span>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* File Attachments */}
              <div className="card p-6">
                <h2 className="text-xl font-semibold text-secondary-900 mb-6">Attachments (Optional)</h2>
                
                <div className="border-2 border-dashed border-secondary-300 rounded-lg p-8 text-center">
                  <Upload className="w-12 h-12 text-secondary-400 mx-auto mb-4" />
                  <p className="text-secondary-600 mb-2">Drag and drop files here, or click to browse</p>
                  <p className="text-sm text-secondary-500">Supported formats: PDF, DOC, DOCX, TXT, ZIP (Max 10MB)</p>
                  <button type="button" className="btn-secondary mt-4">
                    Choose Files
                  </button>
                </div>
              </div>
            </div>

            {/* Sidebar */}
            <div className="lg:col-span-1">
              <div className="card p-6 sticky top-8">
                <h3 className="text-lg font-semibold text-secondary-900 mb-4">Project Settings</h3>
                
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-secondary-700 mb-2">
                      Visibility
                    </label>
                    <div className="space-y-2">
                      <label className="flex items-center">
                        <input
                          type="radio"
                          name="visibility"
                          value="public"
                          checked={formData.visibility === 'public'}
                          onChange={handleInputChange}
                          className="w-4 h-4 text-primary-600 border-secondary-300 focus:ring-primary-500"
                        />
                        <div className="ml-3">
                          <p className="text-sm font-medium text-secondary-900">Public</p>
                          <p className="text-xs text-secondary-600">Anyone can view and bid</p>
                        </div>
                      </label>
                      <label className="flex items-center">
                        <input
                          type="radio"
                          name="visibility"
                          value="invite-only"
                          checked={formData.visibility === 'invite-only'}
                          onChange={handleInputChange}
                          className="w-4 h-4 text-primary-600 border-secondary-300 focus:ring-primary-500"
                        />
                        <div className="ml-3">
                          <p className="text-sm font-medium text-secondary-900">Invite Only</p>
                          <p className="text-xs text-secondary-600">Only invited vendors can bid</p>
                        </div>
                      </label>
                      <label className="flex items-center">
                        <input
                          type="radio"
                          name="visibility"
                          value="private"
                          checked={formData.visibility === 'private'}
                          onChange={handleInputChange}
                          className="w-4 h-4 text-primary-600 border-secondary-300 focus:ring-primary-500"
                        />
                        <div className="ml-3">
                          <p className="text-sm font-medium text-secondary-900">Private</p>
                          <p className="text-xs text-secondary-600">Only you can view</p>
                        </div>
                      </label>
                    </div>
                  </div>

                  {/* Project Preview */}
                  <div className="pt-4 border-t border-secondary-200">
                    <h4 className="text-sm font-medium text-secondary-700 mb-2">Preview</h4>
                    <div className="p-3 bg-secondary-50 rounded-lg">
                      <h5 className="font-medium text-secondary-900 text-sm mb-1">
                        {formData.title || 'Project Title'}
                      </h5>
                      <p className="text-xs text-secondary-600 mb-2">
                        {formData.description ? 
                          formData.description.substring(0, 100) + (formData.description.length > 100 ? '...' : '') :
                          'Project description will appear here...'
                        }
                      </p>
                      <div className="flex items-center justify-between text-xs text-secondary-500">
                        <span>{formData.budget ? projectService.formatBudget(formData.budget) : '$0'}</span>
                        <span>{formData.deadline ? projectService.formatDeadline(formData.deadline) : 'No deadline'}</span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="mt-6 space-y-3">
                  <button
                    type="submit"
                    disabled={isLoading}
                    className="btn-primary w-full disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isLoading ? (
                      <div className="flex items-center justify-center">
                        <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                        Creating...
                      </div>
                    ) : (
                      <>
                        <Save className="w-5 h-5 mr-2" />
                        Create Project
                      </>
                    )}
                  </button>
                  <button
                    type="button"
                    onClick={handleCancel}
                    className="btn-secondary w-full"
                  >
                    Cancel
                  </button>
                </div>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  );
}
