import React from 'react';
import { 
  Calendar, 
  DollarSign, 
  Users, 
  Clock, 
  MapPin, 
  Star,
  Eye,
  MessageCircle,
  Bookmark,
  MoreHorizontal
} from 'lucide-react';

interface Project {
  id: string;
  title: string;
  description: string;
  budget: number;
  deadline: string;
  status: 'open' | 'in-progress' | 'review' | 'completed' | 'cancelled';
  category: string;
  skills: string[];
  client: {
    name: string;
    avatar?: string;
    rating: number;
    location: string;
  };
  bidsCount: number;
  viewsCount: number;
  postedAt: string;
  isBookmarked?: boolean;
}

interface ProjectCardProps {
  project: Project;
  userRole: 'client' | 'vendor' | 'admin';
  onViewDetails: (projectId: string) => void;
  onBid?: (projectId: string) => void;
  onBookmark?: (projectId: string) => void;
}

export default function ProjectCard({ 
  project, 
  userRole, 
  onViewDetails, 
  onBid, 
  onBookmark 
}: ProjectCardProps) {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'open': return 'bg-success-100 text-success-700';
      case 'in-progress': return 'bg-warning-100 text-warning-700';
      case 'review': return 'bg-primary-100 text-primary-700';
      case 'completed': return 'bg-secondary-100 text-secondary-700';
      case 'cancelled': return 'bg-error-100 text-error-700';
      default: return 'bg-secondary-100 text-secondary-700';
    }
  };

  const getDaysLeft = (deadline: string) => {
    const today = new Date();
    const deadlineDate = new Date(deadline);
    const diffTime = deadlineDate.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  const daysLeft = getDaysLeft(project.deadline);

  return (
    <div className="card p-6 hover:shadow-green transition-all duration-300 group">
      {/* Header */}
      <div className="flex items-start justify-between mb-4">
        <div className="flex-1">
          <div className="flex items-center space-x-2 mb-2">
            <span className={`px-2 py-1 rounded-full text-xs font-semibold ${getStatusColor(project.status)}`}>
              {project.status.replace('-', ' ')}
            </span>
            <span className="px-2 py-1 bg-secondary-100 text-secondary-700 rounded-full text-xs font-semibold">
              {project.category}
            </span>
          </div>
          <h3 className="text-lg font-display font-semibold text-secondary-900 mb-2 group-hover:text-primary-600 transition-colors duration-200">
            {project.title}
          </h3>
          <p className="text-secondary-600 text-sm line-clamp-2 leading-relaxed">
            {project.description}
          </p>
        </div>
        
        <div className="flex items-center space-x-2 ml-4">
          {userRole === 'vendor' && (
            <button 
              onClick={() => onBookmark?.(project.id)}
              className={`p-2 rounded-lg transition-colors duration-200 ${
                project.isBookmarked 
                  ? 'text-warning-600 bg-warning-50' 
                  : 'text-secondary-400 hover:text-warning-600 hover:bg-warning-50'
              }`}
            >
              <Bookmark className="w-4 h-4" />
            </button>
          )}
          <button className="p-2 text-secondary-400 hover:text-secondary-600 rounded-lg hover:bg-secondary-50 transition-colors duration-200">
            <MoreHorizontal className="w-4 h-4" />
          </button>
        </div>
      </div>

      {/* Skills */}
      <div className="flex flex-wrap gap-2 mb-4">
        {project.skills.slice(0, 4).map((skill, index) => (
          <span 
            key={index}
            className="px-2 py-1 bg-primary-50 text-primary-600 rounded text-xs font-medium"
          >
            {skill}
          </span>
        ))}
        {project.skills.length > 4 && (
          <span className="px-2 py-1 bg-secondary-100 text-secondary-600 rounded text-xs font-medium">
            +{project.skills.length - 4} more
          </span>
        )}
      </div>

      {/* Project Details */}
      <div className="grid grid-cols-2 gap-4 mb-4">
        <div className="flex items-center space-x-2 text-sm text-secondary-600">
          <DollarSign className="w-4 h-4" />
          <span className="font-semibold text-secondary-900">${project.budget.toLocaleString()}</span>
        </div>
        <div className="flex items-center space-x-2 text-sm text-secondary-600">
          <Calendar className="w-4 h-4" />
          <span className={`font-semibold ${
            daysLeft <= 3 ? 'text-error-600' : 
            daysLeft <= 7 ? 'text-warning-600' : 
            'text-secondary-900'
          }`}>
            {daysLeft > 0 ? `${daysLeft} days left` : 'Overdue'}
          </span>
        </div>
        <div className="flex items-center space-x-2 text-sm text-secondary-600">
          <Users className="w-4 h-4" />
          <span>{project.bidsCount} bids</span>
        </div>
        <div className="flex items-center space-x-2 text-sm text-secondary-600">
          <Eye className="w-4 h-4" />
          <span>{project.viewsCount} views</span>
        </div>
      </div>

      {/* Client Info */}
      <div className="flex items-center justify-between pt-4 border-t border-secondary-100">
        <div className="flex items-center space-x-3">
          {project.client.avatar ? (
            <img 
              src={project.client.avatar} 
              alt={project.client.name}
              className="w-8 h-8 rounded-full"
            />
          ) : (
            <div className="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">
              <span className="text-primary-600 font-semibold text-sm">
                {project.client.name.charAt(0)}
              </span>
            </div>
          )}
          <div>
            <p className="font-medium text-secondary-900 text-sm">{project.client.name}</p>
            <div className="flex items-center space-x-2 text-xs text-secondary-500">
              <div className="flex items-center space-x-1">
                <Star className="w-3 h-3 fill-current text-warning-400" />
                <span>{project.client.rating}</span>
              </div>
              <span>•</span>
              <div className="flex items-center space-x-1">
                <MapPin className="w-3 h-3" />
                <span>{project.client.location}</span>
              </div>
            </div>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <button 
            onClick={() => onViewDetails(project.id)}
            className="btn-secondary text-sm py-2 px-3"
          >
            View Details
          </button>
          {userRole === 'vendor' && project.status === 'open' && (
            <button 
              onClick={() => onBid?.(project.id)}
              className="btn-primary text-sm py-2 px-3"
            >
              Place Bid
            </button>
          )}
        </div>
      </div>

      {/* Posted Time */}
      <div className="mt-3 text-xs text-secondary-400">
        Posted {project.postedAt}
      </div>
    </div>
  );
}
