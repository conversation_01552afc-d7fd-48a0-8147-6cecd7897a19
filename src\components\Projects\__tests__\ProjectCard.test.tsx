import { describe, it, expect, vi, beforeEach } from 'vitest'
import { screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { renderWithProviders, mockProject, mockUser, mockVendorUser } from '../../../test/utils'
import ProjectCard from '../ProjectCard'

// Mock the project service
vi.mock('../../../services/projectService', () => ({
  projectService: {
    formatBudget: vi.fn((amount: number) => `$${amount.toLocaleString()}`),
    formatDeadline: vi.fn((date: string) => '2 weeks left'),
    getCategoryIcon: vi.fn(() => '💻'),
    getCategoryColor: vi.fn(() => 'bg-blue-100 text-blue-700')
  }
}))

describe('ProjectCard', () => {
  const defaultProps = {
    project: mockProject,
    user: mockUser,
    onBidClick: vi.fn(),
    onViewDetails: vi.fn()
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('rendering', () => {
    it('should render project information correctly', () => {
      renderWithProviders(<ProjectCard {...defaultProps} />)

      expect(screen.getByText(mockProject.title)).toBeInTheDocument()
      expect(screen.getByText(mockProject.description)).toBeInTheDocument()
      expect(screen.getByText('$5,000')).toBeInTheDocument()
      expect(screen.getByText('2 weeks left')).toBeInTheDocument()
    })

    it('should display project category with icon', () => {
      renderWithProviders(<ProjectCard {...defaultProps} />)

      expect(screen.getByText('💻')).toBeInTheDocument()
      expect(screen.getByText('Web Development')).toBeInTheDocument()
    })

    it('should show urgent badge for urgent projects', () => {
      const urgentProject = { ...mockProject, isUrgent: true }
      renderWithProviders(
        <ProjectCard {...defaultProps} project={urgentProject} />
      )

      expect(screen.getByText('Urgent')).toBeInTheDocument()
    })

    it('should display client information', () => {
      renderWithProviders(<ProjectCard {...defaultProps} />)

      expect(screen.getByText(mockProject.client.name)).toBeInTheDocument()
    })

    it('should show requirements list', () => {
      renderWithProviders(<ProjectCard {...defaultProps} />)

      mockProject.requirements.forEach(requirement => {
        expect(screen.getByText(requirement)).toBeInTheDocument()
      })
    })
  })

  describe('user interactions', () => {
    it('should call onViewDetails when view details button is clicked', async () => {
      const user = userEvent.setup()
      renderWithProviders(<ProjectCard {...defaultProps} />)

      const viewButton = screen.getByText('View Details')
      await user.click(viewButton)

      expect(defaultProps.onViewDetails).toHaveBeenCalledWith(mockProject._id)
    })

    it('should call onBidClick when bid button is clicked by vendor', async () => {
      const user = userEvent.setup()
      renderWithProviders(
        <ProjectCard {...defaultProps} user={mockVendorUser} />
      )

      const bidButton = screen.getByText('Submit Bid')
      await user.click(bidButton)

      expect(defaultProps.onBidClick).toHaveBeenCalledWith(mockProject._id)
    })

    it('should not show bid button for project owner', () => {
      renderWithProviders(<ProjectCard {...defaultProps} />)

      expect(screen.queryByText('Submit Bid')).not.toBeInTheDocument()
    })

    it('should show different status for assigned projects', () => {
      const assignedProject = {
        ...mockProject,
        status: 'in-progress' as const,
        assignedVendor: mockVendorUser
      }
      
      renderWithProviders(
        <ProjectCard {...defaultProps} project={assignedProject} />
      )

      expect(screen.getByText('In Progress')).toBeInTheDocument()
      expect(screen.queryByText('Submit Bid')).not.toBeInTheDocument()
    })
  })

  describe('project status display', () => {
    it('should show open status for new projects', () => {
      renderWithProviders(<ProjectCard {...defaultProps} />)

      expect(screen.getByText('Open')).toBeInTheDocument()
    })

    it('should show completed status with appropriate styling', () => {
      const completedProject = { ...mockProject, status: 'completed' as const }
      renderWithProviders(
        <ProjectCard {...defaultProps} project={completedProject} />
      )

      expect(screen.getByText('Completed')).toBeInTheDocument()
    })

    it('should show cancelled status', () => {
      const cancelledProject = { ...mockProject, status: 'cancelled' as const }
      renderWithProviders(
        <ProjectCard {...defaultProps} project={cancelledProject} />
      )

      expect(screen.getByText('Cancelled')).toBeInTheDocument()
    })
  })

  describe('responsive behavior', () => {
    it('should handle long project titles gracefully', () => {
      const longTitleProject = {
        ...mockProject,
        title: 'This is a very long project title that should be truncated properly'
      }
      
      renderWithProviders(
        <ProjectCard {...defaultProps} project={longTitleProject} />
      )

      expect(screen.getByText(longTitleProject.title)).toBeInTheDocument()
    })

    it('should handle long descriptions', () => {
      const longDescProject = {
        ...mockProject,
        description: 'This is a very long project description that should be handled properly and truncated if necessary to maintain good UI layout'
      }
      
      renderWithProviders(
        <ProjectCard {...defaultProps} project={longDescProject} />
      )

      expect(screen.getByText(longDescProject.description)).toBeInTheDocument()
    })
  })

  describe('accessibility', () => {
    it('should have proper ARIA labels', () => {
      renderWithProviders(<ProjectCard {...defaultProps} />)

      const card = screen.getByRole('article')
      expect(card).toBeInTheDocument()
    })

    it('should have keyboard navigation support', async () => {
      const user = userEvent.setup()
      renderWithProviders(<ProjectCard {...defaultProps} />)

      const viewButton = screen.getByText('View Details')
      
      // Tab to the button
      await user.tab()
      expect(viewButton).toHaveFocus()

      // Press Enter
      await user.keyboard('{Enter}')
      expect(defaultProps.onViewDetails).toHaveBeenCalled()
    })

    it('should have proper heading hierarchy', () => {
      renderWithProviders(<ProjectCard {...defaultProps} />)

      const heading = screen.getByRole('heading', { level: 3 })
      expect(heading).toHaveTextContent(mockProject.title)
    })
  })

  describe('edge cases', () => {
    it('should handle missing client information', () => {
      const projectWithoutClient = {
        ...mockProject,
        client: null as any
      }
      
      renderWithProviders(
        <ProjectCard {...defaultProps} project={projectWithoutClient} />
      )

      expect(screen.getByText('Unknown Client')).toBeInTheDocument()
    })

    it('should handle empty requirements array', () => {
      const projectWithoutRequirements = {
        ...mockProject,
        requirements: []
      }
      
      renderWithProviders(
        <ProjectCard {...defaultProps} project={projectWithoutRequirements} />
      )

      expect(screen.queryByText('Requirements:')).not.toBeInTheDocument()
    })

    it('should handle zero budget', () => {
      const zeroBudgetProject = {
        ...mockProject,
        budget: 0
      }
      
      renderWithProviders(
        <ProjectCard {...defaultProps} project={zeroBudgetProject} />
      )

      expect(screen.getByText('$0')).toBeInTheDocument()
    })
  })

  describe('conditional rendering based on user role', () => {
    it('should show edit button for project owner', () => {
      renderWithProviders(<ProjectCard {...defaultProps} />)

      expect(screen.getByLabelText('Edit project')).toBeInTheDocument()
    })

    it('should not show edit button for non-owners', () => {
      renderWithProviders(
        <ProjectCard {...defaultProps} user={mockVendorUser} />
      )

      expect(screen.queryByLabelText('Edit project')).not.toBeInTheDocument()
    })

    it('should show appropriate actions for vendors', () => {
      renderWithProviders(
        <ProjectCard {...defaultProps} user={mockVendorUser} />
      )

      expect(screen.getByText('Submit Bid')).toBeInTheDocument()
      expect(screen.getByText('View Details')).toBeInTheDocument()
    })
  })
})
