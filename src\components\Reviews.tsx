import React from 'react';
import { Star, Quote } from 'lucide-react';

const reviews = [
  {
    name: "<PERSON>",
    company: "TechFlow Solutions",
    role: "Project Manager",
    image: "https://images.unsplash.com/photo-1494790108377-be9c29b29330?auto=format&fit=crop&w=150&q=80",
    content: "GlobalConnect has revolutionized how we manage international projects. The platform's intuitive interface and powerful collaboration tools have increased our project success rate by 45%.",
    rating: 5,
    project: "E-commerce Platform Development"
  },
  {
    name: "<PERSON>",
    company: "Digital Innovations Ltd",
    role: "Senior Developer",
    image: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?auto=format&fit=crop&w=150&q=80",
    content: "As a vendor, I've found amazing opportunities through this platform. The secure payment system and transparent bidding process make it my go-to choice for finding quality projects.",
    rating: 5,
    project: "Mobile App Development"
  },
  {
    name: "<PERSON>",
    company: "Creative Studios Inc",
    role: "Design Director",
    image: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?auto=format&fit=crop&w=150&q=80",
    content: "The real-time collaboration features and document security are outstanding. We've completed over 50 projects through GlobalConnect with zero security incidents.",
    rating: 5,
    project: "Brand Identity Design"
  },
  {
    name: "David Kim",
    company: "StartupBoost",
    role: "CEO",
    image: "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?auto=format&fit=crop&w=150&q=80",
    content: "The analytics dashboard provides incredible insights into our project performance. We've optimized our processes and reduced project delivery time by 30%.",
    rating: 5,
    project: "Business Automation"
  },
  {
    name: "Lisa Wang",
    company: "Global Marketing Pro",
    role: "Marketing Manager",
    image: "https://images.unsplash.com/photo-1534528741775-53994a69daeb?auto=format&fit=crop&w=150&q=80",
    content: "Finding reliable vendors was always a challenge until we discovered GlobalConnect. The verification system and rating system ensure we work with top-quality professionals.",
    rating: 5,
    project: "Digital Marketing Campaign"
  },
  {
    name: "James Thompson",
    company: "InnovateTech",
    role: "CTO",
    image: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?auto=format&fit=crop&w=150&q=80",
    content: "The platform's AI-powered project matching has connected us with perfect vendors every time. It's like having a personal project manager that never sleeps.",
    rating: 5,
    project: "AI Integration Project"
  }
];

export default function Reviews() {
  return (
    <section className="section-padding bg-white relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-20 left-10 w-32 h-32 bg-primary-200 rounded-full"></div>
        <div className="absolute bottom-20 right-10 w-24 h-24 bg-success-200 rounded-full"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-40 h-40 bg-primary-100 rounded-full"></div>
      </div>

      <div className="container-custom relative z-10">
        {/* Header */}
        <div className="text-center mb-16 animate-fade-in-up">
          <span className="inline-block px-4 py-2 bg-success-100 text-success-700 rounded-full text-sm font-semibold mb-4">
            ⭐ Customer Success Stories
          </span>
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-display font-bold text-secondary-900 mb-6">
            Trusted by <span className="gradient-text">Thousands</span> Worldwide
          </h2>
          <p className="text-lg text-secondary-600 max-w-3xl mx-auto">
            Join a community of successful businesses who have transformed their operations
            and achieved remarkable results with GlobalConnect.
          </p>
        </div>

        {/* Reviews Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
          {reviews.slice(0, 6).map((review, index) => (
            <div
              key={index}
              className="card p-8 relative group animate-fade-in-up hover:shadow-green"
              style={{ animationDelay: `${index * 100}ms` }}
            >
              {/* Quote Icon */}
              <div className="absolute top-6 right-6 opacity-10 group-hover:opacity-20 transition-opacity duration-300">
                <Quote className="w-8 h-8 text-primary-600" />
              </div>

              {/* Rating */}
              <div className="flex mb-4">
                {[...Array(review.rating)].map((_, i) => (
                  <Star key={i} className="w-5 h-5 fill-current text-warning-400" />
                ))}
              </div>

              {/* Content */}
              <blockquote className="text-secondary-700 mb-6 leading-relaxed italic">
                "{review.content}"
              </blockquote>

              {/* Project Tag */}
              <div className="mb-4">
                <span className="px-3 py-1 bg-primary-50 text-primary-600 rounded-full text-xs font-semibold">
                  {review.project}
                </span>
              </div>

              {/* Author */}
              <div className="flex items-center">
                <img
                  src={review.image}
                  alt={review.name}
                  className="w-12 h-12 rounded-full mr-4 ring-2 ring-primary-100"
                />
                <div>
                  <h4 className="font-semibold text-secondary-900">{review.name}</h4>
                  <p className="text-sm text-secondary-500">{review.role}</p>
                  <p className="text-sm text-primary-600 font-medium">{review.company}</p>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Stats Section */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-8 py-12 border-t border-secondary-100 animate-fade-in-up animation-delay-600">
          <div className="text-center">
            <div className="text-3xl lg:text-4xl font-bold text-primary-600 mb-2">50K+</div>
            <div className="text-secondary-600">Happy Clients</div>
          </div>
          <div className="text-center">
            <div className="text-3xl lg:text-4xl font-bold text-success-600 mb-2">98%</div>
            <div className="text-secondary-600">Success Rate</div>
          </div>
          <div className="text-center">
            <div className="text-3xl lg:text-4xl font-bold text-warning-600 mb-2">4.9/5</div>
            <div className="text-secondary-600">Average Rating</div>
          </div>
          <div className="text-center">
            <div className="text-3xl lg:text-4xl font-bold text-error-600 mb-2">24/7</div>
            <div className="text-secondary-600">Support</div>
          </div>
        </div>

        {/* CTA */}
        <div className="text-center animate-fade-in-up animation-delay-800">
          <button className="btn-primary">
            Join Our Success Stories
          </button>
        </div>
      </div>
    </section>
  );
}