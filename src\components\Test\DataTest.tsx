import React, { useState, useEffect } from 'react';
import { projectService } from '../../services/projectService';
import { authService } from '../../services/authService';

export default function DataTest() {
  const [projects, setProjects] = useState<any[]>([]);
  const [users, setUsers] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [loginTest, setLoginTest] = useState<any>(null);

  useEffect(() => {
    testDataLoading();
  }, []);

  const testDataLoading = async () => {
    try {
      setLoading(true);
      
      // Test project loading
      console.log('Testing project service...');
      const projectsResponse = await projectService.getProjects({ limit: 5 });
      console.log('Projects response:', projectsResponse);
      setProjects(projectsResponse.projects);
      
      console.log('Data test completed successfully');
    } catch (err) {
      console.error('Error in data test:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  const testLogin = async () => {
    try {
      console.log('Testing login...');
      const result = await authService.login({
        email: '<EMAIL>',
        password: 'client123'
      });
      console.log('Login result:', result);
      setLoginTest(result);
    } catch (err) {
      console.error('Login test failed:', err);
      setLoginTest({ error: err instanceof Error ? err.message : 'Login failed' });
    }
  };

  if (loading) {
    return (
      <div className="p-8">
        <h1 className="text-2xl font-bold mb-4">Data Connection Test</h1>
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2 mb-4"></div>
          <div className="h-4 bg-gray-200 rounded w-1/3 mb-4"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-8 max-w-4xl mx-auto">
      <h1 className="text-3xl font-bold mb-8">Backend Data Connection Test</h1>
      
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
          <h3 className="text-red-800 font-semibold">Error</h3>
          <p className="text-red-700">{error}</p>
        </div>
      )}

      {/* Login Test */}
      <div className="bg-white rounded-lg shadow p-6 mb-6">
        <h2 className="text-xl font-semibold mb-4">Authentication Test</h2>
        <button 
          onClick={testLogin}
          className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 mb-4"
        >
          Test Login (<EMAIL>)
        </button>
        
        {loginTest && (
          <div className="mt-4 p-4 bg-gray-50 rounded">
            <h3 className="font-semibold mb-2">Login Test Result:</h3>
            <pre className="text-sm overflow-auto">
              {JSON.stringify(loginTest, null, 2)}
            </pre>
          </div>
        )}
      </div>

      {/* Projects Test */}
      <div className="bg-white rounded-lg shadow p-6 mb-6">
        <h2 className="text-xl font-semibold mb-4">Projects Data ({projects.length} loaded)</h2>
        
        {projects.length > 0 ? (
          <div className="space-y-4">
            {projects.map((project) => (
              <div key={project._id} className="border border-gray-200 rounded p-4">
                <h3 className="font-semibold text-lg">{project.title}</h3>
                <p className="text-gray-600 mb-2">{project.description.substring(0, 150)}...</p>
                <div className="flex flex-wrap gap-4 text-sm text-gray-500">
                  <span>Budget: ${project.budget?.toLocaleString()}</span>
                  <span>Status: {project.status}</span>
                  <span>Category: {project.category}</span>
                  <span>Client: {project.client?.name}</span>
                  <span>Bids: {project.bids?.length || 0}</span>
                </div>
                {project.assignedVendor && (
                  <div className="mt-2 text-sm text-blue-600">
                    Assigned to: {project.assignedVendor.name}
                  </div>
                )}
              </div>
            ))}
          </div>
        ) : (
          <p className="text-gray-500">No projects loaded</p>
        )}
      </div>

      {/* Raw Data Display */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-xl font-semibold mb-4">Raw Data (First Project)</h2>
        {projects.length > 0 && (
          <pre className="text-xs overflow-auto bg-gray-50 p-4 rounded">
            {JSON.stringify(projects[0], null, 2)}
          </pre>
        )}
      </div>

      {/* Connection Status */}
      <div className="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg">
        <h3 className="text-green-800 font-semibold">✅ Backend Connection Status</h3>
        <ul className="text-green-700 mt-2 space-y-1">
          <li>• MongoDB: Connected</li>
          <li>• API Server: Running on port 5000</li>
          <li>• Projects API: {projects.length > 0 ? 'Working' : 'No data'}</li>
          <li>• Seeded Data: {projects.length > 0 ? 'Loaded successfully' : 'Not found'}</li>
        </ul>
      </div>
    </div>
  );
}
