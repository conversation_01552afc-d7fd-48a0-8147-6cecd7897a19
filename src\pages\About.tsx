import React from 'react';
import { Globe, Users, Target, Award, TrendingUp, Heart, Shield, Zap } from 'lucide-react';
import Footer from '../components/Footer';

const stats = [
  { number: '50K+', label: 'Active Users', icon: Users },
  { number: '25K+', label: 'Projects Completed', icon: Target },
  { number: '180+', label: 'Countries', icon: Globe },
  { number: '99.9%', label: 'Uptime', icon: Shield }
];

const team = [
  {
    name: '<PERSON>',
    role: 'CEO & Founder',
    image: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?auto=format&fit=crop&w=400&q=80',
    bio: 'Former VP at Microsoft with 15+ years in global project management.'
  },
  {
    name: '<PERSON>',
    role: 'CTO',
    image: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?auto=format&fit=crop&w=400&q=80',
    bio: 'Ex-Google engineer specializing in distributed systems and AI.'
  },
  {
    name: '<PERSON>',
    role: 'Head of Product',
    image: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?auto=format&fit=crop&w=400&q=80',
    bio: 'Product strategist with expertise in user experience and market research.'
  },
  {
    name: 'David <PERSON>',
    role: 'Head of Security',
    image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?auto=format&fit=crop&w=400&q=80',
    bio: 'Cybersecurity expert with background in enterprise security solutions.'
  }
];

const values = [
  {
    icon: Globe,
    title: 'Global Connection',
    description: 'Breaking down barriers to connect talent and opportunities worldwide.'
  },
  {
    icon: Heart,
    title: 'Trust & Transparency',
    description: 'Building lasting relationships through honest communication and fair practices.'
  },
  {
    icon: Zap,
    title: 'Innovation',
    description: 'Continuously evolving our platform with cutting-edge technology.'
  },
  {
    icon: Award,
    title: 'Excellence',
    description: 'Committed to delivering exceptional results for every project.'
  }
];

export default function About() {
  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="gradient-bg py-20">
        <div className="container-custom">
          <div className="text-center max-w-4xl mx-auto">
            <h1 className="text-4xl sm:text-5xl lg:text-6xl font-display font-bold text-secondary-900 mb-6">
              Connecting the World's <span className="gradient-text">Best Talent</span>
            </h1>
            <p className="text-xl text-secondary-600 mb-8 leading-relaxed">
              GlobalConnect was founded with a simple mission: to break down geographical barriers 
              and create meaningful connections between businesses and talented professionals worldwide.
            </p>
            <div className="flex flex-wrap justify-center gap-4">
              <button className="btn-primary">Join Our Mission</button>
              <button className="btn-secondary">Our Story</button>
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-white">
        <div className="container-custom">
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <div key={index} className="text-center">
                <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <stat.icon className="w-8 h-8 text-primary-600" />
                </div>
                <div className="text-3xl font-bold text-secondary-900 mb-2">{stat.number}</div>
                <div className="text-secondary-600">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Story Section */}
      <section className="py-20 bg-secondary-50">
        <div className="container-custom">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl sm:text-4xl font-display font-bold text-secondary-900 mb-6">
                Our Story
              </h2>
              <div className="space-y-4 text-secondary-600 leading-relaxed">
                <p>
                  Founded in 2020 by a team of experienced entrepreneurs and technologists, 
                  GlobalConnect emerged from a simple observation: the world's best talent 
                  and most exciting opportunities were often separated by geography.
                </p>
                <p>
                  We started with a vision to create a platform that would not just connect 
                  people, but truly understand their needs, protect their interests, and 
                  facilitate meaningful, long-term professional relationships.
                </p>
                <p>
                  Today, we're proud to serve over 50,000 active users across 180+ countries, 
                  facilitating billions of dollars in project value while maintaining our 
                  commitment to security, transparency, and innovation.
                </p>
              </div>
            </div>
            <div className="relative">
              <img
                src="https://images.unsplash.com/photo-1522071820081-009f0129c71c?auto=format&fit=crop&w=800&q=80"
                alt="Team collaboration"
                className="rounded-2xl shadow-xl"
              />
              <div className="absolute -bottom-6 -right-6 w-32 h-32 bg-primary-600 rounded-2xl flex items-center justify-center">
                <TrendingUp className="w-16 h-16 text-white" />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Values Section */}
      <section className="py-20 bg-white">
        <div className="container-custom">
          <div className="text-center mb-16">
            <h2 className="text-3xl sm:text-4xl font-display font-bold text-secondary-900 mb-6">
              Our Values
            </h2>
            <p className="text-xl text-secondary-600 max-w-3xl mx-auto">
              These core principles guide everything we do and shape the culture we're building.
            </p>
          </div>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {values.map((value, index) => (
              <div key={index} className="text-center">
                <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-6">
                  <value.icon className="w-8 h-8 text-primary-600" />
                </div>
                <h3 className="text-xl font-semibold text-secondary-900 mb-4">{value.title}</h3>
                <p className="text-secondary-600 leading-relaxed">{value.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Team Section */}
      <section className="py-20 bg-secondary-50">
        <div className="container-custom">
          <div className="text-center mb-16">
            <h2 className="text-3xl sm:text-4xl font-display font-bold text-secondary-900 mb-6">
              Meet Our Team
            </h2>
            <p className="text-xl text-secondary-600 max-w-3xl mx-auto">
              Passionate professionals dedicated to revolutionizing global collaboration.
            </p>
          </div>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {team.map((member, index) => (
              <div key={index} className="card text-center">
                <img
                  src={member.image}
                  alt={member.name}
                  className="w-24 h-24 rounded-full mx-auto mb-4 object-cover"
                />
                <h3 className="text-xl font-semibold text-secondary-900 mb-2">{member.name}</h3>
                <p className="text-primary-600 font-medium mb-4">{member.role}</p>
                <p className="text-secondary-600 text-sm leading-relaxed">{member.bio}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-primary-600">
        <div className="container-custom text-center">
          <h2 className="text-3xl sm:text-4xl font-display font-bold text-white mb-6">
            Ready to Join Our Global Community?
          </h2>
          <p className="text-xl text-primary-100 mb-8 max-w-2xl mx-auto">
            Whether you're looking to hire top talent or showcase your skills, 
            GlobalConnect is your gateway to unlimited opportunities.
          </p>
          <div className="flex flex-wrap justify-center gap-4">
            <button className="bg-white text-primary-600 px-8 py-3 rounded-lg font-semibold hover:bg-primary-50 transition-colors duration-200">
              Get Started Today
            </button>
            <button className="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-primary-600 transition-colors duration-200">
              Contact Us
            </button>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
}
