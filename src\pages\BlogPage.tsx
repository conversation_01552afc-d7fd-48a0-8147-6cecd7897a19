import React, { useState } from 'react';
import { Calendar, Clock, User, Tag, Search, TrendingUp, BookOpen, Filter } from 'lucide-react';
import Footer from '../components/Footer';

const categories = [
  'All Posts',
  'Project Management',
  'Technology',
  'Business Tips',
  'Success Stories',
  'Industry News',
  'Remote Work'
];

const blogPosts = [
  {
    id: 1,
    title: 'The Future of Remote Project Management',
    excerpt: 'Discover how modern tools and methodologies are reshaping the way distributed teams collaborate on complex projects worldwide.',
    content: 'Full article content would go here...',
    author: '<PERSON>',
    authorImage: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?auto=format&fit=crop&w=400&q=80',
    date: '2024-01-15',
    readTime: '8 min read',
    category: 'Project Management',
    image: 'https://images.unsplash.com/photo-1552664730-d307ca884978?auto=format&fit=crop&w=800&q=80',
    tags: ['Remote Work', 'Project Management', 'Technology'],
    featured: true
  },
  {
    id: 2,
    title: 'AI-Powered Talent Matching: A Game Changer',
    excerpt: 'How artificial intelligence is revolutionizing the way businesses find and connect with the right talent for their projects.',
    content: 'Full article content would go here...',
    author: '<PERSON> Chen',
    authorImage: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?auto=format&fit=crop&w=400&q=80',
    date: '2024-01-12',
    readTime: '6 min read',
    category: 'Technology',
    image: 'https://images.unsplash.com/photo-1485827404703-89b55fcc595e?auto=format&fit=crop&w=800&q=80',
    tags: ['AI', 'Technology', 'Talent Matching'],
    featured: false
  },
  {
    id: 3,
    title: 'Building Trust in Global Business Relationships',
    excerpt: 'Essential strategies for establishing and maintaining trust when working with international partners and remote teams.',
    content: 'Full article content would go here...',
    author: 'Emily Rodriguez',
    authorImage: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?auto=format&fit=crop&w=400&q=80',
    date: '2024-01-10',
    readTime: '7 min read',
    category: 'Business Tips',
    image: 'https://images.unsplash.com/photo-1521737604893-d14cc237f11d?auto=format&fit=crop&w=800&q=80',
    tags: ['Business', 'Trust', 'Global'],
    featured: false
  },
  {
    id: 4,
    title: 'Success Story: From Startup to Global Enterprise',
    excerpt: 'How TechCorp scaled from a 5-person startup to a global enterprise using GlobalConnect to manage distributed teams.',
    content: 'Full article content would go here...',
    author: 'David Kim',
    authorImage: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?auto=format&fit=crop&w=400&q=80',
    date: '2024-01-08',
    readTime: '10 min read',
    category: 'Success Stories',
    image: 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?auto=format&fit=crop&w=800&q=80',
    tags: ['Success Story', 'Startup', 'Growth'],
    featured: false
  },
  {
    id: 5,
    title: 'Security Best Practices for Remote Collaboration',
    excerpt: 'Comprehensive guide to keeping your projects and data secure when working with distributed teams and external partners.',
    content: 'Full article content would go here...',
    author: 'Lisa Wang',
    authorImage: 'https://images.unsplash.com/photo-1517841905240-472988babdf9?auto=format&fit=crop&w=400&q=80',
    date: '2024-01-05',
    readTime: '9 min read',
    category: 'Technology',
    image: 'https://images.unsplash.com/photo-1563013544-824ae1b704d3?auto=format&fit=crop&w=800&q=80',
    tags: ['Security', 'Remote Work', 'Best Practices'],
    featured: false
  },
  {
    id: 6,
    title: 'The Economics of Global Freelancing',
    excerpt: 'Understanding market trends, pricing strategies, and economic factors that shape the global freelancing landscape.',
    content: 'Full article content would go here...',
    author: 'Robert Taylor',
    authorImage: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?auto=format&fit=crop&w=400&q=80',
    date: '2024-01-03',
    readTime: '12 min read',
    category: 'Industry News',
    image: 'https://images.unsplash.com/photo-1551288049-bebda4e38f71?auto=format&fit=crop&w=800&q=80',
    tags: ['Economics', 'Freelancing', 'Market Trends'],
    featured: false
  }
];

export default function BlogPage() {
  const [selectedCategory, setSelectedCategory] = useState('All Posts');
  const [searchQuery, setSearchQuery] = useState('');

  const filteredPosts = blogPosts.filter(post => {
    const matchesCategory = selectedCategory === 'All Posts' || post.category === selectedCategory;
    const matchesSearch = post.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         post.excerpt.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         post.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
    return matchesCategory && matchesSearch;
  });

  const featuredPost = blogPosts.find(post => post.featured);
  const regularPosts = filteredPosts.filter(post => !post.featured);

  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="gradient-bg py-20">
        <div className="container-custom text-center">
          <div className="w-20 h-20 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <BookOpen className="w-10 h-10 text-primary-600" />
          </div>
          <h1 className="text-4xl sm:text-5xl lg:text-6xl font-display font-bold text-secondary-900 mb-6">
            GlobalConnect <span className="gradient-text">Blog</span>
          </h1>
          <p className="text-xl text-secondary-600 mb-8 max-w-3xl mx-auto">
            Insights, tips, and stories from the world of global collaboration and project management.
          </p>
          
          {/* Search Bar */}
          <div className="max-w-2xl mx-auto relative">
            <div className="relative">
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 w-6 h-6 text-secondary-400" />
              <input
                type="text"
                placeholder="Search articles..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-12 pr-4 py-4 text-lg border border-secondary-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Categories Filter */}
      <section className="py-8 bg-white border-b border-secondary-100">
        <div className="container-custom">
          <div className="flex flex-wrap justify-center gap-4">
            {categories.map((category) => (
              <button
                key={category}
                onClick={() => setSelectedCategory(category)}
                className={`px-6 py-2 rounded-full font-medium transition-colors duration-200 ${
                  selectedCategory === category
                    ? 'bg-primary-600 text-white'
                    : 'bg-secondary-100 text-secondary-600 hover:bg-secondary-200'
                }`}
              >
                {category}
              </button>
            ))}
          </div>
        </div>
      </section>

      {/* Featured Post */}
      {featuredPost && selectedCategory === 'All Posts' && (
        <section className="py-16 bg-secondary-50">
          <div className="container-custom">
            <div className="flex items-center mb-8">
              <TrendingUp className="w-6 h-6 text-primary-600 mr-2" />
              <span className="text-lg font-semibold text-primary-600">Featured Article</span>
            </div>
            
            <div className="card overflow-hidden">
              <div className="grid lg:grid-cols-2 gap-8">
                <div className="relative">
                  <img
                    src={featuredPost.image}
                    alt={featuredPost.title}
                    className="w-full h-64 lg:h-full object-cover"
                  />
                  <div className="absolute top-4 left-4">
                    <span className="px-3 py-1 bg-primary-600 text-white rounded-full text-sm font-semibold">
                      Featured
                    </span>
                  </div>
                </div>
                
                <div className="p-6 lg:p-8">
                  <div className="flex items-center space-x-4 text-sm text-secondary-500 mb-4">
                    <span className="px-3 py-1 bg-secondary-100 rounded-full">{featuredPost.category}</span>
                    <div className="flex items-center">
                      <Calendar className="w-4 h-4 mr-1" />
                      {new Date(featuredPost.date).toLocaleDateString()}
                    </div>
                    <div className="flex items-center">
                      <Clock className="w-4 h-4 mr-1" />
                      {featuredPost.readTime}
                    </div>
                  </div>
                  
                  <h2 className="text-3xl font-display font-bold text-secondary-900 mb-4">
                    {featuredPost.title}
                  </h2>
                  
                  <p className="text-secondary-600 mb-6 leading-relaxed">
                    {featuredPost.excerpt}
                  </p>
                  
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <img
                        src={featuredPost.authorImage}
                        alt={featuredPost.author}
                        className="w-10 h-10 rounded-full mr-3"
                      />
                      <div>
                        <p className="font-medium text-secondary-900">{featuredPost.author}</p>
                      </div>
                    </div>
                    
                    <button className="btn-primary">Read Article</button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
      )}

      {/* Blog Posts Grid */}
      <section className="py-20 bg-white">
        <div className="container-custom">
          <div className="flex items-center justify-between mb-12">
            <h2 className="text-3xl font-display font-bold text-secondary-900">
              {selectedCategory === 'All Posts' ? 'Latest Articles' : selectedCategory}
            </h2>
            <div className="flex items-center space-x-4">
              <span className="text-secondary-600">{regularPosts.length} articles</span>
              <button className="flex items-center space-x-2 text-secondary-600 hover:text-primary-600 transition-colors duration-200">
                <Filter className="w-4 h-4" />
                <span>Sort</span>
              </button>
            </div>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {regularPosts.map((post) => (
              <article key={post.id} className="card overflow-hidden hover:shadow-lg transition-shadow duration-300 group">
                <div className="relative">
                  <img
                    src={post.image}
                    alt={post.title}
                    className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                  <div className="absolute top-4 left-4">
                    <span className="px-3 py-1 bg-white/90 backdrop-blur-sm text-primary-600 rounded-full text-xs font-semibold">
                      {post.category}
                    </span>
                  </div>
                </div>
                
                <div className="p-6">
                  <div className="flex items-center space-x-4 text-sm text-secondary-500 mb-3">
                    <div className="flex items-center">
                      <Calendar className="w-4 h-4 mr-1" />
                      {new Date(post.date).toLocaleDateString()}
                    </div>
                    <div className="flex items-center">
                      <Clock className="w-4 h-4 mr-1" />
                      {post.readTime}
                    </div>
                  </div>
                  
                  <h3 className="text-xl font-display font-semibold text-secondary-900 mb-3 group-hover:text-primary-600 transition-colors duration-300">
                    {post.title}
                  </h3>
                  
                  <p className="text-secondary-600 mb-4 leading-relaxed">
                    {post.excerpt}
                  </p>
                  
                  <div className="flex flex-wrap gap-2 mb-4">
                    {post.tags.slice(0, 2).map((tag, index) => (
                      <span key={index} className="px-2 py-1 bg-secondary-100 text-secondary-600 rounded text-xs">
                        {tag}
                      </span>
                    ))}
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <img
                        src={post.authorImage}
                        alt={post.author}
                        className="w-8 h-8 rounded-full mr-2"
                      />
                      <span className="text-sm text-secondary-600">{post.author}</span>
                    </div>
                    
                    <button className="text-primary-600 hover:text-primary-700 font-medium text-sm">
                      Read More
                    </button>
                  </div>
                </div>
              </article>
            ))}
          </div>
          
          {regularPosts.length === 0 && (
            <div className="text-center py-12">
              <p className="text-secondary-600 text-lg">No articles found matching your criteria.</p>
            </div>
          )}
          
          {regularPosts.length > 0 && (
            <div className="text-center mt-12">
              <button className="btn-primary">Load More Articles</button>
            </div>
          )}
        </div>
      </section>

      {/* Newsletter Signup */}
      <section className="py-20 bg-primary-600">
        <div className="container-custom text-center">
          <h2 className="text-3xl sm:text-4xl font-display font-bold text-white mb-6">
            Stay Updated
          </h2>
          <p className="text-xl text-primary-100 mb-8 max-w-2xl mx-auto">
            Subscribe to our newsletter and never miss the latest insights, tips, and industry news.
          </p>
          <div className="max-w-md mx-auto flex gap-4">
            <input
              type="email"
              placeholder="Enter your email"
              className="flex-1 px-4 py-3 rounded-lg border-0 focus:outline-none focus:ring-2 focus:ring-primary-300"
            />
            <button className="bg-white text-primary-600 px-6 py-3 rounded-lg font-semibold hover:bg-primary-50 transition-colors duration-200">
              Subscribe
            </button>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
}
