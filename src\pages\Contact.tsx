import React, { useState } from 'react';
import { Mail, Phone, MapPin, Clock, Send, MessageCircle, Users, Headphones } from 'lucide-react';
import Footer from '../components/Footer';

const contactMethods = [
  {
    icon: Mail,
    title: 'Email Support',
    description: 'Get help via email within 24 hours',
    contact: '<EMAIL>',
    action: 'Send Email'
  },
  {
    icon: Phone,
    title: 'Phone Support',
    description: 'Speak with our team directly',
    contact: '+****************',
    action: 'Call Now'
  },
  {
    icon: MessageCircle,
    title: 'Live Chat',
    description: 'Chat with us in real-time',
    contact: 'Available 24/7',
    action: 'Start Chat'
  },
  {
    icon: Headphones,
    title: 'Help Center',
    description: 'Browse our knowledge base',
    contact: 'Self-service support',
    action: 'Visit Help Center'
  }
];

const offices = [
  {
    city: 'San Francisco',
    address: '123 Tech Street, Suite 100',
    zipcode: 'San Francisco, CA 94105',
    phone: '+****************',
    email: '<EMAIL>'
  },
  {
    city: 'New York',
    address: '456 Business Ave, Floor 25',
    zipcode: 'New York, NY 10001',
    phone: '+****************',
    email: '<EMAIL>'
  },
  {
    city: 'London',
    address: '789 Innovation Road',
    zipcode: 'London, UK EC1A 1BB',
    phone: '+44 20 7123 4567',
    email: '<EMAIL>'
  }
];

export default function Contact() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    company: '',
    subject: '',
    message: '',
    type: 'general'
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitted, setSubmitted] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    // Simulate form submission
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    setIsSubmitting(false);
    setSubmitted(true);
    
    // Reset form after 3 seconds
    setTimeout(() => {
      setSubmitted(false);
      setFormData({
        name: '',
        email: '',
        company: '',
        subject: '',
        message: '',
        type: 'general'
      });
    }, 3000);
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="gradient-bg py-20">
        <div className="container-custom text-center">
          <h1 className="text-4xl sm:text-5xl lg:text-6xl font-display font-bold text-secondary-900 mb-6">
            Get in <span className="gradient-text">Touch</span>
          </h1>
          <p className="text-xl text-secondary-600 mb-8 max-w-3xl mx-auto">
            Have questions? We'd love to hear from you. Send us a message and we'll respond as soon as possible.
          </p>
        </div>
      </section>

      {/* Contact Methods */}
      <section className="py-20 bg-white">
        <div className="container-custom">
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
            {contactMethods.map((method, index) => (
              <div key={index} className="card text-center hover:shadow-lg transition-shadow duration-300">
                <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-6">
                  <method.icon className="w-8 h-8 text-primary-600" />
                </div>
                <h3 className="text-xl font-semibold text-secondary-900 mb-3">{method.title}</h3>
                <p className="text-secondary-600 mb-4">{method.description}</p>
                <p className="text-primary-600 font-medium mb-4">{method.contact}</p>
                <button className="btn-secondary text-sm">{method.action}</button>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Contact Form & Info */}
      <section className="py-20 bg-secondary-50">
        <div className="container-custom">
          <div className="grid lg:grid-cols-2 gap-16">
            {/* Contact Form */}
            <div>
              <h2 className="text-3xl font-display font-bold text-secondary-900 mb-6">
                Send us a Message
              </h2>
              <p className="text-secondary-600 mb-8">
                Fill out the form below and we'll get back to you within 24 hours.
              </p>
              
              {submitted ? (
                <div className="card bg-success-50 border-success-200">
                  <div className="text-center py-8">
                    <div className="w-16 h-16 bg-success-100 rounded-full flex items-center justify-center mx-auto mb-4">
                      <Send className="w-8 h-8 text-success-600" />
                    </div>
                    <h3 className="text-xl font-semibold text-success-900 mb-2">Message Sent!</h3>
                    <p className="text-success-700">
                      Thank you for contacting us. We'll get back to you soon.
                    </p>
                  </div>
                </div>
              ) : (
                <form onSubmit={handleSubmit} className="card">
                  <div className="grid md:grid-cols-2 gap-6 mb-6">
                    <div>
                      <label htmlFor="name" className="block text-sm font-semibold text-secondary-700 mb-2">
                        Full Name *
                      </label>
                      <input
                        type="text"
                        id="name"
                        name="name"
                        value={formData.name}
                        onChange={handleChange}
                        required
                        className="input-field"
                        placeholder="Your full name"
                      />
                    </div>
                    <div>
                      <label htmlFor="email" className="block text-sm font-semibold text-secondary-700 mb-2">
                        Email Address *
                      </label>
                      <input
                        type="email"
                        id="email"
                        name="email"
                        value={formData.email}
                        onChange={handleChange}
                        required
                        className="input-field"
                        placeholder="<EMAIL>"
                      />
                    </div>
                  </div>
                  
                  <div className="grid md:grid-cols-2 gap-6 mb-6">
                    <div>
                      <label htmlFor="company" className="block text-sm font-semibold text-secondary-700 mb-2">
                        Company
                      </label>
                      <input
                        type="text"
                        id="company"
                        name="company"
                        value={formData.company}
                        onChange={handleChange}
                        className="input-field"
                        placeholder="Your company name"
                      />
                    </div>
                    <div>
                      <label htmlFor="type" className="block text-sm font-semibold text-secondary-700 mb-2">
                        Inquiry Type
                      </label>
                      <select
                        id="type"
                        name="type"
                        value={formData.type}
                        onChange={handleChange}
                        className="input-field"
                      >
                        <option value="general">General Inquiry</option>
                        <option value="support">Technical Support</option>
                        <option value="sales">Sales Question</option>
                        <option value="partnership">Partnership</option>
                        <option value="press">Press Inquiry</option>
                      </select>
                    </div>
                  </div>
                  
                  <div className="mb-6">
                    <label htmlFor="subject" className="block text-sm font-semibold text-secondary-700 mb-2">
                      Subject *
                    </label>
                    <input
                      type="text"
                      id="subject"
                      name="subject"
                      value={formData.subject}
                      onChange={handleChange}
                      required
                      className="input-field"
                      placeholder="Brief description of your inquiry"
                    />
                  </div>
                  
                  <div className="mb-6">
                    <label htmlFor="message" className="block text-sm font-semibold text-secondary-700 mb-2">
                      Message *
                    </label>
                    <textarea
                      id="message"
                      name="message"
                      value={formData.message}
                      onChange={handleChange}
                      required
                      rows={6}
                      className="input-field resize-none"
                      placeholder="Tell us more about your inquiry..."
                    />
                  </div>
                  
                  <button
                    type="submit"
                    disabled={isSubmitting}
                    className="btn-primary w-full disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isSubmitting ? (
                      <span className="flex items-center justify-center">
                        <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                        Sending...
                      </span>
                    ) : (
                      <span className="flex items-center justify-center">
                        <Send className="w-5 h-5 mr-2" />
                        Send Message
                      </span>
                    )}
                  </button>
                </form>
              )}
            </div>

            {/* Contact Info */}
            <div>
              <h2 className="text-3xl font-display font-bold text-secondary-900 mb-6">
                Our Offices
              </h2>
              <p className="text-secondary-600 mb-8">
                Visit us at one of our global locations or reach out through any of our contact channels.
              </p>
              
              <div className="space-y-8">
                {offices.map((office, index) => (
                  <div key={index} className="card">
                    <h3 className="text-xl font-semibold text-secondary-900 mb-4">{office.city}</h3>
                    <div className="space-y-3">
                      <div className="flex items-start">
                        <MapPin className="w-5 h-5 text-primary-600 mr-3 mt-0.5 flex-shrink-0" />
                        <div>
                          <p className="text-secondary-600">{office.address}</p>
                          <p className="text-secondary-600">{office.zipcode}</p>
                        </div>
                      </div>
                      <div className="flex items-center">
                        <Phone className="w-5 h-5 text-primary-600 mr-3 flex-shrink-0" />
                        <p className="text-secondary-600">{office.phone}</p>
                      </div>
                      <div className="flex items-center">
                        <Mail className="w-5 h-5 text-primary-600 mr-3 flex-shrink-0" />
                        <p className="text-secondary-600">{office.email}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
              
              {/* Business Hours */}
              <div className="card mt-8">
                <h3 className="text-xl font-semibold text-secondary-900 mb-4 flex items-center">
                  <Clock className="w-6 h-6 text-primary-600 mr-3" />
                  Business Hours
                </h3>
                <div className="space-y-2 text-secondary-600">
                  <div className="flex justify-between">
                    <span>Monday - Friday</span>
                    <span>9:00 AM - 6:00 PM</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Saturday</span>
                    <span>10:00 AM - 4:00 PM</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Sunday</span>
                    <span>Closed</span>
                  </div>
                  <div className="mt-4 pt-4 border-t border-secondary-200">
                    <p className="text-sm">
                      <strong>Emergency Support:</strong> Available 24/7 for Enterprise customers
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Quick Links */}
      <section className="py-20 bg-white">
        <div className="container-custom text-center">
          <h2 className="text-3xl font-display font-bold text-secondary-900 mb-6">
            Need Quick Answers?
          </h2>
          <p className="text-xl text-secondary-600 mb-8 max-w-2xl mx-auto">
            Check out our help center for instant answers to common questions.
          </p>
          <div className="flex flex-wrap justify-center gap-4">
            <button className="btn-primary">Visit Help Center</button>
            <button className="btn-secondary">View Documentation</button>
            <button className="btn-secondary">Community Forum</button>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
}
