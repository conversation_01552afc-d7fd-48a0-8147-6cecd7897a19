import React, { useState } from 'react';
import { Search, Book, MessageCircle, Video, FileText, Users, Settings, CreditCard, Shield, ChevronRight, Star } from 'lucide-react';
import Footer from '../components/Footer';

const categories = [
  {
    icon: Users,
    title: 'Getting Started',
    description: 'Learn the basics of using GlobalConnect',
    articles: 12,
    color: 'primary'
  },
  {
    icon: FileText,
    title: 'Project Management',
    description: 'Create and manage your projects effectively',
    articles: 18,
    color: 'success'
  },
  {
    icon: CreditCard,
    title: 'Payments & Billing',
    description: 'Understanding payments, fees, and billing',
    articles: 8,
    color: 'warning'
  },
  {
    icon: Shield,
    title: 'Security & Privacy',
    description: 'Keep your account and data secure',
    articles: 6,
    color: 'error'
  },
  {
    icon: Settings,
    title: 'Account Settings',
    description: 'Manage your profile and preferences',
    articles: 10,
    color: 'secondary'
  },
  {
    icon: MessageCircle,
    title: 'Communication',
    description: 'Messaging, notifications, and collaboration',
    articles: 7,
    color: 'primary'
  }
];

const popularArticles = [
  {
    title: 'How to create your first project',
    category: 'Getting Started',
    views: '15.2k',
    rating: 4.8
  },
  {
    title: 'Understanding the bidding process',
    category: 'Project Management',
    views: '12.8k',
    rating: 4.9
  },
  {
    title: 'Payment methods and security',
    category: 'Payments & Billing',
    views: '9.5k',
    rating: 4.7
  },
  {
    title: 'Setting up two-factor authentication',
    category: 'Security & Privacy',
    views: '8.1k',
    rating: 4.6
  },
  {
    title: 'Managing your profile and portfolio',
    category: 'Account Settings',
    views: '7.3k',
    rating: 4.8
  }
];

const faqs = [
  {
    question: 'How do I get started on GlobalConnect?',
    answer: 'Simply sign up for a free account, complete your profile, and start browsing projects or posting your own. Our onboarding guide will walk you through each step.'
  },
  {
    question: 'What fees does GlobalConnect charge?',
    answer: 'We charge a small service fee on successful project completions. Clients pay 3% and vendors pay 5%. There are no upfront costs or subscription fees for basic accounts.'
  },
  {
    question: 'How are payments processed?',
    answer: 'All payments are processed securely through our escrow system. Funds are held safely until project milestones are completed and approved by both parties.'
  },
  {
    question: 'What if I have a dispute with another user?',
    answer: 'We offer a comprehensive dispute resolution system with mediation services. Our support team will help resolve any issues fairly and quickly.'
  },
  {
    question: 'Is my data secure on GlobalConnect?',
    answer: 'Yes, we use bank-level encryption and advanced security measures to protect your data. We also offer steganography features for sensitive documents.'
  }
];

export default function HelpCenter() {
  const [searchQuery, setSearchQuery] = useState('');
  const [openFaq, setOpenFaq] = useState<number | null>(null);

  const getColorClasses = (color: string) => {
    const colors = {
      primary: 'bg-primary-100 text-primary-600',
      success: 'bg-success-100 text-success-600',
      warning: 'bg-warning-100 text-warning-600',
      error: 'bg-error-100 text-error-600',
      secondary: 'bg-secondary-100 text-secondary-600'
    };
    return colors[color as keyof typeof colors] || colors.primary;
  };

  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="gradient-bg py-20">
        <div className="container-custom text-center">
          <div className="w-20 h-20 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <Book className="w-10 h-10 text-primary-600" />
          </div>
          <h1 className="text-4xl sm:text-5xl lg:text-6xl font-display font-bold text-secondary-900 mb-6">
            Help <span className="gradient-text">Center</span>
          </h1>
          <p className="text-xl text-secondary-600 mb-8 max-w-3xl mx-auto">
            Find answers to your questions and learn how to make the most of GlobalConnect.
          </p>
          
          {/* Search Bar */}
          <div className="max-w-2xl mx-auto relative">
            <div className="relative">
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 w-6 h-6 text-secondary-400" />
              <input
                type="text"
                placeholder="Search for help articles, guides, and FAQs..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-12 pr-4 py-4 text-lg border border-secondary-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Quick Actions */}
      <section className="py-16 bg-white">
        <div className="container-custom">
          <div className="grid md:grid-cols-3 gap-8 max-w-4xl mx-auto">
            <div className="card text-center hover:shadow-lg transition-shadow duration-300">
              <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <MessageCircle className="w-8 h-8 text-primary-600" />
              </div>
              <h3 className="text-xl font-semibold text-secondary-900 mb-2">Contact Support</h3>
              <p className="text-secondary-600 mb-4">Get help from our support team</p>
              <button className="btn-primary text-sm">Start Chat</button>
            </div>
            
            <div className="card text-center hover:shadow-lg transition-shadow duration-300">
              <div className="w-16 h-16 bg-success-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Video className="w-8 h-8 text-success-600" />
              </div>
              <h3 className="text-xl font-semibold text-secondary-900 mb-2">Video Tutorials</h3>
              <p className="text-secondary-600 mb-4">Watch step-by-step guides</p>
              <button className="btn-secondary text-sm">Watch Videos</button>
            </div>
            
            <div className="card text-center hover:shadow-lg transition-shadow duration-300">
              <div className="w-16 h-16 bg-warning-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Users className="w-8 h-8 text-warning-600" />
              </div>
              <h3 className="text-xl font-semibold text-secondary-900 mb-2">Community Forum</h3>
              <p className="text-secondary-600 mb-4">Connect with other users</p>
              <button className="btn-secondary text-sm">Join Forum</button>
            </div>
          </div>
        </div>
      </section>

      {/* Categories */}
      <section className="py-20 bg-secondary-50">
        <div className="container-custom">
          <div className="text-center mb-16">
            <h2 className="text-3xl sm:text-4xl font-display font-bold text-secondary-900 mb-6">
              Browse by Category
            </h2>
            <p className="text-xl text-secondary-600 max-w-3xl mx-auto">
              Find the information you need organized by topic.
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {categories.map((category, index) => (
              <div key={index} className="card hover:shadow-lg transition-shadow duration-300 group cursor-pointer">
                <div className="flex items-start justify-between mb-4">
                  <div className={`w-12 h-12 rounded-full flex items-center justify-center ${getColorClasses(category.color)}`}>
                    <category.icon className="w-6 h-6" />
                  </div>
                  <ChevronRight className="w-5 h-5 text-secondary-400 group-hover:text-primary-600 transition-colors duration-200" />
                </div>
                <h3 className="text-xl font-semibold text-secondary-900 mb-2 group-hover:text-primary-600 transition-colors duration-200">
                  {category.title}
                </h3>
                <p className="text-secondary-600 mb-4">{category.description}</p>
                <span className="text-sm text-secondary-500">{category.articles} articles</span>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Popular Articles */}
      <section className="py-20 bg-white">
        <div className="container-custom">
          <div className="text-center mb-16">
            <h2 className="text-3xl sm:text-4xl font-display font-bold text-secondary-900 mb-6">
              Popular Articles
            </h2>
            <p className="text-xl text-secondary-600 max-w-3xl mx-auto">
              Most viewed and highest rated help articles.
            </p>
          </div>
          
          <div className="max-w-4xl mx-auto space-y-4">
            {popularArticles.map((article, index) => (
              <div key={index} className="card hover:shadow-lg transition-shadow duration-300 group cursor-pointer">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold text-secondary-900 mb-2 group-hover:text-primary-600 transition-colors duration-200">
                      {article.title}
                    </h3>
                    <div className="flex items-center space-x-4 text-sm text-secondary-500">
                      <span className="px-2 py-1 bg-secondary-100 rounded-full">{article.category}</span>
                      <span>{article.views} views</span>
                      <div className="flex items-center">
                        <Star className="w-4 h-4 text-warning-400 fill-current mr-1" />
                        <span>{article.rating}</span>
                      </div>
                    </div>
                  </div>
                  <ChevronRight className="w-5 h-5 text-secondary-400 group-hover:text-primary-600 transition-colors duration-200" />
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-20 bg-secondary-50">
        <div className="container-custom">
          <div className="text-center mb-16">
            <h2 className="text-3xl sm:text-4xl font-display font-bold text-secondary-900 mb-6">
              Frequently Asked Questions
            </h2>
            <p className="text-xl text-secondary-600 max-w-3xl mx-auto">
              Quick answers to common questions.
            </p>
          </div>
          
          <div className="max-w-4xl mx-auto space-y-4">
            {faqs.map((faq, index) => (
              <div key={index} className="card">
                <button
                  onClick={() => setOpenFaq(openFaq === index ? null : index)}
                  className="w-full text-left p-6 flex justify-between items-center"
                >
                  <h3 className="text-lg font-semibold text-secondary-900">
                    {faq.question}
                  </h3>
                  <div
                    className={`transform transition-transform duration-200 ${
                      openFaq === index ? 'rotate-45' : ''
                    }`}
                  >
                    <div className="w-6 h-6 flex items-center justify-center">
                      <div className="w-4 h-0.5 bg-secondary-600"></div>
                      <div className="w-0.5 h-4 bg-secondary-600 absolute"></div>
                    </div>
                  </div>
                </button>
                {openFaq === index && (
                  <div className="px-6 pb-6">
                    <p className="text-secondary-600 leading-relaxed">
                      {faq.answer}
                    </p>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Contact Support */}
      <section className="py-20 bg-primary-600">
        <div className="container-custom text-center">
          <h2 className="text-3xl sm:text-4xl font-display font-bold text-white mb-6">
            Still Need Help?
          </h2>
          <p className="text-xl text-primary-100 mb-8 max-w-2xl mx-auto">
            Can't find what you're looking for? Our support team is here to help you 24/7.
          </p>
          <div className="flex flex-wrap justify-center gap-4">
            <button className="bg-white text-primary-600 px-8 py-3 rounded-lg font-semibold hover:bg-primary-50 transition-colors duration-200">
              Contact Support
            </button>
            <button className="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-primary-600 transition-colors duration-200">
              Schedule Call
            </button>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
}
