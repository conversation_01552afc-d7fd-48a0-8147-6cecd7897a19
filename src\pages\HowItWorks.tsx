import React from 'react';
import { UserPlus, Search, MessageCircle, CheckCircle, Star, Shield, Zap, Globe } from 'lucide-react';
import Footer from '../components/Footer';

const steps = [
  {
    number: '01',
    icon: UserPlus,
    title: 'Create Your Profile',
    description: 'Sign up and build a comprehensive profile showcasing your skills, experience, and portfolio.',
    details: [
      'Complete profile verification',
      'Upload portfolio samples',
      'Set your availability and rates',
      'Choose your specializations'
    ]
  },
  {
    number: '02',
    icon: Search,
    title: 'Find Perfect Matches',
    description: 'Browse projects or talent using our AI-powered matching system that understands your needs.',
    details: [
      'Smart project recommendations',
      'Advanced filtering options',
      'Real-time market insights',
      'Skill-based matching'
    ]
  },
  {
    number: '03',
    icon: MessageCircle,
    title: 'Connect & Collaborate',
    description: 'Communicate securely with potential partners and negotiate project terms.',
    details: [
      'Secure messaging system',
      'Video call integration',
      'File sharing capabilities',
      'Contract templates'
    ]
  },
  {
    number: '04',
    icon: CheckCircle,
    title: 'Execute & Deliver',
    description: 'Manage your projects with powerful tools and deliver exceptional results.',
    details: [
      'Project management dashboard',
      'Milestone tracking',
      'Secure payment processing',
      'Quality assurance tools'
    ]
  }
];

const userTypes = [
  {
    type: 'Clients',
    icon: Globe,
    title: 'For Businesses & Organizations',
    description: 'Find and hire the best talent for your projects',
    benefits: [
      'Access to global talent pool',
      'Vetted professionals',
      'Secure payment system',
      'Project management tools',
      'Quality guarantees'
    ],
    cta: 'Post a Project'
  },
  {
    type: 'Vendors',
    icon: Star,
    title: 'For Freelancers & Agencies',
    description: 'Showcase your skills and win exciting projects',
    benefits: [
      'High-quality project opportunities',
      'Fair and transparent bidding',
      'Secure payment protection',
      'Professional development tools',
      'Global client network'
    ],
    cta: 'Find Projects'
  }
];

const features = [
  {
    icon: Shield,
    title: 'Secure & Protected',
    description: 'Advanced encryption and steganography protect your sensitive data and intellectual property.'
  },
  {
    icon: Zap,
    title: 'AI-Powered Matching',
    description: 'Smart algorithms ensure perfect matches between projects and talent based on skills and requirements.'
  },
  {
    icon: CheckCircle,
    title: 'Quality Assurance',
    description: 'Built-in quality controls and review systems ensure exceptional results for every project.'
  }
];

export default function HowItWorks() {
  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="gradient-bg py-20">
        <div className="container-custom text-center">
          <h1 className="text-4xl sm:text-5xl lg:text-6xl font-display font-bold text-secondary-900 mb-6">
            How <span className="gradient-text">GlobalConnect</span> Works
          </h1>
          <p className="text-xl text-secondary-600 mb-8 max-w-3xl mx-auto">
            Discover how our platform connects businesses with top talent worldwide through 
            a simple, secure, and efficient process.
          </p>
          <div className="flex flex-wrap justify-center gap-4">
            <button className="btn-primary">Get Started Today</button>
            <button className="btn-secondary">Watch Demo</button>
          </div>
        </div>
      </section>

      {/* Process Steps */}
      <section className="py-20 bg-white">
        <div className="container-custom">
          <div className="text-center mb-16">
            <h2 className="text-3xl sm:text-4xl font-display font-bold text-secondary-900 mb-6">
              Simple 4-Step Process
            </h2>
            <p className="text-xl text-secondary-600 max-w-3xl mx-auto">
              From signup to project completion, we've streamlined every step to make 
              collaboration effortless and effective.
            </p>
          </div>

          <div className="space-y-16">
            {steps.map((step, index) => (
              <div key={index} className={`grid lg:grid-cols-2 gap-12 items-center ${
                index % 2 === 1 ? 'lg:grid-flow-col-dense' : ''
              }`}>
                {/* Content */}
                <div className={index % 2 === 1 ? 'lg:col-start-2' : ''}>
                  <div className="flex items-center mb-6">
                    <span className="text-6xl font-bold text-primary-100 mr-4">
                      {step.number}
                    </span>
                    <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center">
                      <step.icon className="w-8 h-8 text-primary-600" />
                    </div>
                  </div>
                  
                  <h3 className="text-3xl font-display font-bold text-secondary-900 mb-4">
                    {step.title}
                  </h3>
                  <p className="text-lg text-secondary-600 mb-6 leading-relaxed">
                    {step.description}
                  </p>
                  
                  <ul className="space-y-3">
                    {step.details.map((detail, detailIndex) => (
                      <li key={detailIndex} className="flex items-center">
                        <CheckCircle className="w-5 h-5 text-success-600 mr-3 flex-shrink-0" />
                        <span className="text-secondary-600">{detail}</span>
                      </li>
                    ))}
                  </ul>
                </div>

                {/* Visual */}
                <div className={`relative ${index % 2 === 1 ? 'lg:col-start-1' : ''}`}>
                  <div className="relative">
                    <div className="w-full h-80 bg-gradient-to-br from-primary-100 to-primary-200 rounded-2xl flex items-center justify-center">
                      <step.icon className="w-24 h-24 text-primary-600" />
                    </div>
                    
                    {/* Decorative elements */}
                    <div className="absolute -top-4 -right-4 w-16 h-16 bg-primary-600 rounded-full opacity-20"></div>
                    <div className="absolute -bottom-4 -left-4 w-12 h-12 bg-primary-400 rounded-full opacity-30"></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* User Types */}
      <section className="py-20 bg-secondary-50">
        <div className="container-custom">
          <div className="text-center mb-16">
            <h2 className="text-3xl sm:text-4xl font-display font-bold text-secondary-900 mb-6">
              Perfect for Everyone
            </h2>
            <p className="text-xl text-secondary-600 max-w-3xl mx-auto">
              Whether you're looking to hire talent or find your next project, 
              GlobalConnect has everything you need.
            </p>
          </div>

          <div className="grid lg:grid-cols-2 gap-8">
            {userTypes.map((userType, index) => (
              <div key={index} className="card">
                <div className="text-center mb-8">
                  <div className="w-20 h-20 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-6">
                    <userType.icon className="w-10 h-10 text-primary-600" />
                  </div>
                  <span className="inline-block px-4 py-2 bg-primary-100 text-primary-700 rounded-full text-sm font-semibold mb-4">
                    {userType.type}
                  </span>
                  <h3 className="text-2xl font-display font-bold text-secondary-900 mb-4">
                    {userType.title}
                  </h3>
                  <p className="text-secondary-600 mb-6">
                    {userType.description}
                  </p>
                </div>

                <ul className="space-y-4 mb-8">
                  {userType.benefits.map((benefit, benefitIndex) => (
                    <li key={benefitIndex} className="flex items-center">
                      <CheckCircle className="w-5 h-5 text-success-600 mr-3 flex-shrink-0" />
                      <span className="text-secondary-600">{benefit}</span>
                    </li>
                  ))}
                </ul>

                <button className="btn-primary w-full">
                  {userType.cta}
                </button>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Key Features */}
      <section className="py-20 bg-white">
        <div className="container-custom">
          <div className="text-center mb-16">
            <h2 className="text-3xl sm:text-4xl font-display font-bold text-secondary-900 mb-6">
              Why Choose GlobalConnect?
            </h2>
            <p className="text-xl text-secondary-600 max-w-3xl mx-auto">
              Our platform offers unique advantages that set us apart from traditional 
              freelancing and project management solutions.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <div key={index} className="text-center">
                <div className="w-20 h-20 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-6">
                  <feature.icon className="w-10 h-10 text-primary-600" />
                </div>
                <h3 className="text-xl font-semibold text-secondary-900 mb-4">
                  {feature.title}
                </h3>
                <p className="text-secondary-600 leading-relaxed">
                  {feature.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Success Stories Preview */}
      <section className="py-20 bg-secondary-50">
        <div className="container-custom">
          <div className="text-center mb-16">
            <h2 className="text-3xl sm:text-4xl font-display font-bold text-secondary-900 mb-6">
              Success Stories
            </h2>
            <p className="text-xl text-secondary-600 max-w-3xl mx-auto">
              See how businesses and professionals are achieving their goals with GlobalConnect.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            <div className="card text-center">
              <img
                src="https://images.unsplash.com/photo-1560250097-0b93528c311a?auto=format&fit=crop&w=400&q=80"
                alt="Success story"
                className="w-20 h-20 rounded-full mx-auto mb-6 object-cover"
              />
              <h3 className="text-xl font-semibold text-secondary-900 mb-4">
                TechCorp Inc.
              </h3>
              <p className="text-secondary-600 mb-4">
                "GlobalConnect helped us find the perfect development team for our mobile app. 
                The project was completed on time and exceeded our expectations."
              </p>
              <div className="flex justify-center">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="w-5 h-5 text-warning-400 fill-current" />
                ))}
              </div>
            </div>

            <div className="card text-center">
              <img
                src="https://images.unsplash.com/photo-1494790108755-2616b612b786?auto=format&fit=crop&w=400&q=80"
                alt="Success story"
                className="w-20 h-20 rounded-full mx-auto mb-6 object-cover"
              />
              <h3 className="text-xl font-semibold text-secondary-900 mb-4">
                Sarah Johnson
              </h3>
              <p className="text-secondary-600 mb-4">
                "As a freelance designer, GlobalConnect has connected me with amazing clients 
                worldwide. The platform's security features give me peace of mind."
              </p>
              <div className="flex justify-center">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="w-5 h-5 text-warning-400 fill-current" />
                ))}
              </div>
            </div>

            <div className="card text-center">
              <img
                src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?auto=format&fit=crop&w=400&q=80"
                alt="Success story"
                className="w-20 h-20 rounded-full mx-auto mb-6 object-cover"
              />
              <h3 className="text-xl font-semibold text-secondary-900 mb-4">
                Digital Agency Pro
              </h3>
              <p className="text-secondary-600 mb-4">
                "The AI-powered matching system is incredible. We've found clients that are 
                perfect fits for our services, leading to long-term partnerships."
              </p>
              <div className="flex justify-center">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="w-5 h-5 text-warning-400 fill-current" />
                ))}
              </div>
            </div>
          </div>

          <div className="text-center mt-12">
            <button className="btn-primary">View All Success Stories</button>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-primary-600">
        <div className="container-custom text-center">
          <h2 className="text-3xl sm:text-4xl font-display font-bold text-white mb-6">
            Ready to Get Started?
          </h2>
          <p className="text-xl text-primary-100 mb-8 max-w-2xl mx-auto">
            Join thousands of businesses and professionals already using GlobalConnect 
            to achieve their goals.
          </p>
          <div className="flex flex-wrap justify-center gap-4">
            <button className="bg-white text-primary-600 px-8 py-3 rounded-lg font-semibold hover:bg-primary-50 transition-colors duration-200">
              Sign Up Free
            </button>
            <button className="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-primary-600 transition-colors duration-200">
              Schedule Demo
            </button>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
}
