import React from 'react';
import { Shield, Eye, Lock, Users, FileText, Clock } from 'lucide-react';
import Footer from '../components/Footer';

const sections = [
  {
    title: 'Information We Collect',
    icon: FileText,
    content: [
      {
        subtitle: 'Personal Information',
        text: 'We collect information you provide directly to us, such as when you create an account, update your profile, or contact us for support.'
      },
      {
        subtitle: 'Usage Information',
        text: 'We automatically collect information about how you use our services, including your interactions with projects, messages, and platform features.'
      },
      {
        subtitle: 'Device Information',
        text: 'We collect information about the devices you use to access our services, including IP address, browser type, and operating system.'
      }
    ]
  },
  {
    title: 'How We Use Your Information',
    icon: Users,
    content: [
      {
        subtitle: 'Service Provision',
        text: 'We use your information to provide, maintain, and improve our services, including matching you with relevant projects or talent.'
      },
      {
        subtitle: 'Communication',
        text: 'We use your information to communicate with you about your account, projects, and important service updates.'
      },
      {
        subtitle: 'Security',
        text: 'We use your information to protect our platform and users from fraud, abuse, and other harmful activities.'
      }
    ]
  },
  {
    title: 'Information Sharing',
    icon: Eye,
    content: [
      {
        subtitle: 'With Other Users',
        text: 'We share certain profile information with other users to facilitate project matching and collaboration.'
      },
      {
        subtitle: 'Service Providers',
        text: 'We share information with trusted third-party service providers who help us operate our platform.'
      },
      {
        subtitle: 'Legal Requirements',
        text: 'We may share information when required by law or to protect our rights and the safety of our users.'
      }
    ]
  },
  {
    title: 'Data Security',
    icon: Lock,
    content: [
      {
        subtitle: 'Encryption',
        text: 'We use industry-standard encryption to protect your data both in transit and at rest.'
      },
      {
        subtitle: 'Access Controls',
        text: 'We implement strict access controls to ensure only authorized personnel can access your information.'
      },
      {
        subtitle: 'Regular Audits',
        text: 'We conduct regular security audits and assessments to identify and address potential vulnerabilities.'
      }
    ]
  }
];

export default function PrivacyPolicy() {
  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="gradient-bg py-20">
        <div className="container-custom text-center">
          <div className="w-20 h-20 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <Shield className="w-10 h-10 text-primary-600" />
          </div>
          <h1 className="text-4xl sm:text-5xl lg:text-6xl font-display font-bold text-secondary-900 mb-6">
            Privacy <span className="gradient-text">Policy</span>
          </h1>
          <p className="text-xl text-secondary-600 mb-8 max-w-3xl mx-auto">
            Your privacy is important to us. This policy explains how we collect, use, and protect your information.
          </p>
          <div className="flex items-center justify-center space-x-4 text-sm text-secondary-500">
            <div className="flex items-center">
              <Clock className="w-4 h-4 mr-2" />
              Last updated: January 15, 2024
            </div>
          </div>
        </div>
      </section>

      {/* Quick Overview */}
      <section className="py-16 bg-white">
        <div className="container-custom max-w-4xl mx-auto">
          <div className="card bg-primary-50 border-primary-200">
            <h2 className="text-2xl font-display font-bold text-secondary-900 mb-4">
              Privacy at a Glance
            </h2>
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <h3 className="font-semibold text-secondary-900 mb-2">What we collect:</h3>
                <ul className="text-secondary-600 space-y-1">
                  <li>• Account and profile information</li>
                  <li>• Project and communication data</li>
                  <li>• Usage and device information</li>
                </ul>
              </div>
              <div>
                <h3 className="font-semibold text-secondary-900 mb-2">How we protect it:</h3>
                <ul className="text-secondary-600 space-y-1">
                  <li>• End-to-end encryption</li>
                  <li>• Strict access controls</li>
                  <li>• Regular security audits</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <section className="py-20 bg-secondary-50">
        <div className="container-custom max-w-4xl mx-auto">
          <div className="space-y-12">
            {sections.map((section, index) => (
              <div key={index} className="card">
                <div className="flex items-center mb-6">
                  <div className="w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center mr-4">
                    <section.icon className="w-6 h-6 text-primary-600" />
                  </div>
                  <h2 className="text-2xl font-display font-bold text-secondary-900">
                    {section.title}
                  </h2>
                </div>
                
                <div className="space-y-6">
                  {section.content.map((item, itemIndex) => (
                    <div key={itemIndex}>
                      <h3 className="text-lg font-semibold text-secondary-900 mb-2">
                        {item.subtitle}
                      </h3>
                      <p className="text-secondary-600 leading-relaxed">
                        {item.text}
                      </p>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Additional Sections */}
      <section className="py-20 bg-white">
        <div className="container-custom max-w-4xl mx-auto">
          <div className="space-y-12">
            {/* Your Rights */}
            <div className="card">
              <h2 className="text-2xl font-display font-bold text-secondary-900 mb-6">
                Your Rights and Choices
              </h2>
              <div className="space-y-4">
                <div>
                  <h3 className="text-lg font-semibold text-secondary-900 mb-2">Access and Portability</h3>
                  <p className="text-secondary-600">
                    You have the right to access your personal information and request a copy of your data in a portable format.
                  </p>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-secondary-900 mb-2">Correction and Updates</h3>
                  <p className="text-secondary-600">
                    You can update your profile information at any time through your account settings.
                  </p>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-secondary-900 mb-2">Deletion</h3>
                  <p className="text-secondary-600">
                    You can request deletion of your account and associated data, subject to legal and contractual obligations.
                  </p>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-secondary-900 mb-2">Marketing Communications</h3>
                  <p className="text-secondary-600">
                    You can opt out of marketing communications at any time by updating your preferences or clicking unsubscribe.
                  </p>
                </div>
              </div>
            </div>

            {/* Data Retention */}
            <div className="card">
              <h2 className="text-2xl font-display font-bold text-secondary-900 mb-6">
                Data Retention
              </h2>
              <p className="text-secondary-600 mb-4">
                We retain your information for as long as necessary to provide our services and fulfill the purposes outlined in this policy. Specific retention periods include:
              </p>
              <ul className="space-y-2 text-secondary-600">
                <li>• <strong>Account Information:</strong> Retained while your account is active and for 3 years after closure</li>
                <li>• <strong>Project Data:</strong> Retained for 7 years for legal and tax purposes</li>
                <li>• <strong>Communication Records:</strong> Retained for 2 years for support and dispute resolution</li>
                <li>• <strong>Usage Analytics:</strong> Aggregated data retained indefinitely for service improvement</li>
              </ul>
            </div>

            {/* International Transfers */}
            <div className="card">
              <h2 className="text-2xl font-display font-bold text-secondary-900 mb-6">
                International Data Transfers
              </h2>
              <p className="text-secondary-600 mb-4">
                As a global platform, we may transfer your information to countries other than your own. We ensure appropriate safeguards are in place:
              </p>
              <ul className="space-y-2 text-secondary-600">
                <li>• Standard Contractual Clauses approved by the European Commission</li>
                <li>• Adequacy decisions for transfers to countries with adequate protection</li>
                <li>• Additional security measures for transfers to other countries</li>
              </ul>
            </div>

            {/* Children's Privacy */}
            <div className="card">
              <h2 className="text-2xl font-display font-bold text-secondary-900 mb-6">
                Children's Privacy
              </h2>
              <p className="text-secondary-600">
                Our services are not intended for children under 16 years of age. We do not knowingly collect personal information from children under 16. If you believe we have collected information from a child under 16, please contact us immediately.
              </p>
            </div>

            {/* Changes to Policy */}
            <div className="card">
              <h2 className="text-2xl font-display font-bold text-secondary-900 mb-6">
                Changes to This Policy
              </h2>
              <p className="text-secondary-600">
                We may update this privacy policy from time to time. We will notify you of any material changes by posting the new policy on this page and updating the "Last updated" date. We encourage you to review this policy periodically.
              </p>
            </div>

            {/* Contact Information */}
            <div className="card bg-primary-50 border-primary-200">
              <h2 className="text-2xl font-display font-bold text-secondary-900 mb-6">
                Contact Us
              </h2>
              <p className="text-secondary-600 mb-4">
                If you have any questions about this privacy policy or our privacy practices, please contact us:
              </p>
              <div className="space-y-2 text-secondary-600">
                <p><strong>Email:</strong> <EMAIL></p>
                <p><strong>Address:</strong> 123 Tech Street, Suite 100, San Francisco, CA 94105</p>
                <p><strong>Data Protection Officer:</strong> <EMAIL></p>
              </div>
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
}
