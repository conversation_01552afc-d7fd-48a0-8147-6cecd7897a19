import React from 'react';
import { Globe, TrendingUp, FileText, MessageCircle, Shield, Users, Zap, Target, Clock, CheckCircle, Star, ArrowRight } from 'lucide-react';
import Footer from '../components/Footer';

const services = [
  {
    icon: Globe,
    title: "Global Project Marketplace",
    description: "Connect with clients and vendors worldwide through our comprehensive marketplace.",
    features: [
      "Access to 180+ countries",
      "Verified professionals",
      "Multi-language support",
      "Cultural matching algorithms"
    ],
    pricing: "Free to browse, 3-5% service fee"
  },
  {
    icon: TrendingUp,
    title: "Smart Bidding System",
    description: "AI-powered bidding with real-time market insights and recommendations.",
    features: [
      "Intelligent bid suggestions",
      "Market rate analysis",
      "Competitive intelligence",
      "Success probability scoring"
    ],
    pricing: "Included in all plans"
  },
  {
    icon: FileText,
    title: "Secure Document Management",
    description: "Advanced encryption and steganography for sensitive project documents.",
    features: [
      "End-to-end encryption",
      "Steganography protection",
      "Version control",
      "Access permissions"
    ],
    pricing: "Starting at $29/month"
  },
  {
    icon: MessageCircle,
    title: "Real-time Communication",
    description: "Integrated messaging, video calls, and collaboration tools.",
    features: [
      "Instant messaging",
      "Video conferencing",
      "File sharing",
      "Translation services"
    ],
    pricing: "Free with all accounts"
  },
  {
    icon: Shield,
    title: "Payment Protection",
    description: "Secure escrow system with fraud protection and dispute resolution.",
    features: [
      "Escrow protection",
      "Milestone payments",
      "Fraud detection",
      "Dispute mediation"
    ],
    pricing: "2.9% + $0.30 per transaction"
  },
  {
    icon: Users,
    title: "Team Collaboration",
    description: "Tools for managing distributed teams and complex projects.",
    features: [
      "Team workspaces",
      "Role-based permissions",
      "Progress tracking",
      "Resource allocation"
    ],
    pricing: "Starting at $99/month"
  }
];

const industries = [
  {
    name: "Technology",
    description: "Software development, web design, mobile apps, and IT services",
    projects: "15,000+",
    icon: "💻"
  },
  {
    name: "Marketing",
    description: "Digital marketing, content creation, SEO, and brand strategy",
    projects: "8,500+",
    icon: "📈"
  },
  {
    name: "Design",
    description: "Graphic design, UI/UX, branding, and creative services",
    projects: "12,000+",
    icon: "🎨"
  },
  {
    name: "Writing",
    description: "Content writing, copywriting, translation, and editing",
    projects: "6,200+",
    icon: "✍️"
  },
  {
    name: "Business",
    description: "Consulting, strategy, finance, and business development",
    projects: "4,800+",
    icon: "💼"
  },
  {
    name: "Engineering",
    description: "Mechanical, electrical, civil, and industrial engineering",
    projects: "3,500+",
    icon: "⚙️"
  }
];

const testimonials = [
  {
    name: "Sarah Johnson",
    role: "CEO, TechCorp",
    image: "https://images.unsplash.com/photo-1494790108755-2616b612b786?auto=format&fit=crop&w=400&q=80",
    quote: "GlobalConnect transformed how we manage our international projects. The security features and global talent pool are unmatched."
  },
  {
    name: "Michael Chen",
    role: "Freelance Developer",
    image: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?auto=format&fit=crop&w=400&q=80",
    quote: "The AI-powered matching system connected me with clients that perfectly match my skills. My income has increased by 40%."
  },
  {
    name: "Emily Rodriguez",
    role: "Design Agency Owner",
    image: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?auto=format&fit=crop&w=400&q=80",
    quote: "The collaboration tools and secure payment system give us confidence to work with clients worldwide."
  }
];

export default function Services() {
  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="gradient-bg py-20">
        <div className="container-custom text-center">
          <h1 className="text-4xl sm:text-5xl lg:text-6xl font-display font-bold text-secondary-900 mb-6">
            Our <span className="gradient-text">Services</span>
          </h1>
          <p className="text-xl text-secondary-600 mb-8 max-w-3xl mx-auto">
            Comprehensive solutions for global project management, collaboration, and business growth.
          </p>
          <div className="flex flex-wrap justify-center gap-4">
            <button className="btn-primary">Explore Services</button>
            <button className="btn-secondary">Schedule Demo</button>
          </div>
        </div>
      </section>

      {/* Services Grid */}
      <section className="py-20 bg-white">
        <div className="container-custom">
          <div className="text-center mb-16">
            <h2 className="text-3xl sm:text-4xl font-display font-bold text-secondary-900 mb-6">
              Everything You Need to Succeed
            </h2>
            <p className="text-xl text-secondary-600 max-w-3xl mx-auto">
              From project discovery to completion, we provide all the tools and services 
              you need for successful global collaboration.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {services.map((service, index) => (
              <div key={index} className="card hover:shadow-lg transition-shadow duration-300">
                <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mb-6">
                  <service.icon className="w-8 h-8 text-primary-600" />
                </div>
                
                <h3 className="text-xl font-semibold text-secondary-900 mb-4">
                  {service.title}
                </h3>
                
                <p className="text-secondary-600 mb-6 leading-relaxed">
                  {service.description}
                </p>
                
                <ul className="space-y-2 mb-6">
                  {service.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-center text-sm text-secondary-600">
                      <CheckCircle className="w-4 h-4 text-success-600 mr-2 flex-shrink-0" />
                      {feature}
                    </li>
                  ))}
                </ul>
                
                <div className="border-t border-secondary-100 pt-4">
                  <p className="text-sm text-secondary-500 mb-4">{service.pricing}</p>
                  <button className="btn-secondary w-full text-sm">Learn More</button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Industries We Serve */}
      <section className="py-20 bg-secondary-50">
        <div className="container-custom">
          <div className="text-center mb-16">
            <h2 className="text-3xl sm:text-4xl font-display font-bold text-secondary-900 mb-6">
              Industries We Serve
            </h2>
            <p className="text-xl text-secondary-600 max-w-3xl mx-auto">
              Our platform supports professionals and businesses across diverse industries.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {industries.map((industry, index) => (
              <div key={index} className="card text-center hover:shadow-lg transition-shadow duration-300">
                <div className="text-4xl mb-4">{industry.icon}</div>
                <h3 className="text-xl font-semibold text-secondary-900 mb-3">
                  {industry.name}
                </h3>
                <p className="text-secondary-600 mb-4 leading-relaxed">
                  {industry.description}
                </p>
                <div className="text-sm text-primary-600 font-semibold">
                  {industry.projects} active projects
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Process Overview */}
      <section className="py-20 bg-white">
        <div className="container-custom">
          <div className="text-center mb-16">
            <h2 className="text-3xl sm:text-4xl font-display font-bold text-secondary-900 mb-6">
              How It Works
            </h2>
            <p className="text-xl text-secondary-600 max-w-3xl mx-auto">
              Get started in minutes with our streamlined process.
            </p>
          </div>

          <div className="grid md:grid-cols-4 gap-8">
            {[
              { step: "01", title: "Sign Up", description: "Create your profile and verify your identity" },
              { step: "02", title: "Browse & Connect", description: "Find projects or talent that match your needs" },
              { step: "03", title: "Collaborate", description: "Work together using our secure platform" },
              { step: "04", title: "Get Paid", description: "Receive secure payments through our escrow system" }
            ].map((item, index) => (
              <div key={index} className="text-center relative">
                <div className="w-16 h-16 bg-primary-600 text-white rounded-full flex items-center justify-center mx-auto mb-4 text-xl font-bold">
                  {item.step}
                </div>
                <h3 className="text-lg font-semibold text-secondary-900 mb-2">{item.title}</h3>
                <p className="text-secondary-600">{item.description}</p>
                
                {index < 3 && (
                  <div className="hidden md:block absolute top-8 left-full w-full">
                    <ArrowRight className="w-6 h-6 text-primary-300 mx-auto" />
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Testimonials */}
      <section className="py-20 bg-secondary-50">
        <div className="container-custom">
          <div className="text-center mb-16">
            <h2 className="text-3xl sm:text-4xl font-display font-bold text-secondary-900 mb-6">
              What Our Users Say
            </h2>
            <p className="text-xl text-secondary-600 max-w-3xl mx-auto">
              Join thousands of satisfied clients and professionals worldwide.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <div key={index} className="card text-center">
                <div className="flex justify-center mb-4">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className="w-5 h-5 text-warning-400 fill-current" />
                  ))}
                </div>
                
                <p className="text-secondary-600 mb-6 leading-relaxed italic">
                  "{testimonial.quote}"
                </p>
                
                <div className="flex items-center justify-center">
                  <img
                    src={testimonial.image}
                    alt={testimonial.name}
                    className="w-12 h-12 rounded-full mr-4"
                  />
                  <div className="text-left">
                    <p className="font-semibold text-secondary-900">{testimonial.name}</p>
                    <p className="text-sm text-secondary-600">{testimonial.role}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Enterprise Solutions */}
      <section className="py-20 bg-white">
        <div className="container-custom">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl sm:text-4xl font-display font-bold text-secondary-900 mb-6">
                Enterprise Solutions
              </h2>
              <p className="text-lg text-secondary-600 mb-6 leading-relaxed">
                Scale your business with our enterprise-grade platform designed for large 
                organizations with complex project management needs.
              </p>
              
              <ul className="space-y-4 mb-8">
                {[
                  "Custom security policies and compliance",
                  "Dedicated account management",
                  "White-label solutions",
                  "Advanced analytics and reporting",
                  "Priority support and SLA guarantees"
                ].map((feature, index) => (
                  <li key={index} className="flex items-center">
                    <CheckCircle className="w-5 h-5 text-success-600 mr-3 flex-shrink-0" />
                    <span className="text-secondary-600">{feature}</span>
                  </li>
                ))}
              </ul>
              
              <div className="flex flex-wrap gap-4">
                <button className="btn-primary">Contact Sales</button>
                <button className="btn-secondary">View Enterprise Features</button>
              </div>
            </div>
            
            <div className="relative">
              <img
                src="https://images.unsplash.com/photo-**********-b33ff0c44a43?auto=format&fit=crop&w=800&q=80"
                alt="Enterprise solutions"
                className="rounded-2xl shadow-xl"
              />
              <div className="absolute -bottom-6 -left-6 w-32 h-32 bg-primary-600 rounded-2xl flex items-center justify-center">
                <Target className="w-16 h-16 text-white" />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-primary-600">
        <div className="container-custom text-center">
          <h2 className="text-3xl sm:text-4xl font-display font-bold text-white mb-6">
            Ready to Transform Your Business?
          </h2>
          <p className="text-xl text-primary-100 mb-8 max-w-2xl mx-auto">
            Join thousands of businesses already using GlobalConnect to manage their projects 
            and connect with top talent worldwide.
          </p>
          <div className="flex flex-wrap justify-center gap-4">
            <button className="bg-white text-primary-600 px-8 py-3 rounded-lg font-semibold hover:bg-primary-50 transition-colors duration-200">
              Get Started Free
            </button>
            <button className="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-primary-600 transition-colors duration-200">
              Schedule Demo
            </button>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
}
