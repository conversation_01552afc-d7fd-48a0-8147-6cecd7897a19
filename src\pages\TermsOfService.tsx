import React from 'react';
import { FileText, Users, Shield, AlertTriangle, Scale, Clock } from 'lucide-react';
import Footer from '../components/Footer';

const sections = [
  {
    title: 'Acceptance of Terms',
    icon: FileText,
    content: 'By accessing and using GlobalConnect, you accept and agree to be bound by the terms and provision of this agreement. If you do not agree to abide by the above, please do not use this service.'
  },
  {
    title: 'Use License',
    icon: Users,
    content: 'Permission is granted to temporarily download one copy of GlobalConnect materials for personal, non-commercial transitory viewing only. This is the grant of a license, not a transfer of title.'
  },
  {
    title: 'User Accounts',
    icon: Shield,
    content: 'When you create an account with us, you must provide information that is accurate, complete, and current at all times. You are responsible for safeguarding the password and for all activities under your account.'
  },
  {
    title: 'Prohibited Uses',
    icon: AlertTriangle,
    content: 'You may not use our service for any illegal or unauthorized purpose nor may you, in the use of the service, violate any laws in your jurisdiction including but not limited to copyright laws.'
  }
];

export default function TermsOfService() {
  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="gradient-bg py-20">
        <div className="container-custom text-center">
          <div className="w-20 h-20 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <Scale className="w-10 h-10 text-primary-600" />
          </div>
          <h1 className="text-4xl sm:text-5xl lg:text-6xl font-display font-bold text-secondary-900 mb-6">
            Terms of <span className="gradient-text">Service</span>
          </h1>
          <p className="text-xl text-secondary-600 mb-8 max-w-3xl mx-auto">
            Please read these terms carefully before using our services. By using GlobalConnect, you agree to these terms.
          </p>
          <div className="flex items-center justify-center space-x-4 text-sm text-secondary-500">
            <div className="flex items-center">
              <Clock className="w-4 h-4 mr-2" />
              Last updated: January 15, 2024
            </div>
          </div>
        </div>
      </section>

      {/* Quick Overview */}
      <section className="py-16 bg-white">
        <div className="container-custom max-w-4xl mx-auto">
          <div className="card bg-primary-50 border-primary-200">
            <h2 className="text-2xl font-display font-bold text-secondary-900 mb-4">
              Terms Overview
            </h2>
            <p className="text-secondary-600 mb-4">
              These terms govern your use of GlobalConnect and describe the rights and responsibilities of all users. Key points include:
            </p>
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <h3 className="font-semibold text-secondary-900 mb-2">Your Responsibilities:</h3>
                <ul className="text-secondary-600 space-y-1">
                  <li>• Provide accurate information</li>
                  <li>• Use the platform legally and ethically</li>
                  <li>• Respect other users' rights</li>
                </ul>
              </div>
              <div>
                <h3 className="font-semibold text-secondary-900 mb-2">Our Commitments:</h3>
                <ul className="text-secondary-600 space-y-1">
                  <li>• Provide reliable service</li>
                  <li>• Protect your data and privacy</li>
                  <li>• Support fair dispute resolution</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Main Sections */}
      <section className="py-20 bg-secondary-50">
        <div className="container-custom max-w-4xl mx-auto">
          <div className="space-y-8">
            {sections.map((section, index) => (
              <div key={index} className="card">
                <div className="flex items-center mb-4">
                  <div className="w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center mr-4">
                    <section.icon className="w-6 h-6 text-primary-600" />
                  </div>
                  <h2 className="text-2xl font-display font-bold text-secondary-900">
                    {section.title}
                  </h2>
                </div>
                <p className="text-secondary-600 leading-relaxed">
                  {section.content}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Detailed Terms */}
      <section className="py-20 bg-white">
        <div className="container-custom max-w-4xl mx-auto">
          <div className="space-y-12">
            {/* Service Description */}
            <div className="card">
              <h2 className="text-2xl font-display font-bold text-secondary-900 mb-6">
                Service Description
              </h2>
              <p className="text-secondary-600 mb-4">
                GlobalConnect is a platform that connects businesses with freelancers and agencies worldwide. Our services include:
              </p>
              <ul className="space-y-2 text-secondary-600">
                <li>• Project posting and bidding system</li>
                <li>• Secure messaging and file sharing</li>
                <li>• Payment processing and escrow services</li>
                <li>• Project management tools</li>
                <li>• Dispute resolution services</li>
              </ul>
            </div>

            {/* User Responsibilities */}
            <div className="card">
              <h2 className="text-2xl font-display font-bold text-secondary-900 mb-6">
                User Responsibilities
              </h2>
              <div className="space-y-4">
                <div>
                  <h3 className="text-lg font-semibold text-secondary-900 mb-2">Account Security</h3>
                  <p className="text-secondary-600">
                    You are responsible for maintaining the confidentiality of your account credentials and for restricting access to your account.
                  </p>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-secondary-900 mb-2">Accurate Information</h3>
                  <p className="text-secondary-600">
                    You must provide accurate, current, and complete information during registration and keep your profile updated.
                  </p>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-secondary-900 mb-2">Compliance with Laws</h3>
                  <p className="text-secondary-600">
                    You must comply with all applicable local, state, national, and international laws and regulations.
                  </p>
                </div>
              </div>
            </div>

            {/* Payment Terms */}
            <div className="card">
              <h2 className="text-2xl font-display font-bold text-secondary-900 mb-6">
                Payment Terms
              </h2>
              <div className="space-y-4">
                <div>
                  <h3 className="text-lg font-semibold text-secondary-900 mb-2">Service Fees</h3>
                  <p className="text-secondary-600">
                    GlobalConnect charges service fees for successful project completions. Current fee structures are available on our pricing page.
                  </p>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-secondary-900 mb-2">Payment Processing</h3>
                  <p className="text-secondary-600">
                    All payments are processed through secure third-party payment processors. We do not store payment card information.
                  </p>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-secondary-900 mb-2">Refunds</h3>
                  <p className="text-secondary-600">
                    Refund policies vary by service type. Please refer to our refund policy for detailed information.
                  </p>
                </div>
              </div>
            </div>

            {/* Intellectual Property */}
            <div className="card">
              <h2 className="text-2xl font-display font-bold text-secondary-900 mb-6">
                Intellectual Property Rights
              </h2>
              <p className="text-secondary-600 mb-4">
                The service and its original content, features, and functionality are and will remain the exclusive property of GlobalConnect and its licensors.
              </p>
              <div className="space-y-4">
                <div>
                  <h3 className="text-lg font-semibold text-secondary-900 mb-2">User Content</h3>
                  <p className="text-secondary-600">
                    You retain ownership of content you submit to the platform but grant us a license to use, display, and distribute it as necessary to provide our services.
                  </p>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-secondary-900 mb-2">Respect for IP</h3>
                  <p className="text-secondary-600">
                    You must respect the intellectual property rights of others and not upload or share content that infringes on third-party rights.
                  </p>
                </div>
              </div>
            </div>

            {/* Termination */}
            <div className="card">
              <h2 className="text-2xl font-display font-bold text-secondary-900 mb-6">
                Termination
              </h2>
              <div className="space-y-4">
                <div>
                  <h3 className="text-lg font-semibold text-secondary-900 mb-2">By You</h3>
                  <p className="text-secondary-600">
                    You may terminate your account at any time by contacting us or using the account deletion feature in your settings.
                  </p>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-secondary-900 mb-2">By Us</h3>
                  <p className="text-secondary-600">
                    We may terminate or suspend your account immediately if you breach these terms or engage in prohibited activities.
                  </p>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-secondary-900 mb-2">Effect of Termination</h3>
                  <p className="text-secondary-600">
                    Upon termination, your right to use the service will cease immediately, but certain provisions will survive termination.
                  </p>
                </div>
              </div>
            </div>

            {/* Limitation of Liability */}
            <div className="card">
              <h2 className="text-2xl font-display font-bold text-secondary-900 mb-6">
                Limitation of Liability
              </h2>
              <p className="text-secondary-600 mb-4">
                In no event shall GlobalConnect, nor its directors, employees, partners, agents, suppliers, or affiliates, be liable for any indirect, incidental, special, consequential, or punitive damages.
              </p>
              <p className="text-secondary-600">
                Our total liability to you for any damages arising from or related to this agreement shall not exceed the amount you have paid to GlobalConnect in the twelve (12) months preceding the claim.
              </p>
            </div>

            {/* Governing Law */}
            <div className="card">
              <h2 className="text-2xl font-display font-bold text-secondary-900 mb-6">
                Governing Law
              </h2>
              <p className="text-secondary-600">
                These terms shall be interpreted and governed by the laws of the State of California, United States, without regard to its conflict of law provisions. Any disputes will be resolved in the courts of San Francisco County, California.
              </p>
            </div>

            {/* Changes to Terms */}
            <div className="card">
              <h2 className="text-2xl font-display font-bold text-secondary-900 mb-6">
                Changes to Terms
              </h2>
              <p className="text-secondary-600">
                We reserve the right to modify or replace these terms at any time. If a revision is material, we will try to provide at least 30 days notice prior to any new terms taking effect.
              </p>
            </div>

            {/* Contact Information */}
            <div className="card bg-primary-50 border-primary-200">
              <h2 className="text-2xl font-display font-bold text-secondary-900 mb-6">
                Contact Us
              </h2>
              <p className="text-secondary-600 mb-4">
                If you have any questions about these Terms of Service, please contact us:
              </p>
              <div className="space-y-2 text-secondary-600">
                <p><strong>Email:</strong> <EMAIL></p>
                <p><strong>Address:</strong> 123 Tech Street, Suite 100, San Francisco, CA 94105</p>
                <p><strong>Phone:</strong> +****************</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
}
