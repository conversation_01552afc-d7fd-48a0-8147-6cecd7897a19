import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import axios from 'axios'
import { authService } from '../authService'
import { mockUser, mockApiResponse, mockApiError } from '../../test/utils'

// Mock axios
vi.mock('axios')
const mockedAxios = vi.mocked(axios)

describe('AuthService', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    localStorage.clear()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('login', () => {
    it('should login successfully with valid credentials', async () => {
      const loginData = { email: '<EMAIL>', password: 'password123' }
      const responseData = { user: mockUser, token: 'mock-token' }
      
      mockedAxios.post.mockResolvedValue({ data: responseData })

      const result = await authService.login(loginData)

      expect(mockedAxios.post).toHaveBeenCalledWith(
        'http://localhost:5000/api/auth/login',
        loginData
      )
      expect(result).toEqual(responseData)
      expect(localStorage.setItem).toHaveBeenCalledWith('token', 'mock-token')
    })

    it('should throw error with invalid credentials', async () => {
      const loginData = { email: '<EMAIL>', password: 'wrongpassword' }
      const errorMessage = 'Invalid credentials'
      
      mockedAxios.post.mockRejectedValue({
        response: { data: { message: errorMessage } }
      })

      await expect(authService.login(loginData)).rejects.toThrow(errorMessage)
      expect(localStorage.setItem).not.toHaveBeenCalled()
    })

    it('should handle network errors', async () => {
      const loginData = { email: '<EMAIL>', password: 'password123' }
      
      mockedAxios.post.mockRejectedValue(new Error('Network Error'))

      await expect(authService.login(loginData)).rejects.toThrow('Failed to login')
    })
  })

  describe('register', () => {
    it('should register successfully with valid data', async () => {
      const registerData = {
        name: 'John Doe',
        email: '<EMAIL>',
        password: 'password123',
        role: 'client' as const
      }
      const responseData = { user: mockUser, token: 'mock-token' }
      
      mockedAxios.post.mockResolvedValue({ data: responseData })

      const result = await authService.register(registerData)

      expect(mockedAxios.post).toHaveBeenCalledWith(
        'http://localhost:5000/api/auth/register',
        registerData
      )
      expect(result).toEqual(responseData)
      expect(localStorage.setItem).toHaveBeenCalledWith('token', 'mock-token')
    })

    it('should throw error when email already exists', async () => {
      const registerData = {
        name: 'John Doe',
        email: '<EMAIL>',
        password: 'password123',
        role: 'client' as const
      }
      const errorMessage = 'Email already exists'
      
      mockedAxios.post.mockRejectedValue({
        response: { data: { message: errorMessage } }
      })

      await expect(authService.register(registerData)).rejects.toThrow(errorMessage)
    })
  })

  describe('logout', () => {
    it('should logout successfully', async () => {
      localStorage.setItem('token', 'mock-token')
      mockedAxios.post.mockResolvedValue({ data: { message: 'Logged out' } })

      await authService.logout()

      expect(mockedAxios.post).toHaveBeenCalledWith('http://localhost:5000/api/auth/logout')
      expect(localStorage.removeItem).toHaveBeenCalledWith('token')
    })

    it('should clear token even if logout request fails', async () => {
      localStorage.setItem('token', 'mock-token')
      mockedAxios.post.mockRejectedValue(new Error('Network Error'))

      await authService.logout()

      expect(localStorage.removeItem).toHaveBeenCalledWith('token')
    })
  })

  describe('getCurrentUser', () => {
    it('should return current user when token exists', async () => {
      localStorage.setItem('token', 'mock-token')
      mockedAxios.get.mockResolvedValue({ data: mockUser })

      const result = await authService.getCurrentUser()

      expect(mockedAxios.get).toHaveBeenCalledWith('http://localhost:5000/api/auth/me')
      expect(result).toEqual(mockUser)
    })

    it('should throw error when no token exists', async () => {
      await expect(authService.getCurrentUser()).rejects.toThrow('No authentication token found')
    })

    it('should throw error when token is invalid', async () => {
      localStorage.setItem('token', 'invalid-token')
      mockedAxios.get.mockRejectedValue({
        response: { data: { message: 'Invalid token' } }
      })

      await expect(authService.getCurrentUser()).rejects.toThrow('Invalid token')
    })
  })

  describe('updateProfile', () => {
    it('should update profile successfully', async () => {
      const updateData = { name: 'Updated Name' }
      const updatedUser = { ...mockUser, name: 'Updated Name' }
      
      mockedAxios.put.mockResolvedValue({ data: updatedUser })

      const result = await authService.updateProfile(updateData)

      expect(mockedAxios.put).toHaveBeenCalledWith(
        'http://localhost:5000/api/auth/profile',
        updateData
      )
      expect(result).toEqual(updatedUser)
    })
  })

  describe('changePassword', () => {
    it('should change password successfully', async () => {
      const passwordData = {
        currentPassword: 'oldpassword',
        newPassword: 'newpassword'
      }
      
      mockedAxios.put.mockResolvedValue({ data: { message: 'Password updated' } })

      await authService.changePassword(passwordData)

      expect(mockedAxios.put).toHaveBeenCalledWith(
        'http://localhost:5000/api/auth/change-password',
        passwordData
      )
    })

    it('should throw error with wrong current password', async () => {
      const passwordData = {
        currentPassword: 'wrongpassword',
        newPassword: 'newpassword'
      }
      
      mockedAxios.put.mockRejectedValue({
        response: { data: { message: 'Current password is incorrect' } }
      })

      await expect(authService.changePassword(passwordData)).rejects.toThrow(
        'Current password is incorrect'
      )
    })
  })

  describe('forgotPassword', () => {
    it('should send reset email successfully', async () => {
      const email = '<EMAIL>'
      
      mockedAxios.post.mockResolvedValue({ 
        data: { message: 'Reset email sent' } 
      })

      await authService.forgotPassword(email)

      expect(mockedAxios.post).toHaveBeenCalledWith(
        'http://localhost:5000/api/auth/forgot-password',
        { email }
      )
    })
  })

  describe('resetPassword', () => {
    it('should reset password successfully', async () => {
      const resetData = {
        token: 'reset-token',
        password: 'newpassword'
      }
      
      mockedAxios.post.mockResolvedValue({ 
        data: { message: 'Password reset successful' } 
      })

      await authService.resetPassword(resetData)

      expect(mockedAxios.post).toHaveBeenCalledWith(
        'http://localhost:5000/api/auth/reset-password',
        resetData
      )
    })
  })

  describe('verifyEmail', () => {
    it('should verify email successfully', async () => {
      const token = 'verify-token'
      
      mockedAxios.post.mockResolvedValue({ 
        data: { message: 'Email verified' } 
      })

      await authService.verifyEmail(token)

      expect(mockedAxios.post).toHaveBeenCalledWith(
        'http://localhost:5000/api/auth/verify-email',
        { token }
      )
    })
  })

  describe('utility methods', () => {
    describe('isAuthenticated', () => {
      it('should return true when token exists', () => {
        localStorage.setItem('token', 'mock-token')
        expect(authService.isAuthenticated()).toBe(true)
      })

      it('should return false when no token exists', () => {
        expect(authService.isAuthenticated()).toBe(false)
      })
    })

    describe('getToken', () => {
      it('should return token when it exists', () => {
        localStorage.setItem('token', 'mock-token')
        expect(authService.getToken()).toBe('mock-token')
      })

      it('should return null when no token exists', () => {
        expect(authService.getToken()).toBeNull()
      })
    })

    describe('setAuthHeader', () => {
      it('should set authorization header when token exists', () => {
        localStorage.setItem('token', 'mock-token')
        authService.setAuthHeader()
        
        expect(axios.defaults.headers.common['Authorization']).toBe('Bearer mock-token')
      })

      it('should delete authorization header when no token exists', () => {
        authService.setAuthHeader()
        
        expect(axios.defaults.headers.common['Authorization']).toBeUndefined()
      })
    })
  })
})
