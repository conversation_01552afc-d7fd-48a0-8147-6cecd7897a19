import axios from 'axios';

const API_URL = 'http://localhost:5000/api/analytics';

export interface DashboardStats {
  totalProjects?: number;
  activeProjects?: number;
  completedProjects?: number;
  totalBids?: number;
  acceptedBids?: number;
  pendingBids?: number;
  role: 'client' | 'vendor' | 'admin';
}

export interface ChartDataPoint {
  date: string;
  count: number;
  [key: string]: any;
}

export interface ProjectAnalytics {
  projectViews: {
    total: number;
    byDate: ChartDataPoint[];
  };
  bids: {
    total: number;
    average: number;
    overTime: ChartDataPoint[];
    distribution: Array<{ amount: number; count: number }>;
    status: {
      pending: number;
      accepted: number;
      rejected: number;
      withdrawn: number;
    };
  };
  documents: {
    total: number;
    views: number;
  };
  messages: {
    total: number;
    byDate: ChartDataPoint[];
  };
}

export interface UserAnalytics {
  user: {
    _id: string;
    name: string;
    role: string;
  };
  projects?: {
    stats: {
      total: number;
      status: Record<string, number>;
      categories: Record<string, number>;
    };
    overTime: ChartDataPoint[];
  };
  bids?: {
    total: number;
    accepted?: number;
    rejected?: number;
    pending?: number;
    withdrawn?: number;
    successRate?: number;
    overTime: ChartDataPoint[];
    byCategory?: Record<string, any>;
  };
  assignedProjects?: {
    total: number;
    inProgress: number;
    completed: number;
  };
}

export interface PlatformAnalytics {
  users: {
    total: number;
    clients: number;
    vendors: number;
    overTime: Array<{
      date: string;
      total: number;
      client: number;
      vendor: number;
    }>;
  };
  projects: {
    total: number;
    open: number;
    inProgress: number;
    completed: number;
    overTime: ChartDataPoint[];
    categories: Record<string, number>;
    budgetByCategory: Record<string, any>;
    completionRate: number;
  };
  bids: {
    total: number;
    accepted: number;
    avgPerProject: number;
    acceptanceRate: number;
  };
}

export interface RevenueAnalytics {
  totalRevenue: number;
  projectCount: number;
  averageProjectValue: number;
  monthlyRevenue: Array<{
    month: string;
    revenue: number;
  }>;
}

class AnalyticsService {
  async trackEvent(type: string, projectId?: string, metadata?: any): Promise<void> {
    try {
      await axios.post(`${API_URL}/track`, {
        type,
        projectId,
        metadata
      });
    } catch (error) {
      // Silently fail for analytics tracking
      console.warn('Failed to track analytics event:', error);
    }
  }

  async getDashboardStats(): Promise<DashboardStats> {
    try {
      const response = await axios.get(`${API_URL}/dashboard`);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch dashboard stats');
    }
  }

  async getProjectAnalytics(projectId: string, startDate?: string, endDate?: string): Promise<ProjectAnalytics> {
    try {
      const params = new URLSearchParams();
      if (startDate) params.append('startDate', startDate);
      if (endDate) params.append('endDate', endDate);

      const response = await axios.get(`${API_URL}/project/${projectId}?${params.toString()}`);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch project analytics');
    }
  }

  async getUserAnalytics(startDate?: string, endDate?: string, userId?: string): Promise<UserAnalytics> {
    try {
      const params = new URLSearchParams();
      if (startDate) params.append('startDate', startDate);
      if (endDate) params.append('endDate', endDate);
      if (userId) params.append('userId', userId);

      const response = await axios.get(`${API_URL}/user?${params.toString()}`);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch user analytics');
    }
  }

  async getPlatformAnalytics(startDate?: string, endDate?: string): Promise<PlatformAnalytics> {
    try {
      const params = new URLSearchParams();
      if (startDate) params.append('startDate', startDate);
      if (endDate) params.append('endDate', endDate);

      const response = await axios.get(`${API_URL}/platform?${params.toString()}`);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch platform analytics');
    }
  }

  async getRevenueAnalytics(startDate?: string, endDate?: string): Promise<RevenueAnalytics> {
    try {
      const params = new URLSearchParams();
      if (startDate) params.append('startDate', startDate);
      if (endDate) params.append('endDate', endDate);

      const response = await axios.get(`${API_URL}/revenue?${params.toString()}`);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch revenue analytics');
    }
  }

  // Utility methods for formatting and calculations
  formatCurrency(amount: number): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  }

  formatPercentage(value: number): string {
    return `${value.toFixed(1)}%`;
  }

  formatNumber(value: number): string {
    if (value >= 1000000) {
      return `${(value / 1000000).toFixed(1)}M`;
    } else if (value >= 1000) {
      return `${(value / 1000).toFixed(1)}K`;
    }
    return value.toString();
  }

  calculateGrowthRate(current: number, previous: number): number {
    if (previous === 0) return current > 0 ? 100 : 0;
    return ((current - previous) / previous) * 100;
  }

  getDateRange(days: number): { startDate: string; endDate: string } {
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    return {
      startDate: startDate.toISOString().split('T')[0],
      endDate: endDate.toISOString().split('T')[0]
    };
  }

  getMonthRange(months: number): { startDate: string; endDate: string } {
    const endDate = new Date();
    const startDate = new Date();
    startDate.setMonth(startDate.getMonth() - months);

    return {
      startDate: startDate.toISOString().split('T')[0],
      endDate: endDate.toISOString().split('T')[0]
    };
  }

  // Chart data processing
  processChartData(data: ChartDataPoint[], fillGaps: boolean = true): ChartDataPoint[] {
    if (!fillGaps || data.length === 0) return data;

    const sortedData = [...data].sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
    const startDate = new Date(sortedData[0].date);
    const endDate = new Date(sortedData[sortedData.length - 1].date);
    
    const filledData: ChartDataPoint[] = [];
    const dataMap = new Map(sortedData.map(item => [item.date, item]));

    for (let d = new Date(startDate); d <= endDate; d.setDate(d.getDate() + 1)) {
      const dateStr = d.toISOString().split('T')[0];
      const existingData = dataMap.get(dateStr);
      
      filledData.push(existingData || { date: dateStr, count: 0 });
    }

    return filledData;
  }

  // Color schemes for charts
  getChartColors(): {
    primary: string[];
    success: string[];
    warning: string[];
    error: string[];
    info: string[];
  } {
    return {
      primary: ['#059669', '#10b981', '#34d399', '#6ee7b7', '#a7f3d0'],
      success: ['#059669', '#10b981', '#34d399'],
      warning: ['#d97706', '#f59e0b', '#fbbf24'],
      error: ['#dc2626', '#ef4444', '#f87171'],
      info: ['#2563eb', '#3b82f6', '#60a5fa']
    };
  }

  // Status color mapping
  getStatusColor(status: string): string {
    const colors = {
      open: '#10b981',
      'in-progress': '#f59e0b',
      completed: '#059669',
      cancelled: '#ef4444',
      pending: '#f59e0b',
      accepted: '#10b981',
      rejected: '#ef4444',
      withdrawn: '#6b7280'
    };
    return colors[status as keyof typeof colors] || '#6b7280';
  }

  // Generate insights from analytics data
  generateInsights(data: any): string[] {
    const insights: string[] = [];

    // Add insights based on data patterns
    if (data.bids?.successRate !== undefined) {
      if (data.bids.successRate > 80) {
        insights.push('Excellent bid success rate! Your proposals are highly competitive.');
      } else if (data.bids.successRate < 20) {
        insights.push('Consider improving your bid strategy to increase success rate.');
      }
    }

    if (data.projects?.stats?.total > 0) {
      const completionRate = (data.projects.stats.status?.completed || 0) / data.projects.stats.total * 100;
      if (completionRate > 90) {
        insights.push('Outstanding project completion rate!');
      }
    }

    return insights;
  }
}

export const analyticsService = new AnalyticsService();
