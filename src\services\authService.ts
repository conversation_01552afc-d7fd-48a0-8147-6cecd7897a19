import axios from 'axios';

const API_URL = 'http://localhost:5000/api/auth';

// Configure axios defaults
axios.defaults.withCredentials = true;

export interface User {
  _id: string;
  name: string;
  email: string;
  role: 'client' | 'vendor' | 'admin';
  company?: string;
  companyLogo?: string;
  bio?: string;
  location?: string;
  website?: string;
  skills?: string[];
  rating?: number;
}

export interface AuthResponse {
  _id: string;
  name: string;
  email: string;
  role: 'client' | 'vendor' | 'admin';
  company?: string;
  companyLogo?: string;
  token: string;
}

export interface LoginData {
  email: string;
  password: string;
}

export interface RegisterData {
  name: string;
  email: string;
  password: string;
  role: 'client' | 'vendor';
  company?: string;
}

class AuthService {
  private token: string | null = null;
  private user: User | null = null;

  constructor() {
    // Check for existing token in localStorage
    this.token = localStorage.getItem('token');
    const userData = localStorage.getItem('user');
    if (userData) {
      try {
        this.user = JSON.parse(userData);
      } catch (error) {
        console.error('Error parsing user data from localStorage:', error);
        this.clearAuth();
      }
    }

    // Set axios default authorization header if token exists
    if (this.token) {
      axios.defaults.headers.common['Authorization'] = `Bearer ${this.token}`;
    }
  }

  async login(data: LoginData): Promise<AuthResponse> {
    try {
      const response = await axios.post(`${API_URL}/login`, data);
      const authData = response.data;
      
      this.setAuth(authData);
      return authData;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Login failed');
    }
  }

  async register(data: RegisterData): Promise<AuthResponse> {
    try {
      const response = await axios.post(`${API_URL}/register`, data);
      const authData = response.data;
      
      this.setAuth(authData);
      return authData;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Registration failed');
    }
  }

  async getProfile(): Promise<User> {
    try {
      const response = await axios.get(`${API_URL}/profile`);
      this.user = response.data;
      localStorage.setItem('user', JSON.stringify(this.user));
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to get profile');
    }
  }

  async updateProfile(data: Partial<User>): Promise<User> {
    try {
      const response = await axios.put(`${API_URL}/profile`, data);
      const updatedUser = response.data;
      
      this.user = updatedUser;
      localStorage.setItem('user', JSON.stringify(this.user));
      
      // Update token if provided
      if (updatedUser.token) {
        this.token = updatedUser.token;
        localStorage.setItem('token', this.token);
        axios.defaults.headers.common['Authorization'] = `Bearer ${this.token}`;
      }
      
      return updatedUser;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to update profile');
    }
  }

  async verifyToken(): Promise<boolean> {
    if (!this.token) return false;
    
    try {
      const response = await axios.get(`${API_URL}/verify`);
      return response.data.isValid;
    } catch (error) {
      this.clearAuth();
      return false;
    }
  }

  logout(): void {
    this.clearAuth();
  }

  private setAuth(authData: AuthResponse): void {
    this.token = authData.token;
    this.user = {
      _id: authData._id,
      name: authData.name,
      email: authData.email,
      role: authData.role,
      company: authData.company,
      companyLogo: authData.companyLogo
    };

    localStorage.setItem('token', this.token);
    localStorage.setItem('user', JSON.stringify(this.user));
    axios.defaults.headers.common['Authorization'] = `Bearer ${this.token}`;
  }

  private clearAuth(): void {
    this.token = null;
    this.user = null;
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    delete axios.defaults.headers.common['Authorization'];
  }

  getCurrentUser(): User | null {
    return this.user;
  }

  getToken(): string | null {
    return this.token;
  }

  isAuthenticated(): boolean {
    return !!this.token && !!this.user;
  }
}

export const authService = new AuthService();
