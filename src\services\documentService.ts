import axios from 'axios';

const API_URL = 'http://localhost:5000/api/documents';

export interface Document {
  _id: string;
  title: string;
  description: string;
  project: string;
  fileKey: string;
  fileType: string;
  fileName: string;
  fileSize: number;
  uploadedBy: {
    _id: string;
    name: string;
    email: string;
  };
  securityLevel: number;
  metadata: {
    uploadedBy: string;
    projectId: string;
    timestamp: string;
    userDefined: any;
  };
  permissions: string[];
  versions: Array<{
    version: number;
    fileKey: string;
    updatedBy: string;
    description: string;
    createdAt: string;
  }>;
  isDeleted: boolean;
  deletedAt?: string;
  deletedBy?: string;
  createdAt: string;
  updatedAt: string;
  url?: string;
}

export interface UploadDocumentData {
  projectId: string;
  title: string;
  description?: string;
  securityLevel?: number;
  metadata?: any;
  file: File;
}

export interface UpdateDocumentData {
  title?: string;
  description?: string;
  permissions?: string[];
}

export interface DocumentVersion {
  file: File;
  description?: string;
}

class DocumentService {
  async uploadDocument(data: UploadDocumentData): Promise<{ document: Document; url: string }> {
    try {
      const formData = new FormData();
      formData.append('file', data.file);
      formData.append('projectId', data.projectId);
      formData.append('title', data.title);
      
      if (data.description) {
        formData.append('description', data.description);
      }
      
      if (data.securityLevel) {
        formData.append('securityLevel', data.securityLevel.toString());
      }
      
      if (data.metadata) {
        formData.append('metadata', JSON.stringify(data.metadata));
      }

      const response = await axios.post(API_URL, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });
      
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to upload document');
    }
  }

  async getProjectDocuments(projectId: string): Promise<Document[]> {
    try {
      const response = await axios.get(`${API_URL}/project/${projectId}`);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch documents');
    }
  }

  async getDocumentById(id: string): Promise<Document> {
    try {
      const response = await axios.get(`${API_URL}/${id}`);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch document');
    }
  }

  async updateDocument(id: string, data: UpdateDocumentData): Promise<Document> {
    try {
      const response = await axios.put(`${API_URL}/${id}`, data);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to update document');
    }
  }

  async updateDocumentPermissions(id: string, permissions: string[]): Promise<Document> {
    try {
      const response = await axios.put(`${API_URL}/${id}/permissions`, { permissions });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to update permissions');
    }
  }

  async deleteDocument(id: string): Promise<void> {
    try {
      await axios.delete(`${API_URL}/${id}`);
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to delete document');
    }
  }

  async updateDocumentVersion(id: string, data: DocumentVersion): Promise<{ document: Document; url: string }> {
    try {
      const formData = new FormData();
      formData.append('file', data.file);
      
      if (data.description) {
        formData.append('description', data.description);
      }

      const response = await axios.put(`${API_URL}/${id}/version`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });
      
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to update document version');
    }
  }

  async extractDocumentData(id: string): Promise<{ success: boolean; data: any; documentId: string }> {
    try {
      const response = await axios.post(`${API_URL}/${id}/extract`);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to extract document data');
    }
  }

  // Utility methods
  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  getFileIcon(fileType: string): string {
    if (fileType.includes('pdf')) return '📄';
    if (fileType.includes('word') || fileType.includes('document')) return '📝';
    if (fileType.includes('excel') || fileType.includes('spreadsheet')) return '📊';
    if (fileType.includes('powerpoint') || fileType.includes('presentation')) return '📈';
    if (fileType.startsWith('image/')) return '🖼️';
    if (fileType.startsWith('video/')) return '🎥';
    if (fileType.startsWith('audio/')) return '🎵';
    if (fileType.includes('zip') || fileType.includes('rar')) return '🗜️';
    return '📎';
  }

  getSecurityLevelLabel(level: number): string {
    switch (level) {
      case 1:
        return 'Basic';
      case 2:
        return 'Standard';
      case 3:
        return 'High';
      case 4:
        return 'Maximum';
      default:
        return 'Unknown';
    }
  }

  getSecurityLevelColor(level: number): string {
    switch (level) {
      case 1:
        return 'bg-success-100 text-success-700';
      case 2:
        return 'bg-warning-100 text-warning-700';
      case 3:
        return 'bg-error-100 text-error-700';
      case 4:
        return 'bg-purple-100 text-purple-700';
      default:
        return 'bg-secondary-100 text-secondary-700';
    }
  }

  isImageFile(fileType: string): boolean {
    return fileType.startsWith('image/');
  }

  isPdfFile(fileType: string): boolean {
    return fileType.includes('pdf');
  }

  isDocumentFile(fileType: string): boolean {
    return fileType.includes('word') || 
           fileType.includes('document') || 
           fileType.includes('excel') || 
           fileType.includes('spreadsheet') || 
           fileType.includes('powerpoint') || 
           fileType.includes('presentation');
  }

  canPreview(fileType: string): boolean {
    return this.isImageFile(fileType) || this.isPdfFile(fileType);
  }

  downloadDocument(document: Document): void {
    if (document.url) {
      const link = document.createElement('a');
      link.href = document.url;
      link.download = document.fileName;
      link.target = '_blank';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  }

  formatUploadDate(dateString: string): string {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 1) {
      const diffInMinutes = Math.floor(diffInHours * 60);
      return diffInMinutes < 1 ? 'Just now' : `${diffInMinutes}m ago`;
    } else if (diffInHours < 24) {
      return `${Math.floor(diffInHours)}h ago`;
    } else if (diffInHours < 48) {
      return 'Yesterday';
    } else {
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
    }
  }

  validateFile(file: File): { isValid: boolean; error?: string } {
    const maxSize = 10 * 1024 * 1024; // 10MB
    const allowedTypes = [
      'application/pdf',
      'image/jpeg',
      'image/png',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-powerpoint',
      'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      'text/plain',
      'application/zip',
      'application/x-rar-compressed'
    ];

    if (file.size > maxSize) {
      return {
        isValid: false,
        error: 'File size must be less than 10MB'
      };
    }

    if (!allowedTypes.includes(file.type)) {
      return {
        isValid: false,
        error: 'File type not supported. Please upload PDF, Word, Excel, PowerPoint, images, or text files.'
      };
    }

    return { isValid: true };
  }
}

export const documentService = new DocumentService();
