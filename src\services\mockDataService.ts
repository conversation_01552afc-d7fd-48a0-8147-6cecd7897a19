// Mock Data Service for GlobalConnect
// This service provides realistic mock data for all platform features

export interface MockUser {
  _id: string;
  name: string;
  email: string;
  role: 'client' | 'vendor' | 'admin';
  company?: string;
  location: string;
  bio: string;
  skills?: string[];
  rating?: number;
  website?: string;
  avatar?: string;
  createdAt: string;
}

export interface MockProject {
  _id: string;
  title: string;
  description: string;
  client: MockUser;
  budget: number;
  deadline: string;
  status: 'open' | 'in-progress' | 'review' | 'completed' | 'cancelled';
  category: string;
  requirements: string[];
  skills?: string[];
  bids?: MockBid[];
  views?: number;
  createdAt: string;
  isUrgent?: boolean;
  assignedVendor?: MockUser;
}

export interface MockBid {
  _id: string;
  project: string;
  vendor: MockUser;
  amount: number;
  deliveryTime: number;
  proposal: string;
  status: 'pending' | 'accepted' | 'rejected';
  createdAt: string;
}

export interface MockMessage {
  _id: string;
  sender: MockUser;
  recipient: MockUser;
  project?: MockProject;
  content: string;
  createdAt: string;
  isRead: boolean;
}

export interface MockNotification {
  _id: string;
  user: string;
  title: string;
  message: string;
  type: 'bid' | 'project' | 'message' | 'payment' | 'reminder';
  isRead: boolean;
  createdAt: string;
  relatedProject?: string;
}

// Mock Users
export const mockUsers: MockUser[] = [
  {
    _id: 'admin1',
    name: 'System Administrator',
    email: '<EMAIL>',
    role: 'admin',
    location: 'San Francisco, CA, USA',
    bio: 'Platform administrator with full system access.',
    avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?auto=format&fit=crop&w=400&q=80',
    createdAt: '2024-01-01T00:00:00Z'
  },
  {
    _id: 'client1',
    name: 'Sarah Johnson',
    email: '<EMAIL>',
    role: 'client',
    company: 'TechCorp Inc.',
    location: 'New York, NY, USA',
    bio: 'CEO of TechCorp Inc., leading digital transformation initiatives.',
    website: 'https://techcorp.com',
    avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?auto=format&fit=crop&w=400&q=80',
    createdAt: '2024-01-02T00:00:00Z'
  },
  {
    _id: 'client2',
    name: 'Michael Chen',
    email: '<EMAIL>',
    role: 'client',
    company: 'Global Marketing Solutions',
    location: 'London, UK',
    bio: 'Marketing director focused on global brand expansion.',
    website: 'https://gms.com',
    avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?auto=format&fit=crop&w=400&q=80',
    createdAt: '2024-01-03T00:00:00Z'
  },
  {
    _id: 'client3',
    name: 'Emily Rodriguez',
    email: '<EMAIL>',
    role: 'client',
    company: 'StartupXYZ',
    location: 'Austin, TX, USA',
    bio: 'Startup founder building the next generation of fintech solutions.',
    website: 'https://startupxyz.com',
    avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?auto=format&fit=crop&w=400&q=80',
    createdAt: '2024-01-04T00:00:00Z'
  },
  {
    _id: 'vendor1',
    name: 'David Kim',
    email: '<EMAIL>',
    role: 'vendor',
    company: 'DK Development',
    location: 'Seoul, South Korea',
    bio: 'Full-stack developer with 8+ years of experience in React, Node.js, and cloud technologies.',
    skills: ['React', 'Node.js', 'Python', 'AWS', 'MongoDB', 'TypeScript'],
    rating: 4.9,
    website: 'https://dkdev.com',
    avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?auto=format&fit=crop&w=400&q=80',
    createdAt: '2024-01-05T00:00:00Z'
  },
  {
    _id: 'vendor2',
    name: 'Lisa Wang',
    email: '<EMAIL>',
    role: 'vendor',
    company: 'Wang Design Studio',
    location: 'Toronto, Canada',
    bio: 'UI/UX designer specializing in modern, user-centered design solutions.',
    skills: ['Figma', 'Adobe Creative Suite', 'Prototyping', 'User Research', 'Wireframing'],
    rating: 4.8,
    website: 'https://wangdesign.com',
    avatar: 'https://images.unsplash.com/photo-1517841905240-472988babdf9?auto=format&fit=crop&w=400&q=80',
    createdAt: '2024-01-06T00:00:00Z'
  },
  {
    _id: 'vendor3',
    name: 'Carlos Martinez',
    email: '<EMAIL>',
    role: 'vendor',
    company: 'Martinez Digital',
    location: 'Barcelona, Spain',
    bio: 'Digital marketing expert with proven track record in SEO, PPC, and social media.',
    skills: ['SEO', 'PPC', 'Social Media Marketing', 'Google Analytics', 'Content Strategy'],
    rating: 4.7,
    website: 'https://martinezdigital.com',
    avatar: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?auto=format&fit=crop&w=400&q=80',
    createdAt: '2024-01-07T00:00:00Z'
  },
  {
    _id: 'vendor4',
    name: 'Priya Sharma',
    email: '<EMAIL>',
    role: 'vendor',
    company: 'Sharma Mobile Solutions',
    location: 'Mumbai, India',
    bio: 'Mobile app developer specializing in cross-platform solutions.',
    skills: ['React Native', 'Flutter', 'iOS', 'Android', 'Firebase', 'API Integration'],
    rating: 4.9,
    website: 'https://sharmamobile.com',
    avatar: 'https://images.unsplash.com/photo-1534528741775-53994a69daeb?auto=format&fit=crop&w=400&q=80',
    createdAt: '2024-01-08T00:00:00Z'
  },
  {
    _id: 'vendor5',
    name: 'James Wilson',
    email: '<EMAIL>',
    role: 'vendor',
    company: 'Wilson Content',
    location: 'Sydney, Australia',
    bio: 'Professional content writer and copywriter with expertise in technical documentation.',
    skills: ['Technical Writing', 'Copywriting', 'SEO Writing', 'Content Strategy', 'Editing'],
    rating: 4.6,
    website: 'https://wilsoncontent.com',
    avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?auto=format&fit=crop&w=400&q=80',
    createdAt: '2024-01-09T00:00:00Z'
  }
];

// Mock Projects
export const mockProjects: MockProject[] = [
  {
    _id: 'project1',
    title: 'E-commerce Platform Development',
    description: 'Build a modern, scalable e-commerce platform with React frontend and Node.js backend. Must include payment integration, inventory management, and admin dashboard.',
    client: mockUsers.find(u => u._id === 'client1')!,
    budget: 25000,
    deadline: '2024-03-15T00:00:00Z',
    status: 'open',
    category: 'web-development',
    requirements: ['React', 'Node.js', 'MongoDB', 'Payment Integration', 'AWS'],
    views: 156,
    createdAt: '2024-01-15T00:00:00Z',
    isUrgent: false
  },
  {
    _id: 'project2',
    title: 'Mobile Banking App',
    description: 'Develop a secure mobile banking application with biometric authentication, transaction history, and real-time notifications.',
    client: mockUsers.find(u => u._id === 'client2')!,
    budget: 35000,
    deadline: '2024-04-20T00:00:00Z',
    status: 'in-progress',
    category: 'mobile-development',
    requirements: ['React Native', 'Security', 'Banking APIs', 'Biometric Auth'],
    assignedVendor: mockUsers.find(u => u._id === 'vendor4')!,
    views: 89,
    createdAt: '2024-01-10T00:00:00Z',
    isUrgent: true
  },
  {
    _id: 'project3',
    title: 'Brand Identity Design',
    description: 'Create a complete brand identity package including logo, color palette, typography, and brand guidelines for a fintech startup.',
    client: mockUsers.find(u => u._id === 'client3')!,
    budget: 8500,
    deadline: '2024-02-28T00:00:00Z',
    status: 'open',
    category: 'design',
    requirements: ['Logo Design', 'Brand Guidelines', 'Adobe Creative Suite', 'Typography'],
    views: 67,
    createdAt: '2024-01-12T00:00:00Z',
    isUrgent: true
  },
  {
    _id: 'project4',
    title: 'SEO Optimization & Content Strategy',
    description: 'Comprehensive SEO audit and optimization for corporate website, including content strategy and link building campaign.',
    client: mockUsers.find(u => u._id === 'client1')!,
    budget: 12000,
    deadline: '2024-03-30T00:00:00Z',
    status: 'open',
    category: 'marketing',
    requirements: ['SEO', 'Content Strategy', 'Google Analytics', 'Link Building'],
    views: 134,
    createdAt: '2024-01-18T00:00:00Z',
    isUrgent: false
  },
  {
    _id: 'project5',
    title: 'Corporate Website Redesign',
    description: 'Complete redesign of corporate website with modern UI/UX and responsive design.',
    client: mockUsers.find(u => u._id === 'client1')!,
    budget: 15000,
    deadline: '2024-01-10T00:00:00Z',
    status: 'completed',
    category: 'web-development',
    requirements: ['React', 'UI/UX Design', 'Responsive Design'],
    assignedVendor: mockUsers.find(u => u._id === 'vendor1')!,
    views: 245,
    createdAt: '2023-12-01T00:00:00Z',
    isUrgent: false
  }
];

// Mock Bids
export const mockBids: MockBid[] = [
  {
    _id: 'bid1',
    project: 'project1',
    vendor: mockUsers.find(u => u._id === 'vendor1')!,
    amount: 23000,
    deliveryTime: 56,
    proposal: 'I will build a modern e-commerce platform using React and Node.js with the latest best practices. The solution will include a responsive frontend, secure backend API, payment integration with Stripe, and comprehensive admin dashboard.',
    status: 'pending',
    createdAt: '2024-01-16T00:00:00Z'
  },
  {
    _id: 'bid2',
    project: 'project1',
    vendor: mockUsers.find(u => u._id === 'vendor4')!,
    amount: 24500,
    deliveryTime: 70,
    proposal: 'Full-stack e-commerce solution with mobile-first approach. I will deliver a scalable platform with React frontend, Node.js backend, and mobile app for better customer engagement.',
    status: 'pending',
    createdAt: '2024-01-17T00:00:00Z'
  },
  {
    _id: 'bid3',
    project: 'project3',
    vendor: mockUsers.find(u => u._id === 'vendor2')!,
    amount: 7800,
    deliveryTime: 21,
    proposal: 'Complete brand identity package with multiple logo concepts, comprehensive brand guidelines, and marketing material templates. I will provide 3 initial concepts and unlimited revisions.',
    status: 'pending',
    createdAt: '2024-01-13T00:00:00Z'
  }
];

// Mock Messages
export const mockMessages: MockMessage[] = [
  {
    _id: 'msg1',
    sender: mockUsers.find(u => u._id === 'client1')!,
    recipient: mockUsers.find(u => u._id === 'vendor1')!,
    content: 'Hi David, I reviewed your bid for the e-commerce platform. Your proposal looks comprehensive. Can we discuss the timeline in more detail?',
    createdAt: '2024-01-20T10:30:00Z',
    isRead: true
  },
  {
    _id: 'msg2',
    sender: mockUsers.find(u => u._id === 'vendor1')!,
    recipient: mockUsers.find(u => u._id === 'client1')!,
    content: 'Hello Sarah, thank you for considering my proposal. I can definitely adjust the timeline based on your priorities. Would you like to schedule a call to discuss the project phases?',
    createdAt: '2024-01-20T11:15:00Z',
    isRead: true
  },
  {
    _id: 'msg3',
    sender: mockUsers.find(u => u._id === 'client2')!,
    recipient: mockUsers.find(u => u._id === 'vendor4')!,
    content: 'Hi Priya, the mobile banking app is looking great! I tested the latest build and the biometric authentication works perfectly.',
    createdAt: '2024-01-21T14:20:00Z',
    isRead: true
  }
];

// Mock Notifications
export const mockNotifications: MockNotification[] = [
  {
    _id: 'notif1',
    user: 'client1',
    title: 'New Bid Received',
    message: 'David Kim submitted a bid for your E-commerce Platform Development project',
    type: 'bid',
    isRead: false,
    createdAt: '2024-01-20T09:00:00Z',
    relatedProject: 'project1'
  },
  {
    _id: 'notif2',
    user: 'client2',
    title: 'Project Milestone Completed',
    message: 'Mobile Banking App has reached 35% completion',
    type: 'project',
    isRead: true,
    createdAt: '2024-01-19T16:30:00Z',
    relatedProject: 'project2'
  },
  {
    _id: 'notif3',
    user: 'vendor1',
    title: 'New Message',
    message: 'Sarah Johnson sent you a message about E-commerce Platform Development',
    type: 'message',
    isRead: false,
    createdAt: '2024-01-20T10:35:00Z',
    relatedProject: 'project1'
  }
];

// Mock Data Service Class
export class MockDataService {
  static getUsers(): MockUser[] {
    return mockUsers;
  }

  static getProjects(): MockProject[] {
    return mockProjects.map(project => ({
      ...project,
      bids: mockBids.filter(bid => bid.project === project._id)
    }));
  }

  static getBids(): MockBid[] {
    return mockBids;
  }

  static getMessages(): MockMessage[] {
    return mockMessages;
  }

  static getNotifications(): MockNotification[] {
    return mockNotifications;
  }

  static getUserById(id: string): MockUser | undefined {
    return mockUsers.find(user => user._id === id);
  }

  static getProjectById(id: string): MockProject | undefined {
    const project = mockProjects.find(project => project._id === id);
    if (project) {
      return {
        ...project,
        bids: mockBids.filter(bid => bid.project === project._id)
      };
    }
    return undefined;
  }

  static getProjectsByUser(userId: string, role: 'client' | 'vendor'): MockProject[] {
    if (role === 'client') {
      return this.getProjects().filter(project => project.client._id === userId);
    } else {
      return this.getProjects().filter(project => 
        project.assignedVendor?._id === userId || 
        mockBids.some(bid => bid.vendor._id === userId && bid.project === project._id)
      );
    }
  }

  static getBidsByProject(projectId: string): MockBid[] {
    return mockBids.filter(bid => bid.project === projectId);
  }

  static getBidsByVendor(vendorId: string): MockBid[] {
    return mockBids.filter(bid => bid.vendor._id === vendorId);
  }

  static getMessagesByUser(userId: string): MockMessage[] {
    return mockMessages.filter(msg => 
      msg.sender._id === userId || msg.recipient._id === userId
    );
  }

  static getNotificationsByUser(userId: string): MockNotification[] {
    return mockNotifications.filter(notif => notif.user === userId);
  }
}
