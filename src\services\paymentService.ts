// Mock Payment Service - Frontend Only
export interface PaymentMethod {
  id: string;
  type: 'card' | 'paypal' | 'bank';
  last4?: string;
  brand?: string;
  expiryMonth?: number;
  expiryYear?: number;
  isDefault: boolean;
  email?: string; // for PayPal
  bankName?: string; // for bank transfers
}

export interface Transaction {
  id: string;
  amount: number;
  currency: string;
  status: 'pending' | 'completed' | 'failed' | 'refunded';
  type: 'payment' | 'refund' | 'withdrawal';
  description: string;
  projectId?: string;
  projectTitle?: string;
  date: string;
  paymentMethod: PaymentMethod;
  fees: number;
}

export interface PaymentIntent {
  id: string;
  amount: number;
  currency: string;
  status: 'requires_payment_method' | 'requires_confirmation' | 'succeeded' | 'canceled';
  clientSecret: string;
}

export interface Subscription {
  id: string;
  plan: 'basic' | 'pro' | 'enterprise';
  status: 'active' | 'canceled' | 'past_due';
  currentPeriodStart: string;
  currentPeriodEnd: string;
  amount: number;
  currency: string;
}

class PaymentService {
  // Mock payment methods
  private mockPaymentMethods: PaymentMethod[] = [
    {
      id: 'pm_1',
      type: 'card',
      last4: '4242',
      brand: 'visa',
      expiryMonth: 12,
      expiryYear: 2025,
      isDefault: true
    },
    {
      id: 'pm_2',
      type: 'paypal',
      email: '<EMAIL>',
      isDefault: false
    }
  ];

  private mockTransactions: Transaction[] = [
    {
      id: 'txn_1',
      amount: 5000,
      currency: 'USD',
      status: 'completed',
      type: 'payment',
      description: 'Payment for E-commerce Platform project',
      projectId: 'proj_1',
      projectTitle: 'E-commerce Platform',
      date: '2024-01-15T10:30:00Z',
      paymentMethod: this.mockPaymentMethods[0],
      fees: 150
    },
    {
      id: 'txn_2',
      amount: 3500,
      currency: 'USD',
      status: 'pending',
      type: 'withdrawal',
      description: 'Withdrawal to bank account',
      date: '2024-01-14T15:45:00Z',
      paymentMethod: { id: 'bank_1', type: 'bank', bankName: 'Chase Bank', isDefault: false },
      fees: 25
    }
  ];

  // Payment Methods
  async getPaymentMethods(): Promise<PaymentMethod[]> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));
    return this.mockPaymentMethods;
  }

  async addPaymentMethod(data: Partial<PaymentMethod>): Promise<PaymentMethod> {
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const newMethod: PaymentMethod = {
      id: `pm_${Date.now()}`,
      type: data.type || 'card',
      last4: data.last4 || '1234',
      brand: data.brand || 'visa',
      expiryMonth: data.expiryMonth || 12,
      expiryYear: data.expiryYear || 2025,
      isDefault: data.isDefault || false,
      email: data.email,
      bankName: data.bankName
    };

    this.mockPaymentMethods.push(newMethod);
    return newMethod;
  }

  async removePaymentMethod(id: string): Promise<void> {
    await new Promise(resolve => setTimeout(resolve, 500));
    this.mockPaymentMethods = this.mockPaymentMethods.filter(pm => pm.id !== id);
  }

  async setDefaultPaymentMethod(id: string): Promise<void> {
    await new Promise(resolve => setTimeout(resolve, 500));
    this.mockPaymentMethods.forEach(pm => {
      pm.isDefault = pm.id === id;
    });
  }

  // Transactions
  async getTransactions(page: number = 1, limit: number = 20): Promise<{
    transactions: Transaction[];
    total: number;
    currentPage: number;
    totalPages: number;
  }> {
    await new Promise(resolve => setTimeout(resolve, 500));
    
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedTransactions = this.mockTransactions.slice(startIndex, endIndex);

    return {
      transactions: paginatedTransactions,
      total: this.mockTransactions.length,
      currentPage: page,
      totalPages: Math.ceil(this.mockTransactions.length / limit)
    };
  }

  // Payment Processing
  async createPaymentIntent(amount: number, currency: string = 'USD'): Promise<PaymentIntent> {
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    return {
      id: `pi_${Date.now()}`,
      amount,
      currency,
      status: 'requires_payment_method',
      clientSecret: `pi_${Date.now()}_secret_${Math.random().toString(36).substr(2, 9)}`
    };
  }

  async confirmPayment(paymentIntentId: string, paymentMethodId: string): Promise<PaymentIntent> {
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Simulate success/failure
    const success = Math.random() > 0.1; // 90% success rate
    
    return {
      id: paymentIntentId,
      amount: 5000,
      currency: 'USD',
      status: success ? 'succeeded' : 'canceled',
      clientSecret: `${paymentIntentId}_secret_confirmed`
    };
  }

  async processProjectPayment(projectId: string, amount: number, paymentMethodId: string): Promise<Transaction> {
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    const transaction: Transaction = {
      id: `txn_${Date.now()}`,
      amount,
      currency: 'USD',
      status: 'completed',
      type: 'payment',
      description: `Payment for project ${projectId}`,
      projectId,
      projectTitle: 'Project Payment',
      date: new Date().toISOString(),
      paymentMethod: this.mockPaymentMethods.find(pm => pm.id === paymentMethodId) || this.mockPaymentMethods[0],
      fees: Math.round(amount * 0.029) // 2.9% fee
    };

    this.mockTransactions.unshift(transaction);
    return transaction;
  }

  // Withdrawals
  async requestWithdrawal(amount: number, paymentMethodId: string): Promise<Transaction> {
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    const transaction: Transaction = {
      id: `txn_${Date.now()}`,
      amount,
      currency: 'USD',
      status: 'pending',
      type: 'withdrawal',
      description: 'Withdrawal request',
      date: new Date().toISOString(),
      paymentMethod: this.mockPaymentMethods.find(pm => pm.id === paymentMethodId) || this.mockPaymentMethods[0],
      fees: 25 // Flat withdrawal fee
    };

    this.mockTransactions.unshift(transaction);
    return transaction;
  }

  // Subscriptions
  async getSubscription(): Promise<Subscription | null> {
    await new Promise(resolve => setTimeout(resolve, 500));
    
    return {
      id: 'sub_1',
      plan: 'pro',
      status: 'active',
      currentPeriodStart: '2024-01-01T00:00:00Z',
      currentPeriodEnd: '2024-02-01T00:00:00Z',
      amount: 2900, // $29.00
      currency: 'USD'
    };
  }

  async updateSubscription(plan: 'basic' | 'pro' | 'enterprise'): Promise<Subscription> {
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const amounts = { basic: 900, pro: 2900, enterprise: 9900 };
    
    return {
      id: 'sub_1',
      plan,
      status: 'active',
      currentPeriodStart: new Date().toISOString(),
      currentPeriodEnd: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
      amount: amounts[plan],
      currency: 'USD'
    };
  }

  async cancelSubscription(): Promise<void> {
    await new Promise(resolve => setTimeout(resolve, 1000));
    // Mock cancellation
  }

  // Utility methods
  formatAmount(amount: number, currency: string = 'USD'): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency,
      minimumFractionDigits: 2
    }).format(amount / 100);
  }

  getPaymentMethodDisplay(method: PaymentMethod): string {
    switch (method.type) {
      case 'card':
        return `**** **** **** ${method.last4}`;
      case 'paypal':
        return method.email || 'PayPal Account';
      case 'bank':
        return method.bankName || 'Bank Account';
      default:
        return 'Payment Method';
    }
  }

  getStatusColor(status: string): string {
    const colors = {
      completed: 'text-success-600',
      pending: 'text-warning-600',
      failed: 'text-error-600',
      refunded: 'text-blue-600',
      active: 'text-success-600',
      canceled: 'text-error-600',
      past_due: 'text-warning-600'
    };
    return colors[status as keyof typeof colors] || 'text-secondary-600';
  }

  getStatusBadgeColor(status: string): string {
    const colors = {
      completed: 'bg-success-100 text-success-700',
      pending: 'bg-warning-100 text-warning-700',
      failed: 'bg-error-100 text-error-700',
      refunded: 'bg-blue-100 text-blue-700',
      active: 'bg-success-100 text-success-700',
      canceled: 'bg-error-100 text-error-700',
      past_due: 'bg-warning-100 text-warning-700'
    };
    return colors[status as keyof typeof colors] || 'bg-secondary-100 text-secondary-700';
  }
}

export const paymentService = new PaymentService();
