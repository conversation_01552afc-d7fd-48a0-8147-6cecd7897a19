import axios from 'axios';
import { MockDataService, MockProject } from './mockDataService';

const API_URL = 'http://localhost:5000/api/projects';
const USE_MOCK_DATA = import.meta.env.VITE_USE_MOCK_DATA === 'true' || false; // Default to false to use real backend

export interface Project {
  _id: string;
  title: string;
  description: string;
  client: {
    _id: string;
    name: string;
    email: string;
    company?: string;
    companyLogo?: string;
  };
  budget: number;
  deadline: string;
  status: 'open' | 'in-progress' | 'completed' | 'cancelled';
  category: 'web-development' | 'mobile-development' | 'design' | 'content' | 'marketing' | 'data-analytics' | 'other';
  skills: string[];
  attachments: Array<{
    name: string;
    url: string;
    type: string;
  }>;
  visibility: 'public' | 'invite-only' | 'private';
  assignedVendor?: {
    _id: string;
    name: string;
    email: string;
    company?: string;
    companyLogo?: string;
  };
  winningBid?: any;
  bids?: any[];
  documents?: any[];
  mlPriceRecommendation?: number;
  priceOptimizationApplied?: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface CreateProjectData {
  title: string;
  description: string;
  budget: number;
  deadline: string;
  category: string;
  skills: string[];
  attachments?: Array<{
    name: string;
    url: string;
    type: string;
  }>;
  visibility?: 'public' | 'invite-only' | 'private';
}

export interface ProjectFilters {
  status?: string;
  category?: string;
  skills?: string;
  minBudget?: number;
  maxBudget?: number;
  page?: number;
  limit?: number;
}

export interface ProjectsResponse {
  projects: Project[];
  page: number;
  pages: number;
  total: number;
}

class ProjectService {
  async createProject(data: CreateProjectData): Promise<Project> {
    try {
      const response = await axios.post(API_URL, data);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to create project');
    }
  }

  async getProjects(filters: ProjectFilters = {}): Promise<ProjectsResponse> {
    if (USE_MOCK_DATA) {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 500));

      let projects = MockDataService.getProjects() as Project[];

      // Apply filters
      if (filters.status) {
        projects = projects.filter(p => p.status === filters.status);
      }
      if (filters.category) {
        projects = projects.filter(p => p.category === filters.category);
      }
      if (filters.skills) {
        const skillsArray = filters.skills.split(',').map(s => s.trim().toLowerCase());
        projects = projects.filter(p =>
          p.skills?.some(skill => skillsArray.includes(skill.toLowerCase())) ||
          p.requirements?.some(req => skillsArray.includes(req.toLowerCase()))
        );
      }
      if (filters.minBudget) {
        projects = projects.filter(p => p.budget >= filters.minBudget!);
      }
      if (filters.maxBudget) {
        projects = projects.filter(p => p.budget <= filters.maxBudget!);
      }

      const page = filters.page || 1;
      const limit = filters.limit || 10;
      const startIndex = (page - 1) * limit;
      const endIndex = startIndex + limit;

      return {
        projects: projects.slice(startIndex, endIndex),
        page,
        pages: Math.ceil(projects.length / limit),
        total: projects.length
      };
    }

    try {
      const params = new URLSearchParams();

      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          params.append(key, value.toString());
        }
      });

      const response = await axios.get(`${API_URL}?${params.toString()}`);
      return response.data;
    } catch (error: any) {
      console.error('Error fetching projects:', error);
      // Fallback to mock data
      return this.getProjects(filters);
    }
  }

  async getProjectById(id: string): Promise<Project> {
    if (USE_MOCK_DATA) {
      await new Promise(resolve => setTimeout(resolve, 300));
      const project = MockDataService.getProjectById(id);
      if (!project) {
        throw new Error('Project not found');
      }
      return project as Project;
    }

    try {
      const response = await axios.get(`${API_URL}/${id}`);
      return response.data;
    } catch (error: any) {
      console.error('Error fetching project:', error);
      // Fallback to mock data
      const project = MockDataService.getProjectById(id);
      if (!project) {
        throw new Error('Project not found');
      }
      return project as Project;
    }
  }

  async updateProject(id: string, data: Partial<CreateProjectData>): Promise<Project> {
    try {
      const response = await axios.put(`${API_URL}/${id}`, data);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to update project');
    }
  }

  async deleteProject(id: string): Promise<void> {
    try {
      await axios.delete(`${API_URL}/${id}`);
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to delete project');
    }
  }

  // Helper methods for categories and skills
  getCategories(): Array<{ value: string; label: string }> {
    return [
      { value: 'web-development', label: 'Web Development' },
      { value: 'mobile-development', label: 'Mobile Development' },
      { value: 'design', label: 'Design' },
      { value: 'content', label: 'Content' },
      { value: 'marketing', label: 'Marketing' },
      { value: 'data-analytics', label: 'Data Analytics' },
      { value: 'other', label: 'Other' }
    ];
  }

  getCommonSkills(): string[] {
    return [
      'JavaScript',
      'React',
      'Node.js',
      'Python',
      'Java',
      'PHP',
      'HTML/CSS',
      'TypeScript',
      'Vue.js',
      'Angular',
      'React Native',
      'Flutter',
      'Swift',
      'Kotlin',
      'UI/UX Design',
      'Graphic Design',
      'Photoshop',
      'Figma',
      'Adobe Illustrator',
      'Content Writing',
      'Copywriting',
      'SEO',
      'Social Media Marketing',
      'Google Ads',
      'Facebook Ads',
      'Data Analysis',
      'Machine Learning',
      'SQL',
      'MongoDB',
      'PostgreSQL',
      'AWS',
      'Docker',
      'DevOps'
    ];
  }

  formatBudget(amount: number): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  }

  formatDeadline(deadline: string): string {
    const date = new Date(deadline);
    const now = new Date();
    const diffTime = date.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays < 0) {
      return 'Overdue';
    } else if (diffDays === 0) {
      return 'Due today';
    } else if (diffDays === 1) {
      return 'Due tomorrow';
    } else if (diffDays <= 7) {
      return `Due in ${diffDays} days`;
    } else {
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
    }
  }

  getStatusColor(status: string): string {
    switch (status) {
      case 'open':
        return 'bg-success-100 text-success-700';
      case 'in-progress':
        return 'bg-warning-100 text-warning-700';
      case 'completed':
        return 'bg-primary-100 text-primary-700';
      case 'cancelled':
        return 'bg-error-100 text-error-700';
      default:
        return 'bg-secondary-100 text-secondary-700';
    }
  }

  getCategoryColor(category: string): string {
    const colors = {
      'web-development': 'bg-blue-100 text-blue-700',
      'mobile-development': 'bg-purple-100 text-purple-700',
      'design': 'bg-pink-100 text-pink-700',
      'content': 'bg-green-100 text-green-700',
      'marketing': 'bg-orange-100 text-orange-700',
      'data-analytics': 'bg-indigo-100 text-indigo-700',
      'other': 'bg-gray-100 text-gray-700'
    };
    return colors[category as keyof typeof colors] || colors.other;
  }
}

export const projectService = new ProjectService();
