import { describe, it, expect, vi, beforeEach } from 'vitest'
import { screen, waitFor, within } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { renderWithProviders, mockUser, mockVendorUser, mockProject, mockBid } from '../utils'
import App from '../../App'
import { authService } from '../../services/authService'
import { projectService } from '../../services/projectService'
import { bidService } from '../../services/bidService'

// Mock all services
vi.mock('../../services/authService')
vi.mock('../../services/projectService')
vi.mock('../../services/bidService')
vi.mock('../../services/messageService')
vi.mock('../../services/notificationService')

const mockedAuthService = vi.mocked(authService)
const mockedProjectService = vi.mocked(projectService)
const mockedBidService = vi.mocked(bidService)

describe('User Journey E2E Tests', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    localStorage.clear()
    
    // Setup default mocks
    mockedAuthService.isAuthenticated.mockReturnValue(false)
    mockedAuthService.getToken.mockReturnValue(null)
    mockedProjectService.getProjects.mockResolvedValue({
      projects: [mockProject],
      total: 1,
      currentPage: 1,
      totalPages: 1
    })
  })

  describe('Client Journey: Project Creation and Management', () => {
    it('should allow client to register, create project, and manage bids', async () => {
      const user = userEvent.setup()

      // Mock successful registration
      mockedAuthService.register.mockResolvedValue({
        user: mockUser,
        token: 'mock-token'
      })
      mockedAuthService.getCurrentUser.mockResolvedValue(mockUser)
      mockedProjectService.createProject.mockResolvedValue(mockProject)
      mockedBidService.getProjectBids.mockResolvedValue([mockBid])

      renderWithProviders(<App />)

      // Step 1: User lands on homepage
      expect(screen.getByText(/connect with global talent/i)).toBeInTheDocument()

      // Step 2: User clicks "Get Started" to register
      await user.click(screen.getByText(/get started/i))

      // Step 3: Fill registration form
      await user.type(screen.getByLabelText(/full name/i), 'John Doe')
      await user.type(screen.getByLabelText(/email/i), '<EMAIL>')
      await user.type(screen.getByLabelText(/password/i), 'password123')
      await user.type(screen.getByLabelText(/confirm password/i), 'password123')
      await user.selectOptions(screen.getByLabelText(/role/i), 'client')

      // Step 4: Submit registration
      await user.click(screen.getByRole('button', { name: /create account/i }))

      // Wait for registration to complete and redirect to dashboard
      await waitFor(() => {
        expect(mockedAuthService.register).toHaveBeenCalled()
      })

      // Mock authenticated state
      mockedAuthService.isAuthenticated.mockReturnValue(true)
      mockedAuthService.getToken.mockReturnValue('mock-token')

      // Step 5: Navigate to create project
      await user.click(screen.getByText(/post project/i))

      // Step 6: Fill project creation form
      await user.type(screen.getByLabelText(/project title/i), 'Test Project')
      await user.type(screen.getByLabelText(/description/i), 'A test project description')
      await user.selectOptions(screen.getByLabelText(/category/i), 'web-development')
      await user.type(screen.getByLabelText(/budget/i), '5000')
      await user.type(screen.getByLabelText(/deadline/i), '2024-12-31')

      // Step 7: Submit project
      await user.click(screen.getByRole('button', { name: /post project/i }))

      await waitFor(() => {
        expect(mockedProjectService.createProject).toHaveBeenCalledWith({
          title: 'Test Project',
          description: 'A test project description',
          category: 'web-development',
          budget: 5000,
          deadline: '2024-12-31',
          requirements: []
        })
      })

      // Step 8: View project and manage bids
      await user.click(screen.getByText(/view bids/i))

      await waitFor(() => {
        expect(mockedBidService.getProjectBids).toHaveBeenCalled()
      })

      // Should see bid management interface
      expect(screen.getByText(/received bids/i)).toBeInTheDocument()
    })
  })

  describe('Vendor Journey: Finding and Bidding on Projects', () => {
    it('should allow vendor to register, browse projects, and submit bids', async () => {
      const user = userEvent.setup()

      // Mock successful registration and login
      mockedAuthService.register.mockResolvedValue({
        user: mockVendorUser,
        token: 'vendor-token'
      })
      mockedAuthService.getCurrentUser.mockResolvedValue(mockVendorUser)
      mockedBidService.submitBid.mockResolvedValue(mockBid)

      renderWithProviders(<App />)

      // Step 1: Register as vendor
      await user.click(screen.getByText(/get started/i))
      await user.type(screen.getByLabelText(/full name/i), 'Jane Smith')
      await user.type(screen.getByLabelText(/email/i), '<EMAIL>')
      await user.type(screen.getByLabelText(/password/i), 'password123')
      await user.type(screen.getByLabelText(/confirm password/i), 'password123')
      await user.selectOptions(screen.getByLabelText(/role/i), 'vendor')

      await user.click(screen.getByRole('button', { name: /create account/i }))

      await waitFor(() => {
        expect(mockedAuthService.register).toHaveBeenCalled()
      })

      // Mock authenticated state
      mockedAuthService.isAuthenticated.mockReturnValue(true)
      mockedAuthService.getToken.mockReturnValue('vendor-token')

      // Step 2: Browse projects
      await user.click(screen.getByText(/browse projects/i))

      await waitFor(() => {
        expect(mockedProjectService.getProjects).toHaveBeenCalled()
      })

      // Step 3: View project details
      await user.click(screen.getByText(/view details/i))

      // Step 4: Submit bid
      await user.click(screen.getByText(/submit bid/i))

      // Fill bid form
      await user.type(screen.getByLabelText(/bid amount/i), '4500')
      await user.type(screen.getByLabelText(/timeline/i), '30')
      await user.type(screen.getByLabelText(/proposal/i), 'I can complete this project efficiently')

      await user.click(screen.getByRole('button', { name: /submit bid/i }))

      await waitFor(() => {
        expect(mockedBidService.submitBid).toHaveBeenCalledWith({
          projectId: mockProject._id,
          amount: 4500,
          timeline: 30,
          proposal: 'I can complete this project efficiently'
        })
      })

      // Should see success message
      expect(screen.getByText(/bid submitted successfully/i)).toBeInTheDocument()
    })
  })

  describe('Project Collaboration Journey', () => {
    it('should handle complete project lifecycle from creation to completion', async () => {
      const user = userEvent.setup()

      // Mock project with assigned vendor
      const assignedProject = {
        ...mockProject,
        status: 'in-progress' as const,
        assignedVendor: mockVendorUser
      }

      mockedAuthService.isAuthenticated.mockReturnValue(true)
      mockedAuthService.getCurrentUser.mockResolvedValue(mockUser)
      mockedProjectService.getProjectById.mockResolvedValue(assignedProject)

      renderWithProviders(<App />)

      // Navigate to project
      await user.click(screen.getByText(/my projects/i))
      await user.click(screen.getByText(assignedProject.title))

      await waitFor(() => {
        expect(mockedProjectService.getProjectById).toHaveBeenCalled()
      })

      // Should see project collaboration interface
      expect(screen.getByText(/in progress/i)).toBeInTheDocument()
      expect(screen.getByText(mockVendorUser.name)).toBeInTheDocument()

      // Test messaging functionality
      await user.click(screen.getByText(/send message/i))
      await user.type(screen.getByLabelText(/message/i), 'How is the progress?')
      await user.click(screen.getByRole('button', { name: /send/i }))

      // Test document upload
      await user.click(screen.getByText(/upload document/i))
      
      const file = new File(['test content'], 'test.pdf', { type: 'application/pdf' })
      const fileInput = screen.getByLabelText(/select file/i)
      await user.upload(fileInput, file)

      await user.type(screen.getByLabelText(/document title/i), 'Project Requirements')
      await user.click(screen.getByRole('button', { name: /upload/i }))

      // Test project completion
      await user.click(screen.getByText(/mark complete/i))
      await user.click(screen.getByRole('button', { name: /confirm/i }))
    })
  })

  describe('Error Handling and Edge Cases', () => {
    it('should handle network errors gracefully', async () => {
      const user = userEvent.setup()

      // Mock network error
      mockedAuthService.login.mockRejectedValue(new Error('Network error'))

      renderWithProviders(<App />)

      await user.click(screen.getByText(/sign in/i))
      await user.type(screen.getByLabelText(/email/i), '<EMAIL>')
      await user.type(screen.getByLabelText(/password/i), 'password123')
      await user.click(screen.getByRole('button', { name: /sign in/i }))

      await waitFor(() => {
        expect(screen.getByText(/network error/i)).toBeInTheDocument()
      })

      // Should allow retry
      expect(screen.getByRole('button', { name: /try again/i })).toBeInTheDocument()
    })

    it('should handle validation errors', async () => {
      const user = userEvent.setup()

      renderWithProviders(<App />)

      // Try to submit empty form
      await user.click(screen.getByText(/get started/i))
      await user.click(screen.getByRole('button', { name: /create account/i }))

      // Should show validation errors
      expect(screen.getByText(/name is required/i)).toBeInTheDocument()
      expect(screen.getByText(/email is required/i)).toBeInTheDocument()
      expect(screen.getByText(/password is required/i)).toBeInTheDocument()
    })

    it('should handle unauthorized access', async () => {
      const user = userEvent.setup()

      // Mock unauthorized error
      mockedAuthService.getCurrentUser.mockRejectedValue({
        response: { status: 401, data: { message: 'Unauthorized' } }
      })

      renderWithProviders(<App />)

      // Try to access protected route
      await user.click(screen.getByText(/dashboard/i))

      await waitFor(() => {
        expect(screen.getByText(/please sign in/i)).toBeInTheDocument()
      })
    })
  })

  describe('Responsive Design and Accessibility', () => {
    it('should work on mobile devices', async () => {
      const user = userEvent.setup()

      // Mock mobile viewport
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 375,
      })

      renderWithProviders(<App />)

      // Should show mobile menu button
      expect(screen.getByLabelText(/menu/i)).toBeInTheDocument()

      // Open mobile menu
      await user.click(screen.getByLabelText(/menu/i))

      // Should show navigation items
      expect(screen.getByText(/browse projects/i)).toBeInTheDocument()
    })

    it('should support keyboard navigation', async () => {
      const user = userEvent.setup()

      renderWithProviders(<App />)

      // Tab through navigation
      await user.tab()
      expect(screen.getByText(/browse projects/i)).toHaveFocus()

      await user.tab()
      expect(screen.getByText(/how it works/i)).toHaveFocus()

      // Should be able to activate with Enter
      await user.keyboard('{Enter}')
    })

    it('should have proper ARIA labels and roles', () => {
      renderWithProviders(<App />)

      // Check main navigation
      expect(screen.getByRole('navigation')).toBeInTheDocument()

      // Check main content
      expect(screen.getByRole('main')).toBeInTheDocument()

      // Check headings hierarchy
      expect(screen.getByRole('heading', { level: 1 })).toBeInTheDocument()
    })
  })
})
