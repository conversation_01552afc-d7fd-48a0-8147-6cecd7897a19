import '@testing-library/jest-dom'
import { vi } from 'vitest'

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
}
global.localStorage = localStorageMock

// Mock sessionStorage
const sessionStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
}
global.sessionStorage = sessionStorageMock

// Mock window.matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(), // deprecated
    removeListener: vi.fn(), // deprecated
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
})

// Mock IntersectionObserver
global.IntersectionObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}))

// Mock ResizeObserver
global.ResizeObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}))

// Mock fetch
global.fetch = vi.fn()

// Mock console methods to reduce noise in tests
global.console = {
  ...console,
  warn: vi.fn(),
  error: vi.fn(),
}

// Mock URL.createObjectURL
global.URL.createObjectURL = vi.fn(() => 'mocked-url')
global.URL.revokeObjectURL = vi.fn()

// Mock File and FileReader
global.File = class MockFile {
  constructor(public chunks: any[], public name: string, public options?: any) {}
  get size() { return this.chunks.reduce((acc, chunk) => acc + chunk.length, 0) }
  get type() { return this.options?.type || '' }
}

global.FileReader = class MockFileReader {
  result: any = null
  error: any = null
  readyState: number = 0
  onload: any = null
  onerror: any = null
  onabort: any = null
  onloadstart: any = null
  onloadend: any = null
  onprogress: any = null

  readAsDataURL() {
    this.readyState = 2
    this.result = 'data:text/plain;base64,dGVzdA=='
    if (this.onload) this.onload({ target: this })
  }

  readAsText() {
    this.readyState = 2
    this.result = 'test content'
    if (this.onload) this.onload({ target: this })
  }

  abort() {
    this.readyState = 2
    if (this.onabort) this.onabort({ target: this })
  }
}

// Mock Notification API
global.Notification = class MockNotification {
  static permission = 'granted'
  static requestPermission = vi.fn().mockResolvedValue('granted')
  
  constructor(public title: string, public options?: any) {}
  
  close = vi.fn()
  onclick = null
  onclose = null
  onerror = null
  onshow = null
}

// Mock WebSocket
global.WebSocket = class MockWebSocket {
  static CONNECTING = 0
  static OPEN = 1
  static CLOSING = 2
  static CLOSED = 3

  readyState = 1
  url: string
  protocol = ''
  extensions = ''
  bufferedAmount = 0
  binaryType = 'blob'

  onopen: any = null
  onclose: any = null
  onmessage: any = null
  onerror: any = null

  constructor(url: string) {
    this.url = url
    setTimeout(() => {
      if (this.onopen) this.onopen({ target: this })
    }, 0)
  }

  send = vi.fn()
  close = vi.fn()
  addEventListener = vi.fn()
  removeEventListener = vi.fn()
  dispatchEvent = vi.fn()
}
