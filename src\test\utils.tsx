import React, { ReactElement } from 'react'
import { render, RenderOptions } from '@testing-library/react'
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'react-router-dom'
import { vi } from 'vitest'

// Mock user data
export const mockUser = {
  _id: 'user123',
  name: '<PERSON>',
  email: '<EMAIL>',
  role: 'client' as const,
  isVerified: true,
  isActive: true,
  profileImage: 'https://example.com/avatar.jpg',
  createdAt: '2023-01-01T00:00:00.000Z',
  updatedAt: '2023-01-01T00:00:00.000Z'
}

export const mockVendorUser = {
  ...mockUser,
  _id: 'vendor123',
  name: '<PERSON>',
  email: '<EMAIL>',
  role: 'vendor' as const
}

export const mockAdminUser = {
  ...mockUser,
  _id: 'admin123',
  name: 'Admin User',
  email: '<EMAIL>',
  role: 'admin' as const
}

// Mock project data
export const mockProject = {
  _id: 'project123',
  title: 'Test Project',
  description: 'A test project description',
  category: 'web-development' as const,
  budget: 5000,
  deadline: '2024-12-31T00:00:00.000Z',
  status: 'open' as const,
  isUrgent: false,
  client: mockUser,
  assignedVendor: null,
  requirements: ['React', 'TypeScript'],
  skills: ['React', 'TypeScript'],
  attachments: [],
  visibility: 'public' as const,
  createdAt: '2023-01-01T00:00:00.000Z',
  updatedAt: '2023-01-01T00:00:00.000Z'
}

// Mock bid data
export const mockBid = {
  _id: 'bid123',
  project: mockProject._id,
  vendor: mockVendorUser,
  amount: 4500,
  timeline: 30,
  proposal: 'I can complete this project efficiently',
  status: 'pending' as const,
  deliveryTime: 30,
  isCounterOffer: false,
  revisions: 3,
  milestones: [],
  attachments: [],
  createdAt: '2023-01-01T00:00:00.000Z',
  updatedAt: '2023-01-01T00:00:00.000Z'
}

// Mock document data
export const mockDocument = {
  _id: 'doc123',
  title: 'Test Document',
  description: 'A test document',
  project: mockProject._id,
  fileKey: 'documents/test.pdf',
  fileType: 'application/pdf',
  fileName: 'test.pdf',
  fileSize: 1024000,
  uploadedBy: mockUser,
  securityLevel: 2,
  metadata: {
    uploadedBy: mockUser._id,
    projectId: mockProject._id,
    timestamp: '2023-01-01T00:00:00.000Z',
    userDefined: {}
  },
  permissions: [mockUser._id],
  versions: [],
  isDeleted: false,
  createdAt: '2023-01-01T00:00:00.000Z',
  updatedAt: '2023-01-01T00:00:00.000Z',
  url: 'https://example.com/test.pdf'
}

// Mock notification data
export const mockNotification = {
  _id: 'notif123',
  recipient: mockUser._id,
  type: 'message' as const,
  title: 'New Message',
  content: 'You have a new message',
  isRead: false,
  reference: {
    model: 'Message',
    id: 'msg123'
  },
  metadata: {
    conversationId: 'conv123',
    senderId: mockVendorUser._id
  },
  expiresAt: '2024-01-01T00:00:00.000Z',
  createdAt: '2023-01-01T00:00:00.000Z',
  updatedAt: '2023-01-01T00:00:00.000Z'
}

// Mock API responses
export const mockApiResponse = <T,>(data: T, delay = 0) => {
  return new Promise<T>((resolve) => {
    setTimeout(() => resolve(data), delay)
  })
}

export const mockApiError = (message: string, status = 400, delay = 0) => {
  return new Promise((_, reject) => {
    setTimeout(() => {
      const error = new Error(message) as any
      error.response = { data: { message }, status }
      reject(error)
    }, delay)
  })
}

// Custom render function with providers
interface CustomRenderOptions extends Omit<RenderOptions, 'wrapper'> {
  initialEntries?: string[]
}

const AllTheProviders = ({ children, initialEntries = ['/'] }: { children: React.ReactNode, initialEntries?: string[] }) => {
  return (
    <BrowserRouter>
      {children}
    </BrowserRouter>
  )
}

export const renderWithProviders = (
  ui: ReactElement,
  options: CustomRenderOptions = {}
) => {
  const { initialEntries, ...renderOptions } = options
  
  return render(ui, {
    wrapper: ({ children }) => (
      <AllTheProviders initialEntries={initialEntries}>
        {children}
      </AllTheProviders>
    ),
    ...renderOptions,
  })
}

// Mock service functions
export const createMockService = <T extends Record<string, any>>(methods: Partial<T>): T => {
  const mockService = {} as T

  Object.keys(methods).forEach(key => {
    mockService[key as keyof T] = vi.fn().mockImplementation(methods[key as keyof typeof methods])
  })

  return mockService
}

// File upload helpers
export const createMockFile = (name: string, size: number, type: string) => {
  const file = new File(['test content'], name, { type })
  Object.defineProperty(file, 'size', { value: size })
  return file
}

// Form helpers
export const fillForm = async (user: any, form: HTMLFormElement, data: Record<string, string>) => {
  for (const [name, value] of Object.entries(data)) {
    const field = form.querySelector(`[name="${name}"]`) as HTMLInputElement
    if (field) {
      await user.clear(field)
      await user.type(field, value)
    }
  }
}

// Wait helpers
export const waitForLoadingToFinish = async () => {
  const { findByText } = await import('@testing-library/react')
  try {
    await findByText(/loading/i, {}, { timeout: 100 })
    await findByText(/loading/i, {}, { timeout: 5000 }).then(() => {
      throw new Error('Still loading')
    })
  } catch {
    // Loading finished
  }
}

// Local storage helpers
export const mockLocalStorage = () => {
  const store: Record<string, string> = {}
  
  return {
    getItem: vi.fn((key: string) => store[key] || null),
    setItem: vi.fn((key: string, value: string) => {
      store[key] = value
    }),
    removeItem: vi.fn((key: string) => {
      delete store[key]
    }),
    clear: vi.fn(() => {
      Object.keys(store).forEach(key => delete store[key])
    })
  }
}

// Network helpers
export const mockFetch = (response: any, ok = true, status = 200) => {
  return vi.fn().mockResolvedValue({
    ok,
    status,
    json: vi.fn().mockResolvedValue(response),
    text: vi.fn().mockResolvedValue(JSON.stringify(response))
  })
}

// Re-export everything from testing-library
export * from '@testing-library/react'
export { default as userEvent } from '@testing-library/user-event'
